/*! For license information please see index.js.LICENSE.txt */
(my["webpackJsonp"]=my["webpackJsonp"]||[]).push([[47],Array(19).concat([function(t,e,n){"use strict";(function(t){var r=n(334),i=n(335),o=n(226);function a(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"===typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(t){return!1}}function s(){return u.TYPED_ARRAY_SUPPORT?**********:**********}function l(t,e){if(s()<e)throw new RangeError("Invalid typed array length");return u.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(e),t.__proto__=u.prototype):(null===t&&(t=new u(e)),t.length=e),t}function u(t,e,n){if(!u.TYPED_ARRAY_SUPPORT&&!(this instanceof u))return new u(t,e,n);if("number"===typeof t){if("string"===typeof e)throw new Error("If encoding is specified then the first argument must be a string");return d(this,t)}return h(this,t,e,n)}function h(t,e,n,r){if("number"===typeof e)throw new TypeError('"value" argument must not be a number');return"undefined"!==typeof ArrayBuffer&&e instanceof ArrayBuffer?_(t,e,n,r):"string"===typeof e?p(t,e,n):y(t,e)}function f(t){if("number"!==typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function c(t,e,n,r){return f(e),e<=0?l(t,e):void 0!==n?"string"===typeof r?l(t,e).fill(n,r):l(t,e).fill(n):l(t,e)}function d(t,e){if(f(e),t=l(t,e<0?0:0|b(e)),!u.TYPED_ARRAY_SUPPORT)for(var n=0;n<e;++n)t[n]=0;return t}function p(t,e,n){if("string"===typeof n&&""!==n||(n="utf8"),!u.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var r=0|m(e,n);t=l(t,r);var i=t.write(e,n);return i!==r&&(t=t.slice(0,i)),t}function g(t,e){var n=e.length<0?0:0|b(e.length);t=l(t,n);for(var r=0;r<n;r+=1)t[r]=255&e[r];return t}function _(t,e,n,r){if(e.byteLength,n<0||e.byteLength<n)throw new RangeError("'offset' is out of bounds");if(e.byteLength<n+(r||0))throw new RangeError("'length' is out of bounds");return e=void 0===n&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,n):new Uint8Array(e,n,r),u.TYPED_ARRAY_SUPPORT?(t=e,t.__proto__=u.prototype):t=g(t,e),t}function y(t,e){if(u.isBuffer(e)){var n=0|b(e.length);return t=l(t,n),0===t.length?t:(e.copy(t,0,0,n),t)}if(e){if("undefined"!==typeof ArrayBuffer&&e.buffer instanceof ArrayBuffer||"length"in e)return"number"!==typeof e.length||et(e.length)?l(t,0):g(t,e);if("Buffer"===e.type&&o(e.data))return g(t,e.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function b(t){if(t>=s())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+s().toString(16)+" bytes");return 0|t}function w(t){return+t!=t&&(t=0),u.alloc(+t)}function m(t,e){if(u.isBuffer(t))return t.length;if("undefined"!==typeof ArrayBuffer&&"function"===typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!==typeof t&&(t=""+t);var n=t.length;if(0===n)return 0;for(var r=!1;;)switch(e){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return $(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return Q(t).length;default:if(r)return $(t).length;e=(""+e).toLowerCase(),r=!0}}function v(t,e,n){var r=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if(n>>>=0,e>>>=0,n<=e)return"";t||(t="utf8");while(1)switch(t){case"hex":return M(this,e,n);case"utf8":case"utf-8":return L(this,e,n);case"ascii":return B(this,e,n);case"latin1":case"binary":return N(this,e,n);case"base64":return P(this,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return U(this,e,n);default:if(r)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),r=!0}}function E(t,e,n){var r=t[e];t[e]=t[n],t[n]=r}function k(t,e,n,r,i){if(0===t.length)return-1;if("string"===typeof n?(r=n,n=0):n>**********?n=**********:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=i?0:t.length-1),n<0&&(n=t.length+n),n>=t.length){if(i)return-1;n=t.length-1}else if(n<0){if(!i)return-1;n=0}if("string"===typeof e&&(e=u.from(e,r)),u.isBuffer(e))return 0===e.length?-1:x(t,e,n,r,i);if("number"===typeof e)return e&=255,u.TYPED_ARRAY_SUPPORT&&"function"===typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,e,n):Uint8Array.prototype.lastIndexOf.call(t,e,n):x(t,[e],n,r,i);throw new TypeError("val must be string, number or Buffer")}function x(t,e,n,r,i){var o,a=1,s=t.length,l=e.length;if(void 0!==r&&(r=String(r).toLowerCase(),"ucs2"===r||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(t.length<2||e.length<2)return-1;a=2,s/=2,l/=2,n/=2}function u(t,e){return 1===a?t[e]:t.readUInt16BE(e*a)}if(i){var h=-1;for(o=n;o<s;o++)if(u(t,o)===u(e,-1===h?0:o-h)){if(-1===h&&(h=o),o-h+1===l)return h*a}else-1!==h&&(o-=o-h),h=-1}else for(n+l>s&&(n=s-l),o=n;o>=0;o--){for(var f=!0,c=0;c<l;c++)if(u(t,o+c)!==u(e,c)){f=!1;break}if(f)return o}return-1}function A(t,e,n,r){n=Number(n)||0;var i=t.length-n;r?(r=Number(r),r>i&&(r=i)):r=i;var o=e.length;if(o%2!==0)throw new TypeError("Invalid hex string");r>o/2&&(r=o/2);for(var a=0;a<r;++a){var s=parseInt(e.substr(2*a,2),16);if(isNaN(s))return a;t[n+a]=s}return a}function T(t,e,n,r){return tt($(e,t.length-n),t,n,r)}function O(t,e,n,r){return tt(J(e),t,n,r)}function S(t,e,n,r){return O(t,e,n,r)}function R(t,e,n,r){return tt(Q(e),t,n,r)}function I(t,e,n,r){return tt(X(e,t.length-n),t,n,r)}function P(t,e,n){return 0===e&&n===t.length?r.fromByteArray(t):r.fromByteArray(t.slice(e,n))}function L(t,e,n){n=Math.min(t.length,n);var r=[],i=e;while(i<n){var o,a,s,l,u=t[i],h=null,f=u>239?4:u>223?3:u>191?2:1;if(i+f<=n)switch(f){case 1:u<128&&(h=u);break;case 2:o=t[i+1],128===(192&o)&&(l=(31&u)<<6|63&o,l>127&&(h=l));break;case 3:o=t[i+1],a=t[i+2],128===(192&o)&&128===(192&a)&&(l=(15&u)<<12|(63&o)<<6|63&a,l>2047&&(l<55296||l>57343)&&(h=l));break;case 4:o=t[i+1],a=t[i+2],s=t[i+3],128===(192&o)&&128===(192&a)&&128===(192&s)&&(l=(15&u)<<18|(63&o)<<12|(63&a)<<6|63&s,l>65535&&l<1114112&&(h=l))}null===h?(h=65533,f=1):h>65535&&(h-=65536,r.push(h>>>10&1023|55296),h=56320|1023&h),r.push(h),i+=f}return j(r)}e.Buffer=u,e.SlowBuffer=w,e.INSPECT_MAX_BYTES=50,u.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:a(),e.kMaxLength=s(),u.poolSize=8192,u._augment=function(t){return t.__proto__=u.prototype,t},u.from=function(t,e,n){return h(null,t,e,n)},u.TYPED_ARRAY_SUPPORT&&(u.prototype.__proto__=Uint8Array.prototype,u.__proto__=Uint8Array,"undefined"!==typeof Symbol&&Symbol.species&&u[Symbol.species]===u&&Object.defineProperty(u,Symbol.species,{value:null,configurable:!0})),u.alloc=function(t,e,n){return c(null,t,e,n)},u.allocUnsafe=function(t){return d(null,t)},u.allocUnsafeSlow=function(t){return d(null,t)},u.isBuffer=function(t){return!(null==t||!t._isBuffer)},u.compare=function(t,e){if(!u.isBuffer(t)||!u.isBuffer(e))throw new TypeError("Arguments must be Buffers");if(t===e)return 0;for(var n=t.length,r=e.length,i=0,o=Math.min(n,r);i<o;++i)if(t[i]!==e[i]){n=t[i],r=e[i];break}return n<r?-1:r<n?1:0},u.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},u.concat=function(t,e){if(!o(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return u.alloc(0);var n;if(void 0===e)for(e=0,n=0;n<t.length;++n)e+=t[n].length;var r=u.allocUnsafe(e),i=0;for(n=0;n<t.length;++n){var a=t[n];if(!u.isBuffer(a))throw new TypeError('"list" argument must be an Array of Buffers');a.copy(r,i),i+=a.length}return r},u.byteLength=m,u.prototype._isBuffer=!0,u.prototype.swap16=function(){var t=this.length;if(t%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)E(this,e,e+1);return this},u.prototype.swap32=function(){var t=this.length;if(t%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)E(this,e,e+3),E(this,e+1,e+2);return this},u.prototype.swap64=function(){var t=this.length;if(t%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)E(this,e,e+7),E(this,e+1,e+6),E(this,e+2,e+5),E(this,e+3,e+4);return this},u.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?L(this,0,t):v.apply(this,arguments)},u.prototype.equals=function(t){if(!u.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===u.compare(this,t)},u.prototype.inspect=function(){var t="",n=e.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n&&(t+=" ... ")),"<Buffer "+t+">"},u.prototype.compare=function(t,e,n,r,i){if(!u.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===e&&(e=0),void 0===n&&(n=t?t.length:0),void 0===r&&(r=0),void 0===i&&(i=this.length),e<0||n>t.length||r<0||i>this.length)throw new RangeError("out of range index");if(r>=i&&e>=n)return 0;if(r>=i)return-1;if(e>=n)return 1;if(e>>>=0,n>>>=0,r>>>=0,i>>>=0,this===t)return 0;for(var o=i-r,a=n-e,s=Math.min(o,a),l=this.slice(r,i),h=t.slice(e,n),f=0;f<s;++f)if(l[f]!==h[f]){o=l[f],a=h[f];break}return o<a?-1:a<o?1:0},u.prototype.includes=function(t,e,n){return-1!==this.indexOf(t,e,n)},u.prototype.indexOf=function(t,e,n){return k(this,t,e,n,!0)},u.prototype.lastIndexOf=function(t,e,n){return k(this,t,e,n,!1)},u.prototype.write=function(t,e,n,r){if(void 0===e)r="utf8",n=this.length,e=0;else if(void 0===n&&"string"===typeof e)r=e,n=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e|=0,isFinite(n)?(n|=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var i=this.length-e;if((void 0===n||n>i)&&(n=i),t.length>0&&(n<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var o=!1;;)switch(r){case"hex":return A(this,t,e,n);case"utf8":case"utf-8":return T(this,t,e,n);case"ascii":return O(this,t,e,n);case"latin1":case"binary":return S(this,t,e,n);case"base64":return R(this,t,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return I(this,t,e,n);default:if(o)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),o=!0}},u.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var C=4096;function j(t){var e=t.length;if(e<=C)return String.fromCharCode.apply(String,t);var n="",r=0;while(r<e)n+=String.fromCharCode.apply(String,t.slice(r,r+=C));return n}function B(t,e,n){var r="";n=Math.min(t.length,n);for(var i=e;i<n;++i)r+=String.fromCharCode(127&t[i]);return r}function N(t,e,n){var r="";n=Math.min(t.length,n);for(var i=e;i<n;++i)r+=String.fromCharCode(t[i]);return r}function M(t,e,n){var r=t.length;(!e||e<0)&&(e=0),(!n||n<0||n>r)&&(n=r);for(var i="",o=e;o<n;++o)i+=K(t[o]);return i}function U(t,e,n){for(var r=t.slice(e,n),i="",o=0;o<r.length;o+=2)i+=String.fromCharCode(r[o]+256*r[o+1]);return i}function D(t,e,n){if(t%1!==0||t<0)throw new RangeError("offset is not uint");if(t+e>n)throw new RangeError("Trying to access beyond buffer length")}function F(t,e,n,r,i,o){if(!u.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw new RangeError('"value" argument is out of bounds');if(n+r>t.length)throw new RangeError("Index out of range")}function z(t,e,n,r){e<0&&(e=65535+e+1);for(var i=0,o=Math.min(t.length-n,2);i<o;++i)t[n+i]=(e&255<<8*(r?i:1-i))>>>8*(r?i:1-i)}function Z(t,e,n,r){e<0&&(e=4294967295+e+1);for(var i=0,o=Math.min(t.length-n,4);i<o;++i)t[n+i]=e>>>8*(r?i:3-i)&255}function Y(t,e,n,r,i,o){if(n+r>t.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function H(t,e,n,r,o){return o||Y(t,e,n,4,34028234663852886e22,-34028234663852886e22),i.write(t,e,n,r,23,4),n+4}function W(t,e,n,r,o){return o||Y(t,e,n,8,17976931348623157e292,-17976931348623157e292),i.write(t,e,n,r,52,8),n+8}u.prototype.slice=function(t,e){var n,r=this.length;if(t=~~t,e=void 0===e?r:~~e,t<0?(t+=r,t<0&&(t=0)):t>r&&(t=r),e<0?(e+=r,e<0&&(e=0)):e>r&&(e=r),e<t&&(e=t),u.TYPED_ARRAY_SUPPORT)n=this.subarray(t,e),n.__proto__=u.prototype;else{var i=e-t;n=new u(i,void 0);for(var o=0;o<i;++o)n[o]=this[o+t]}return n},u.prototype.readUIntLE=function(t,e,n){t|=0,e|=0,n||D(t,e,this.length);var r=this[t],i=1,o=0;while(++o<e&&(i*=256))r+=this[t+o]*i;return r},u.prototype.readUIntBE=function(t,e,n){t|=0,e|=0,n||D(t,e,this.length);var r=this[t+--e],i=1;while(e>0&&(i*=256))r+=this[t+--e]*i;return r},u.prototype.readUInt8=function(t,e){return e||D(t,1,this.length),this[t]},u.prototype.readUInt16LE=function(t,e){return e||D(t,2,this.length),this[t]|this[t+1]<<8},u.prototype.readUInt16BE=function(t,e){return e||D(t,2,this.length),this[t]<<8|this[t+1]},u.prototype.readUInt32LE=function(t,e){return e||D(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},u.prototype.readUInt32BE=function(t,e){return e||D(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},u.prototype.readIntLE=function(t,e,n){t|=0,e|=0,n||D(t,e,this.length);var r=this[t],i=1,o=0;while(++o<e&&(i*=256))r+=this[t+o]*i;return i*=128,r>=i&&(r-=Math.pow(2,8*e)),r},u.prototype.readIntBE=function(t,e,n){t|=0,e|=0,n||D(t,e,this.length);var r=e,i=1,o=this[t+--r];while(r>0&&(i*=256))o+=this[t+--r]*i;return i*=128,o>=i&&(o-=Math.pow(2,8*e)),o},u.prototype.readInt8=function(t,e){return e||D(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},u.prototype.readInt16LE=function(t,e){e||D(t,2,this.length);var n=this[t]|this[t+1]<<8;return 32768&n?4294901760|n:n},u.prototype.readInt16BE=function(t,e){e||D(t,2,this.length);var n=this[t+1]|this[t]<<8;return 32768&n?4294901760|n:n},u.prototype.readInt32LE=function(t,e){return e||D(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},u.prototype.readInt32BE=function(t,e){return e||D(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},u.prototype.readFloatLE=function(t,e){return e||D(t,4,this.length),i.read(this,t,!0,23,4)},u.prototype.readFloatBE=function(t,e){return e||D(t,4,this.length),i.read(this,t,!1,23,4)},u.prototype.readDoubleLE=function(t,e){return e||D(t,8,this.length),i.read(this,t,!0,52,8)},u.prototype.readDoubleBE=function(t,e){return e||D(t,8,this.length),i.read(this,t,!1,52,8)},u.prototype.writeUIntLE=function(t,e,n,r){if(t=+t,e|=0,n|=0,!r){var i=Math.pow(2,8*n)-1;F(this,t,e,n,i,0)}var o=1,a=0;this[e]=255&t;while(++a<n&&(o*=256))this[e+a]=t/o&255;return e+n},u.prototype.writeUIntBE=function(t,e,n,r){if(t=+t,e|=0,n|=0,!r){var i=Math.pow(2,8*n)-1;F(this,t,e,n,i,0)}var o=n-1,a=1;this[e+o]=255&t;while(--o>=0&&(a*=256))this[e+o]=t/a&255;return e+n},u.prototype.writeUInt8=function(t,e,n){return t=+t,e|=0,n||F(this,t,e,1,255,0),u.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[e]=255&t,e+1},u.prototype.writeUInt16LE=function(t,e,n){return t=+t,e|=0,n||F(this,t,e,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):z(this,t,e,!0),e+2},u.prototype.writeUInt16BE=function(t,e,n){return t=+t,e|=0,n||F(this,t,e,2,65535,0),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):z(this,t,e,!1),e+2},u.prototype.writeUInt32LE=function(t,e,n){return t=+t,e|=0,n||F(this,t,e,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t):Z(this,t,e,!0),e+4},u.prototype.writeUInt32BE=function(t,e,n){return t=+t,e|=0,n||F(this,t,e,4,4294967295,0),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):Z(this,t,e,!1),e+4},u.prototype.writeIntLE=function(t,e,n,r){if(t=+t,e|=0,!r){var i=Math.pow(2,8*n-1);F(this,t,e,n,i-1,-i)}var o=0,a=1,s=0;this[e]=255&t;while(++o<n&&(a*=256))t<0&&0===s&&0!==this[e+o-1]&&(s=1),this[e+o]=(t/a>>0)-s&255;return e+n},u.prototype.writeIntBE=function(t,e,n,r){if(t=+t,e|=0,!r){var i=Math.pow(2,8*n-1);F(this,t,e,n,i-1,-i)}var o=n-1,a=1,s=0;this[e+o]=255&t;while(--o>=0&&(a*=256))t<0&&0===s&&0!==this[e+o+1]&&(s=1),this[e+o]=(t/a>>0)-s&255;return e+n},u.prototype.writeInt8=function(t,e,n){return t=+t,e|=0,n||F(this,t,e,1,127,-128),u.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[e]=255&t,e+1},u.prototype.writeInt16LE=function(t,e,n){return t=+t,e|=0,n||F(this,t,e,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):z(this,t,e,!0),e+2},u.prototype.writeInt16BE=function(t,e,n){return t=+t,e|=0,n||F(this,t,e,2,32767,-32768),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):z(this,t,e,!1),e+2},u.prototype.writeInt32LE=function(t,e,n){return t=+t,e|=0,n||F(this,t,e,4,**********,-2147483648),u.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24):Z(this,t,e,!0),e+4},u.prototype.writeInt32BE=function(t,e,n){return t=+t,e|=0,n||F(this,t,e,4,**********,-2147483648),t<0&&(t=4294967295+t+1),u.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):Z(this,t,e,!1),e+4},u.prototype.writeFloatLE=function(t,e,n){return H(this,t,e,!0,n)},u.prototype.writeFloatBE=function(t,e,n){return H(this,t,e,!1,n)},u.prototype.writeDoubleLE=function(t,e,n){return W(this,t,e,!0,n)},u.prototype.writeDoubleBE=function(t,e,n){return W(this,t,e,!1,n)},u.prototype.copy=function(t,e,n,r){if(n||(n=0),r||0===r||(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-n&&(r=t.length-e+n);var i,o=r-n;if(this===t&&n<e&&e<r)for(i=o-1;i>=0;--i)t[i+e]=this[i+n];else if(o<1e3||!u.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)t[i+e]=this[i+n];else Uint8Array.prototype.set.call(t,this.subarray(n,n+o),e);return o},u.prototype.fill=function(t,e,n,r){if("string"===typeof t){if("string"===typeof e?(r=e,e=0,n=this.length):"string"===typeof n&&(r=n,n=this.length),1===t.length){var i=t.charCodeAt(0);i<256&&(t=i)}if(void 0!==r&&"string"!==typeof r)throw new TypeError("encoding must be a string");if("string"===typeof r&&!u.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"===typeof t&&(t&=255);if(e<0||this.length<e||this.length<n)throw new RangeError("Out of range index");if(n<=e)return this;var o;if(e>>>=0,n=void 0===n?this.length:n>>>0,t||(t=0),"number"===typeof t)for(o=e;o<n;++o)this[o]=t;else{var a=u.isBuffer(t)?t:$(new u(t,r).toString()),s=a.length;for(o=0;o<n-e;++o)this[o+e]=a[o%s]}return this};var q=/[^+\/0-9A-Za-z-_]/g;function G(t){if(t=V(t).replace(q,""),t.length<2)return"";while(t.length%4!==0)t+="=";return t}function V(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}function K(t){return t<16?"0"+t.toString(16):t.toString(16)}function $(t,e){var n;e=e||1/0;for(var r=t.length,i=null,o=[],a=0;a<r;++a){if(n=t.charCodeAt(a),n>55295&&n<57344){if(!i){if(n>56319){(e-=3)>-1&&o.push(239,191,189);continue}if(a+1===r){(e-=3)>-1&&o.push(239,191,189);continue}i=n;continue}if(n<56320){(e-=3)>-1&&o.push(239,191,189),i=n;continue}n=65536+(i-55296<<10|n-56320)}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,n<128){if((e-=1)<0)break;o.push(n)}else if(n<2048){if((e-=2)<0)break;o.push(n>>6|192,63&n|128)}else if(n<65536){if((e-=3)<0)break;o.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return o}function J(t){for(var e=[],n=0;n<t.length;++n)e.push(255&t.charCodeAt(n));return e}function X(t,e){for(var n,r,i,o=[],a=0;a<t.length;++a){if((e-=2)<0)break;n=t.charCodeAt(a),r=n>>8,i=n%256,o.push(i),o.push(r)}return o}function Q(t){return r.toByteArray(G(t))}function tt(t,e,n,r){for(var i=0;i<r;++i){if(i+n>=e.length||i>=t.length)break;e[i+n]=t[i]}return i}function et(t){return t!==t}}).call(this,n(29))},,,,,,,,,,,function(t,e,n){(function(t){var r=Object.getOwnPropertyDescriptors||function(t){for(var e=Object.keys(t),n={},r=0;r<e.length;r++)n[e[r]]=Object.getOwnPropertyDescriptor(t,e[r]);return n},i=/%[sdj%]/g;e.format=function(t){if(!E(t)){for(var e=[],n=0;n<arguments.length;n++)e.push(s(arguments[n]));return e.join(" ")}n=1;for(var r=arguments,o=r.length,a=String(t).replace(i,(function(t){if("%%"===t)return"%";if(n>=o)return t;switch(t){case"%s":return String(r[n++]);case"%d":return Number(r[n++]);case"%j":try{return JSON.stringify(r[n++])}catch(t){return"[Circular]"}default:return t}})),l=r[n];n<o;l=r[++n])w(l)||!T(l)?a+=" "+l:a+=" "+s(l);return a},e.deprecate=function(n,r){if("undefined"!==typeof t&&!0===t.noDeprecation)return n;if("undefined"===typeof t)return function(){return e.deprecate(n,r).apply(this,arguments)};var i=!1;function o(){if(!i){if(t.throwDeprecation)throw new Error(r);t.traceDeprecation?console.trace(r):console.error(r),i=!0}return n.apply(this,arguments)}return o};var o,a={};function s(t,n){var r={seen:[],stylize:u};return arguments.length>=3&&(r.depth=arguments[2]),arguments.length>=4&&(r.colors=arguments[3]),b(n)?r.showHidden=n:n&&e._extend(r,n),x(r.showHidden)&&(r.showHidden=!1),x(r.depth)&&(r.depth=2),x(r.colors)&&(r.colors=!1),x(r.customInspect)&&(r.customInspect=!0),r.colors&&(r.stylize=l),f(r,t,r.depth)}function l(t,e){var n=s.styles[e];return n?"\x1b["+s.colors[n][0]+"m"+t+"\x1b["+s.colors[n][1]+"m":t}function u(t,e){return t}function h(t){var e={};return t.forEach((function(t,n){e[t]=!0})),e}function f(t,n,r){if(t.customInspect&&n&&R(n.inspect)&&n.inspect!==e.inspect&&(!n.constructor||n.constructor.prototype!==n)){var i=n.inspect(r,t);return E(i)||(i=f(t,i,r)),i}var o=c(t,n);if(o)return o;var a=Object.keys(n),s=h(a);if(t.showHidden&&(a=Object.getOwnPropertyNames(n)),S(n)&&(a.indexOf("message")>=0||a.indexOf("description")>=0))return d(n);if(0===a.length){if(R(n)){var l=n.name?": "+n.name:"";return t.stylize("[Function"+l+"]","special")}if(A(n))return t.stylize(RegExp.prototype.toString.call(n),"regexp");if(O(n))return t.stylize(Date.prototype.toString.call(n),"date");if(S(n))return d(n)}var u,b="",w=!1,m=["{","}"];if(y(n)&&(w=!0,m=["[","]"]),R(n)){var v=n.name?": "+n.name:"";b=" [Function"+v+"]"}return A(n)&&(b=" "+RegExp.prototype.toString.call(n)),O(n)&&(b=" "+Date.prototype.toUTCString.call(n)),S(n)&&(b=" "+d(n)),0!==a.length||w&&0!=n.length?r<0?A(n)?t.stylize(RegExp.prototype.toString.call(n),"regexp"):t.stylize("[Object]","special"):(t.seen.push(n),u=w?p(t,n,r,s,a):a.map((function(e){return g(t,n,r,s,e,w)})),t.seen.pop(),_(u,b,m)):m[0]+b+m[1]}function c(t,e){if(x(e))return t.stylize("undefined","undefined");if(E(e)){var n="'"+JSON.stringify(e).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return t.stylize(n,"string")}return v(e)?t.stylize(""+e,"number"):b(e)?t.stylize(""+e,"boolean"):w(e)?t.stylize("null","null"):void 0}function d(t){return"["+Error.prototype.toString.call(t)+"]"}function p(t,e,n,r,i){for(var o=[],a=0,s=e.length;a<s;++a)B(e,String(a))?o.push(g(t,e,n,r,String(a),!0)):o.push("");return i.forEach((function(i){i.match(/^\d+$/)||o.push(g(t,e,n,r,i,!0))})),o}function g(t,e,n,r,i,o){var a,s,l;if(l=Object.getOwnPropertyDescriptor(e,i)||{value:e[i]},l.get?s=l.set?t.stylize("[Getter/Setter]","special"):t.stylize("[Getter]","special"):l.set&&(s=t.stylize("[Setter]","special")),B(r,i)||(a="["+i+"]"),s||(t.seen.indexOf(l.value)<0?(s=w(n)?f(t,l.value,null):f(t,l.value,n-1),s.indexOf("\n")>-1&&(s=o?s.split("\n").map((function(t){return"  "+t})).join("\n").substr(2):"\n"+s.split("\n").map((function(t){return"   "+t})).join("\n"))):s=t.stylize("[Circular]","special")),x(a)){if(o&&i.match(/^\d+$/))return s;a=JSON.stringify(""+i),a.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(a=a.substr(1,a.length-2),a=t.stylize(a,"name")):(a=a.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),a=t.stylize(a,"string"))}return a+": "+s}function _(t,e,n){var r=t.reduce((function(t,e){return e.indexOf("\n")>=0&&0,t+e.replace(/\u001b\[\d\d?m/g,"").length+1}),0);return r>60?n[0]+(""===e?"":e+"\n ")+" "+t.join(",\n  ")+" "+n[1]:n[0]+e+" "+t.join(", ")+" "+n[1]}function y(t){return Array.isArray(t)}function b(t){return"boolean"===typeof t}function w(t){return null===t}function m(t){return null==t}function v(t){return"number"===typeof t}function E(t){return"string"===typeof t}function k(t){return"symbol"===typeof t}function x(t){return void 0===t}function A(t){return T(t)&&"[object RegExp]"===P(t)}function T(t){return"object"===typeof t&&null!==t}function O(t){return T(t)&&"[object Date]"===P(t)}function S(t){return T(t)&&("[object Error]"===P(t)||t instanceof Error)}function R(t){return"function"===typeof t}function I(t){return null===t||"boolean"===typeof t||"number"===typeof t||"string"===typeof t||"symbol"===typeof t||"undefined"===typeof t}function P(t){return Object.prototype.toString.call(t)}function L(t){return t<10?"0"+t.toString(10):t.toString(10)}e.debuglog=function(n){if(x(o)&&(o=t.env.NODE_DEBUG||""),n=n.toUpperCase(),!a[n])if(new RegExp("\\b"+n+"\\b","i").test(o)){var r=t.pid;a[n]=function(){var t=e.format.apply(e,arguments);console.error("%s %d: %s",n,r,t)}}else a[n]=function(){};return a[n]},e.inspect=s,s.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},s.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},e.isArray=y,e.isBoolean=b,e.isNull=w,e.isNullOrUndefined=m,e.isNumber=v,e.isString=E,e.isSymbol=k,e.isUndefined=x,e.isRegExp=A,e.isObject=T,e.isDate=O,e.isError=S,e.isFunction=R,e.isPrimitive=I,e.isBuffer=n(337);var C=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function j(){var t=new Date,e=[L(t.getHours()),L(t.getMinutes()),L(t.getSeconds())].join(":");return[t.getDate(),C[t.getMonth()],e].join(" ")}function B(t,e){return Object.prototype.hasOwnProperty.call(t,e)}e.log=function(){console.log("%s - %s",j(),e.format.apply(e,arguments))},e.inherits=n(338),e._extend=function(t,e){if(!e||!T(e))return t;var n=Object.keys(e),r=n.length;while(r--)t[n[r]]=e[n[r]];return t};var N="undefined"!==typeof Symbol?Symbol("util.promisify.custom"):void 0;function M(t,e){if(!t){var n=new Error("Promise was rejected with a falsy value");n.reason=t,t=n}return e(t)}function U(e){if("function"!==typeof e)throw new TypeError('The "original" argument must be of type Function');function n(){for(var n=[],r=0;r<arguments.length;r++)n.push(arguments[r]);var i=n.pop();if("function"!==typeof i)throw new TypeError("The last argument must be of type Function");var o=this,a=function(){return i.apply(o,arguments)};e.apply(this,n).then((function(e){t.nextTick(a,null,e)}),(function(e){t.nextTick(M,e,a)}))}return Object.setPrototypeOf(n,Object.getPrototypeOf(e)),Object.defineProperties(n,r(e)),n}e.promisify=function(t){if("function"!==typeof t)throw new TypeError('The "original" argument must be of type Function');if(N&&t[N]){var e=t[N];if("function"!==typeof e)throw new TypeError('The "util.promisify.custom" argument must be of type Function');return Object.defineProperty(e,N,{value:e,enumerable:!1,writable:!1,configurable:!0}),e}function e(){for(var e,n,r=new Promise((function(t,r){e=t,n=r})),i=[],o=0;o<arguments.length;o++)i.push(arguments[o]);i.push((function(t,r){t?n(t):e(r)}));try{t.apply(this,i)}catch(t){n(t)}return r}return Object.setPrototypeOf(e,Object.getPrototypeOf(t)),N&&Object.defineProperty(e,N,{value:e,enumerable:!1,writable:!1,configurable:!0}),Object.defineProperties(e,r(t))},e.promisify.custom=N,e.callbackify=U}).call(this,n(26))},,,,,,,,function(t,e){let n;const r=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];e.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return 4*t+17},e.getSymbolTotalCodewords=function(t){return r[t]},e.getBCHDigit=function(t){let e=0;while(0!==t)e++,t>>>=1;return e},e.setToSJISFunction=function(t){if("function"!==typeof t)throw new Error('"toSJISFunc" is not a valid function.');n=t},e.isKanjiModeEnabled=function(){return"undefined"!==typeof n},e.toSJIS=function(t){return n(t)}},function(t,e,n){const r=n(224),i=n(225);function o(t){if("string"!==typeof t)throw new Error("Param is not a string");const n=t.toLowerCase();switch(n){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw new Error("Unknown mode: "+t)}}e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(t,e){if(!t.ccBits)throw new Error("Invalid mode: "+t);if(!r.isValid(e))throw new Error("Invalid version: "+e);return e>=1&&e<10?t.ccBits[0]:e<27?t.ccBits[1]:t.ccBits[2]},e.getBestModeForData=function(t){return i.testNumeric(t)?e.NUMERIC:i.testAlphanumeric(t)?e.ALPHANUMERIC:i.testKanji(t)?e.KANJI:e.BYTE},e.toString=function(t){if(t&&t.id)return t.id;throw new Error("Invalid mode")},e.isValid=function(t){return t&&t.bit&&t.ccBits},e.from=function(t,n){if(e.isValid(t))return t;try{return o(t)}catch(t){return n}}},function(t,e,n){t.exports=o;var r=n(227).EventEmitter,i=n(43);function o(){r.call(this)}i(o,r),o.Readable=n(54),o.Writable=n(345),o.Duplex=n(346),o.Transform=n(347),o.PassThrough=n(348),o.Stream=o,o.prototype.pipe=function(t,e){var n=this;function i(e){t.writable&&!1===t.write(e)&&n.pause&&n.pause()}function o(){n.readable&&n.resume&&n.resume()}n.on("data",i),t.on("drain",o),t._isStdio||e&&!1===e.end||(n.on("end",s),n.on("close",l));var a=!1;function s(){a||(a=!0,t.end())}function l(){a||(a=!0,"function"===typeof t.destroy&&t.destroy())}function u(t){if(h(),0===r.listenerCount(this,"error"))throw t}function h(){n.removeListener("data",i),t.removeListener("drain",o),n.removeListener("end",s),n.removeListener("close",l),n.removeListener("error",u),t.removeListener("error",u),n.removeListener("end",h),n.removeListener("close",h),t.removeListener("close",h)}return n.on("error",u),t.on("error",u),n.on("end",h),n.on("close",h),t.on("close",h),t.emit("pipe",n),t}},,,function(t,e){"function"===typeof Object.create?t.exports=function(t,e){e&&(t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}))}:t.exports=function(t,e){if(e){t.super_=e;var n=function(){};n.prototype=e.prototype,t.prototype=new n,t.prototype.constructor=t}}},function(t,e,n){"use strict";var r=n(55),i=Object.keys||function(t){var e=[];for(var n in t)e.push(n);return e};t.exports=f;var o=Object.create(n(48));o.inherits=n(43);var a=n(228),s=n(71);o.inherits(f,a);for(var l=i(s.prototype),u=0;u<l.length;u++){var h=l[u];f.prototype[h]||(f.prototype[h]=s.prototype[h])}function f(t){if(!(this instanceof f))return new f(t);a.call(this,t),s.call(this,t),t&&!1===t.readable&&(this.readable=!1),t&&!1===t.writable&&(this.writable=!1),this.allowHalfOpen=!0,t&&!1===t.allowHalfOpen&&(this.allowHalfOpen=!1),this.once("end",c)}function c(){this.allowHalfOpen||this._writableState.ended||r.nextTick(d,this)}function d(t){t.end()}Object.defineProperty(f.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(f.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(t){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=t,this._writableState.destroyed=t)}}),f.prototype._destroy=function(t,e){this.push(null),this.end(),r.nextTick(e,t)}},function(t,e,n){"use strict";var r,i=SyntaxError,o=Function,a=TypeError,s=function(t){try{return o('"use strict"; return ('+t+").constructor;")()}catch(t){}},l=Object.getOwnPropertyDescriptor;if(l)try{l({},"")}catch(t){l=null}var u=function(){throw new a},h=l?function(){try{return u}catch(t){try{return l(arguments,"callee").get}catch(t){return u}}}():u,f=n(356)(),c=n(357)(),d=Object.getPrototypeOf||(c?function(t){return t.__proto__}:null),p={},g="undefined"!==typeof Uint8Array&&d?d(Uint8Array):r,_={"%AggregateError%":"undefined"===typeof AggregateError?r:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"===typeof ArrayBuffer?r:ArrayBuffer,"%ArrayIteratorPrototype%":f&&d?d([][Symbol.iterator]()):r,"%AsyncFromSyncIteratorPrototype%":r,"%AsyncFunction%":p,"%AsyncGenerator%":p,"%AsyncGeneratorFunction%":p,"%AsyncIteratorPrototype%":p,"%Atomics%":"undefined"===typeof Atomics?r:Atomics,"%BigInt%":"undefined"===typeof BigInt?r:BigInt,"%BigInt64Array%":"undefined"===typeof BigInt64Array?r:BigInt64Array,"%BigUint64Array%":"undefined"===typeof BigUint64Array?r:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"===typeof DataView?r:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Error,"%eval%":eval,"%EvalError%":EvalError,"%Float32Array%":"undefined"===typeof Float32Array?r:Float32Array,"%Float64Array%":"undefined"===typeof Float64Array?r:Float64Array,"%FinalizationRegistry%":"undefined"===typeof FinalizationRegistry?r:FinalizationRegistry,"%Function%":o,"%GeneratorFunction%":p,"%Int8Array%":"undefined"===typeof Int8Array?r:Int8Array,"%Int16Array%":"undefined"===typeof Int16Array?r:Int16Array,"%Int32Array%":"undefined"===typeof Int32Array?r:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":f&&d?d(d([][Symbol.iterator]())):r,"%JSON%":"object"===typeof JSON?JSON:r,"%Map%":"undefined"===typeof Map?r:Map,"%MapIteratorPrototype%":"undefined"!==typeof Map&&f&&d?d((new Map)[Symbol.iterator]()):r,"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"===typeof Promise?r:Promise,"%Proxy%":"undefined"===typeof Proxy?r:Proxy,"%RangeError%":RangeError,"%ReferenceError%":ReferenceError,"%Reflect%":"undefined"===typeof Reflect?r:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"===typeof Set?r:Set,"%SetIteratorPrototype%":"undefined"!==typeof Set&&f&&d?d((new Set)[Symbol.iterator]()):r,"%SharedArrayBuffer%":"undefined"===typeof SharedArrayBuffer?r:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":f&&d?d(""[Symbol.iterator]()):r,"%Symbol%":f?Symbol:r,"%SyntaxError%":i,"%ThrowTypeError%":h,"%TypedArray%":g,"%TypeError%":a,"%Uint8Array%":"undefined"===typeof Uint8Array?r:Uint8Array,"%Uint8ClampedArray%":"undefined"===typeof Uint8ClampedArray?r:Uint8ClampedArray,"%Uint16Array%":"undefined"===typeof Uint16Array?r:Uint16Array,"%Uint32Array%":"undefined"===typeof Uint32Array?r:Uint32Array,"%URIError%":URIError,"%WeakMap%":"undefined"===typeof WeakMap?r:WeakMap,"%WeakRef%":"undefined"===typeof WeakRef?r:WeakRef,"%WeakSet%":"undefined"===typeof WeakSet?r:WeakSet};if(d)try{null.error}catch(t){var y=d(d(t));_["%Error.prototype%"]=y}var b=function t(e){var n;if("%AsyncFunction%"===e)n=s("async function () {}");else if("%GeneratorFunction%"===e)n=s("function* () {}");else if("%AsyncGeneratorFunction%"===e)n=s("async function* () {}");else if("%AsyncGenerator%"===e){var r=t("%AsyncGeneratorFunction%");r&&(n=r.prototype)}else if("%AsyncIteratorPrototype%"===e){var i=t("%AsyncGenerator%");i&&d&&(n=d(i.prototype))}return _[e]=n,n},w={"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},m=n(73),v=n(359),E=m.call(Function.call,Array.prototype.concat),k=m.call(Function.apply,Array.prototype.splice),x=m.call(Function.call,String.prototype.replace),A=m.call(Function.call,String.prototype.slice),T=m.call(Function.call,RegExp.prototype.exec),O=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,S=/\\(\\)?/g,R=function(t){var e=A(t,0,1),n=A(t,-1);if("%"===e&&"%"!==n)throw new i("invalid intrinsic syntax, expected closing `%`");if("%"===n&&"%"!==e)throw new i("invalid intrinsic syntax, expected opening `%`");var r=[];return x(t,O,(function(t,e,n,i){r[r.length]=n?x(i,S,"$1"):e||t})),r},I=function(t,e){var n,r=t;if(v(w,r)&&(n=w[r],r="%"+n[0]+"%"),v(_,r)){var o=_[r];if(o===p&&(o=b(r)),"undefined"===typeof o&&!e)throw new a("intrinsic "+t+" exists, but is not available. Please file an issue!");return{alias:n,name:r,value:o}}throw new i("intrinsic "+t+" does not exist!")};t.exports=function(t,e){if("string"!==typeof t||0===t.length)throw new a("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!==typeof e)throw new a('"allowMissing" argument must be a boolean');if(null===T(/^%?[^%]*%?$/,t))throw new i("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var n=R(t),r=n.length>0?n[0]:"",o=I("%"+r+"%",e),s=o.name,u=o.value,h=!1,f=o.alias;f&&(r=f[0],k(n,E([0,1],f)));for(var c=1,d=!0;c<n.length;c+=1){var p=n[c],g=A(p,0,1),y=A(p,-1);if(('"'===g||"'"===g||"`"===g||'"'===y||"'"===y||"`"===y)&&g!==y)throw new i("property names with quotes must have matching quotes");if("constructor"!==p&&d||(h=!0),r+="."+p,s="%"+r+"%",v(_,s))u=_[s];else if(null!=u){if(!(p in u)){if(!e)throw new a("base intrinsic for "+t+" exists, but the property is not available.");return}if(l&&c+1>=n.length){var b=l(u,p);d=!!b,u=d&&"get"in b&&!("originalValue"in b.get)?b.get:u[p]}else d=v(u,p),u=u[p];d&&!h&&(_[s]=u)}}return u}},,,function(t,e,n){function r(t){return Array.isArray?Array.isArray(t):"[object Array]"===y(t)}function i(t){return"boolean"===typeof t}function o(t){return null===t}function a(t){return null==t}function s(t){return"number"===typeof t}function l(t){return"string"===typeof t}function u(t){return"symbol"===typeof t}function h(t){return void 0===t}function f(t){return"[object RegExp]"===y(t)}function c(t){return"object"===typeof t&&null!==t}function d(t){return"[object Date]"===y(t)}function p(t){return"[object Error]"===y(t)||t instanceof Error}function g(t){return"function"===typeof t}function _(t){return null===t||"boolean"===typeof t||"number"===typeof t||"string"===typeof t||"symbol"===typeof t||"undefined"===typeof t}function y(t){return Object.prototype.toString.call(t)}e.isArray=r,e.isBoolean=i,e.isNull=o,e.isNullOrUndefined=a,e.isNumber=s,e.isString=l,e.isSymbol=u,e.isUndefined=h,e.isRegExp=f,e.isObject=c,e.isDate=d,e.isError=p,e.isFunction=g,e.isPrimitive=_,e.isBuffer=n(19).Buffer.isBuffer},function(t,e,n){"use strict";(function(t){var r=n(19).Buffer,i=n(40).Transform,o=n(350),a=n(30),s=n(72).ok,l=n(19).kMaxLength,u="Cannot create final Buffer. It would be larger than 0x"+l.toString(16)+" bytes";o.Z_MIN_WINDOWBITS=8,o.Z_MAX_WINDOWBITS=15,o.Z_DEFAULT_WINDOWBITS=15,o.Z_MIN_CHUNK=64,o.Z_MAX_CHUNK=1/0,o.Z_DEFAULT_CHUNK=16384,o.Z_MIN_MEMLEVEL=1,o.Z_MAX_MEMLEVEL=9,o.Z_DEFAULT_MEMLEVEL=8,o.Z_MIN_LEVEL=-1,o.Z_MAX_LEVEL=9,o.Z_DEFAULT_LEVEL=o.Z_DEFAULT_COMPRESSION;for(var h=Object.keys(o),f=0;f<h.length;f++){var c=h[f];c.match(/^Z/)&&Object.defineProperty(e,c,{enumerable:!0,value:o[c],writable:!1})}for(var d={Z_OK:o.Z_OK,Z_STREAM_END:o.Z_STREAM_END,Z_NEED_DICT:o.Z_NEED_DICT,Z_ERRNO:o.Z_ERRNO,Z_STREAM_ERROR:o.Z_STREAM_ERROR,Z_DATA_ERROR:o.Z_DATA_ERROR,Z_MEM_ERROR:o.Z_MEM_ERROR,Z_BUF_ERROR:o.Z_BUF_ERROR,Z_VERSION_ERROR:o.Z_VERSION_ERROR},p=Object.keys(d),g=0;g<p.length;g++){var _=p[g];d[d[_]]=_}function y(t,e,n){var i=[],o=0;function a(){var e;while(null!==(e=t.read()))i.push(e),o+=e.length;t.once("readable",a)}function s(e){t.removeListener("end",h),t.removeListener("readable",a),n(e)}function h(){var e,a=null;o>=l?a=new RangeError(u):e=r.concat(i,o),i=[],t.close(),n(a,e)}t.on("error",s),t.on("end",h),t.end(e),a()}function b(t,e){if("string"===typeof e&&(e=r.from(e)),!r.isBuffer(e))throw new TypeError("Not a string or buffer");var n=t._finishFlushFlag;return t._processChunk(e,n)}function w(t){if(!(this instanceof w))return new w(t);O.call(this,t,o.DEFLATE)}function m(t){if(!(this instanceof m))return new m(t);O.call(this,t,o.INFLATE)}function v(t){if(!(this instanceof v))return new v(t);O.call(this,t,o.GZIP)}function E(t){if(!(this instanceof E))return new E(t);O.call(this,t,o.GUNZIP)}function k(t){if(!(this instanceof k))return new k(t);O.call(this,t,o.DEFLATERAW)}function x(t){if(!(this instanceof x))return new x(t);O.call(this,t,o.INFLATERAW)}function A(t){if(!(this instanceof A))return new A(t);O.call(this,t,o.UNZIP)}function T(t){return t===o.Z_NO_FLUSH||t===o.Z_PARTIAL_FLUSH||t===o.Z_SYNC_FLUSH||t===o.Z_FULL_FLUSH||t===o.Z_FINISH||t===o.Z_BLOCK}function O(t,n){var a=this;if(this._opts=t=t||{},this._chunkSize=t.chunkSize||e.Z_DEFAULT_CHUNK,i.call(this,t),t.flush&&!T(t.flush))throw new Error("Invalid flush flag: "+t.flush);if(t.finishFlush&&!T(t.finishFlush))throw new Error("Invalid flush flag: "+t.finishFlush);if(this._flushFlag=t.flush||o.Z_NO_FLUSH,this._finishFlushFlag="undefined"!==typeof t.finishFlush?t.finishFlush:o.Z_FINISH,t.chunkSize&&(t.chunkSize<e.Z_MIN_CHUNK||t.chunkSize>e.Z_MAX_CHUNK))throw new Error("Invalid chunk size: "+t.chunkSize);if(t.windowBits&&(t.windowBits<e.Z_MIN_WINDOWBITS||t.windowBits>e.Z_MAX_WINDOWBITS))throw new Error("Invalid windowBits: "+t.windowBits);if(t.level&&(t.level<e.Z_MIN_LEVEL||t.level>e.Z_MAX_LEVEL))throw new Error("Invalid compression level: "+t.level);if(t.memLevel&&(t.memLevel<e.Z_MIN_MEMLEVEL||t.memLevel>e.Z_MAX_MEMLEVEL))throw new Error("Invalid memLevel: "+t.memLevel);if(t.strategy&&t.strategy!=e.Z_FILTERED&&t.strategy!=e.Z_HUFFMAN_ONLY&&t.strategy!=e.Z_RLE&&t.strategy!=e.Z_FIXED&&t.strategy!=e.Z_DEFAULT_STRATEGY)throw new Error("Invalid strategy: "+t.strategy);if(t.dictionary&&!r.isBuffer(t.dictionary))throw new Error("Invalid dictionary: it should be a Buffer instance");this._handle=new o.Zlib(n);var s=this;this._hadError=!1,this._handle.onerror=function(t,n){S(s),s._hadError=!0;var r=new Error(t);r.errno=n,r.code=e.codes[n],s.emit("error",r)};var l=e.Z_DEFAULT_COMPRESSION;"number"===typeof t.level&&(l=t.level);var u=e.Z_DEFAULT_STRATEGY;"number"===typeof t.strategy&&(u=t.strategy),this._handle.init(t.windowBits||e.Z_DEFAULT_WINDOWBITS,l,t.memLevel||e.Z_DEFAULT_MEMLEVEL,u,t.dictionary),this._buffer=r.allocUnsafe(this._chunkSize),this._offset=0,this._level=l,this._strategy=u,this.once("end",this.close),Object.defineProperty(this,"_closed",{get:function(){return!a._handle},configurable:!0,enumerable:!0})}function S(e,n){n&&t.nextTick(n),e._handle&&(e._handle.close(),e._handle=null)}function R(t){t.emit("close")}Object.defineProperty(e,"codes",{enumerable:!0,value:Object.freeze(d),writable:!1}),e.Deflate=w,e.Inflate=m,e.Gzip=v,e.Gunzip=E,e.DeflateRaw=k,e.InflateRaw=x,e.Unzip=A,e.createDeflate=function(t){return new w(t)},e.createInflate=function(t){return new m(t)},e.createDeflateRaw=function(t){return new k(t)},e.createInflateRaw=function(t){return new x(t)},e.createGzip=function(t){return new v(t)},e.createGunzip=function(t){return new E(t)},e.createUnzip=function(t){return new A(t)},e.deflate=function(t,e,n){return"function"===typeof e&&(n=e,e={}),y(new w(e),t,n)},e.deflateSync=function(t,e){return b(new w(e),t)},e.gzip=function(t,e,n){return"function"===typeof e&&(n=e,e={}),y(new v(e),t,n)},e.gzipSync=function(t,e){return b(new v(e),t)},e.deflateRaw=function(t,e,n){return"function"===typeof e&&(n=e,e={}),y(new k(e),t,n)},e.deflateRawSync=function(t,e){return b(new k(e),t)},e.unzip=function(t,e,n){return"function"===typeof e&&(n=e,e={}),y(new A(e),t,n)},e.unzipSync=function(t,e){return b(new A(e),t)},e.inflate=function(t,e,n){return"function"===typeof e&&(n=e,e={}),y(new m(e),t,n)},e.inflateSync=function(t,e){return b(new m(e),t)},e.gunzip=function(t,e,n){return"function"===typeof e&&(n=e,e={}),y(new E(e),t,n)},e.gunzipSync=function(t,e){return b(new E(e),t)},e.inflateRaw=function(t,e,n){return"function"===typeof e&&(n=e,e={}),y(new x(e),t,n)},e.inflateRawSync=function(t,e){return b(new x(e),t)},a.inherits(O,i),O.prototype.params=function(n,r,i){if(n<e.Z_MIN_LEVEL||n>e.Z_MAX_LEVEL)throw new RangeError("Invalid compression level: "+n);if(r!=e.Z_FILTERED&&r!=e.Z_HUFFMAN_ONLY&&r!=e.Z_RLE&&r!=e.Z_FIXED&&r!=e.Z_DEFAULT_STRATEGY)throw new TypeError("Invalid strategy: "+r);if(this._level!==n||this._strategy!==r){var a=this;this.flush(o.Z_SYNC_FLUSH,(function(){s(a._handle,"zlib binding closed"),a._handle.params(n,r),a._hadError||(a._level=n,a._strategy=r,i&&i())}))}else t.nextTick(i)},O.prototype.reset=function(){return s(this._handle,"zlib binding closed"),this._handle.reset()},O.prototype._flush=function(t){this._transform(r.alloc(0),"",t)},O.prototype.flush=function(e,n){var i=this,a=this._writableState;("function"===typeof e||void 0===e&&!n)&&(n=e,e=o.Z_FULL_FLUSH),a.ended?n&&t.nextTick(n):a.ending?n&&this.once("end",n):a.needDrain?n&&this.once("drain",(function(){return i.flush(e,n)})):(this._flushFlag=e,this.write(r.alloc(0),"",n))},O.prototype.close=function(e){S(this,e),t.nextTick(R,this)},O.prototype._transform=function(t,e,n){var i,a=this._writableState,s=a.ending||a.ended,l=s&&(!t||a.length===t.length);return null===t||r.isBuffer(t)?this._handle?(l?i=this._finishFlushFlag:(i=this._flushFlag,t.length>=a.length&&(this._flushFlag=this._opts.flush||o.Z_NO_FLUSH)),void this._processChunk(t,i,n)):n(new Error("zlib binding closed")):n(new Error("invalid input"))},O.prototype._processChunk=function(t,e,n){var i=t&&t.length,o=this._chunkSize-this._offset,a=0,h=this,f="function"===typeof n;if(!f){var c,d=[],p=0;this.on("error",(function(t){c=t})),s(this._handle,"zlib binding closed");do{var g=this._handle.writeSync(e,t,a,i,this._buffer,this._offset,o)}while(!this._hadError&&b(g[0],g[1]));if(this._hadError)throw c;if(p>=l)throw S(this),new RangeError(u);var _=r.concat(d,p);return S(this),_}s(this._handle,"zlib binding closed");var y=this._handle.write(e,t,a,i,this._buffer,this._offset,o);function b(l,u){if(this&&(this.buffer=null,this.callback=null),!h._hadError){var c=o-u;if(s(c>=0,"have should not go down"),c>0){var g=h._buffer.slice(h._offset,h._offset+c);h._offset+=c,f?h.push(g):(d.push(g),p+=g.length)}if((0===u||h._offset>=h._chunkSize)&&(o=h._chunkSize,h._offset=0,h._buffer=r.allocUnsafe(h._chunkSize)),0===u){if(a+=i-l,i=l,!f)return!0;var _=h._handle.write(e,t,a,i,h._buffer,h._offset,h._chunkSize);return _.callback=b,void(_.buffer=t)}if(!f)return!1;n()}}y.buffer=t,y.callback=b},a.inherits(w,O),a.inherits(m,O),a.inherits(v,O),a.inherits(E,O),a.inherits(k,O),a.inherits(x,O),a.inherits(A,O)}).call(this,n(26))},function(t,e,n){"use strict";t.exports={PNG_SIGNATURE:[137,80,78,71,13,10,26,10],TYPE_IHDR:1229472850,TYPE_IEND:1229278788,TYPE_IDAT:1229209940,TYPE_PLTE:1347179589,TYPE_tRNS:1951551059,TYPE_gAMA:1732332865,COLORTYPE_GRAYSCALE:0,COLORTYPE_PALETTE:1,COLORTYPE_COLOR:2,COLORTYPE_ALPHA:4,COLORTYPE_PALETTE_COLOR:3,COLORTYPE_COLOR_ALPHA:6,COLORTYPE_TO_BPP_MAP:{0:1,2:3,3:1,4:2,6:4},GAMMA_DIVISION:1e5}},,,,function(t,e,n){(function(r){var i=n(40);"disable"===r.env.READABLE_STREAM&&i?(t.exports=i,e=t.exports=i.Readable,e.Readable=i.Readable,e.Writable=i.Writable,e.Duplex=i.Duplex,e.Transform=i.Transform,e.PassThrough=i.PassThrough,e.Stream=i):(e=t.exports=n(228),e.Stream=i||e,e.Readable=e,e.Writable=n(71),e.Duplex=n(44),e.Transform=n(232),e.PassThrough=n(344))}).call(this,n(26))},function(t,e,n){"use strict";(function(e){function n(t,n,r,i){if("function"!==typeof t)throw new TypeError('"callback" argument must be a function');var o,a,s=arguments.length;switch(s){case 0:case 1:return e.nextTick(t);case 2:return e.nextTick((function(){t.call(null,n)}));case 3:return e.nextTick((function(){t.call(null,n,r)}));case 4:return e.nextTick((function(){t.call(null,n,r,i)}));default:o=new Array(s-1),a=0;while(a<o.length)o[a++]=arguments[a];return e.nextTick((function(){t.apply(null,o)}))}}"undefined"===typeof e||!e.version||0===e.version.indexOf("v0.")||0===e.version.indexOf("v1.")&&0!==e.version.indexOf("v1.8.")?t.exports={nextTick:n}:t.exports=e}).call(this,n(26))},function(t,e,n){"use strict";var r="undefined"!==typeof Uint8Array&&"undefined"!==typeof Uint16Array&&"undefined"!==typeof Int32Array;function i(t,e){return Object.prototype.hasOwnProperty.call(t,e)}e.assign=function(t){var e=Array.prototype.slice.call(arguments,1);while(e.length){var n=e.shift();if(n){if("object"!==typeof n)throw new TypeError(n+"must be non-object");for(var r in n)i(n,r)&&(t[r]=n[r])}}return t},e.shrinkBuf=function(t,e){return t.length===e?t:t.subarray?t.subarray(0,e):(t.length=e,t)};var o={arraySet:function(t,e,n,r,i){if(e.subarray&&t.subarray)t.set(e.subarray(n,n+r),i);else for(var o=0;o<r;o++)t[i+o]=e[n+o]},flattenChunks:function(t){var e,n,r,i,o,a;for(r=0,e=0,n=t.length;e<n;e++)r+=t[e].length;for(a=new Uint8Array(r),i=0,e=0,n=t.length;e<n;e++)o=t[e],a.set(o,i),i+=o.length;return a}},a={arraySet:function(t,e,n,r,i){for(var o=0;o<r;o++)t[i+o]=e[n+o]},flattenChunks:function(t){return[].concat.apply([],t)}};e.setTyped=function(t){t?(e.Buf8=Uint8Array,e.Buf16=Uint16Array,e.Buf32=Int32Array,e.assign(e,o)):(e.Buf8=Array,e.Buf16=Array,e.Buf32=Array,e.assign(e,a))},e.setTyped(r)},function(t,e){function n(t){if("number"===typeof t&&(t=t.toString()),"string"!==typeof t)throw new Error("Color should be defined as hex string");let e=t.slice().replace("#","").split("");if(e.length<3||5===e.length||e.length>8)throw new Error("Invalid hex color: "+t);3!==e.length&&4!==e.length||(e=Array.prototype.concat.apply([],e.map((function(t){return[t,t]})))),6===e.length&&e.push("F","F");const n=parseInt(e.join(""),16);return{r:n>>24&255,g:n>>16&255,b:n>>8&255,a:255&n,hex:"#"+e.slice(0,6).join("")}}e.getOptions=function(t){t||(t={}),t.color||(t.color={});const e="undefined"===typeof t.margin||null===t.margin||t.margin<0?4:t.margin,r=t.width&&t.width>=21?t.width:void 0,i=t.scale||4;return{width:r,scale:r?4:i,margin:e,color:{dark:n(t.color.dark||"#000000ff"),light:n(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},e.getScale=function(t,e){return e.width&&e.width>=t+2*e.margin?e.width/(t+2*e.margin):e.scale},e.getImageWidth=function(t,n){const r=e.getScale(t,n);return Math.floor((t+2*n.margin)*r)},e.qrToImageData=function(t,n,r){const i=n.modules.size,o=n.modules.data,a=e.getScale(i,r),s=Math.floor((i+2*r.margin)*a),l=r.margin*a,u=[r.color.light,r.color.dark];for(let e=0;e<s;e++)for(let n=0;n<s;n++){let h=4*(e*s+n),f=r.color.light;if(e>=l&&n>=l&&e<s-l&&n<s-l){const t=Math.floor((e-l)/a),r=Math.floor((n-l)/a);f=u[o[t*i+r]?1:0]}t[h++]=f.r,t[h++]=f.g,t[h++]=f.b,t[h]=f.a}}},,,,,,,,,,,function(t,e){function n(t){if("string"!==typeof t)throw new Error("Param is not a string");const n=t.toLowerCase();switch(n){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw new Error("Unknown EC Level: "+t)}}e.L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2},e.isValid=function(t){return t&&"undefined"!==typeof t.bit&&t.bit>=0&&t.bit<4},e.from=function(t,r){if(e.isValid(t))return t;try{return n(t)}catch(t){return r}}},function(t,e){},function(t,e,n){var r=n(19),i=r.Buffer;function o(t,e){for(var n in t)e[n]=t[n]}function a(t,e,n){return i(t,e,n)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?t.exports=r:(o(r,e),e.Buffer=a),o(i,a),a.from=function(t,e,n){if("number"===typeof t)throw new TypeError("Argument must not be a number");return i(t,e,n)},a.alloc=function(t,e,n){if("number"!==typeof t)throw new TypeError("Argument must be a number");var r=i(t);return void 0!==e?"string"===typeof n?r.fill(e,n):r.fill(e):r.fill(0),r},a.allocUnsafe=function(t){if("number"!==typeof t)throw new TypeError("Argument must be a number");return i(t)},a.allocUnsafeSlow=function(t){if("number"!==typeof t)throw new TypeError("Argument must be a number");return r.SlowBuffer(t)}},function(t,e,n){"use strict";(function(e,r,i,o){var a=n(55);function s(t){var e=this;this.next=null,this.entry=null,this.finish=function(){U(e,t)}}t.exports=v;var l,u=!e.browser&&["v0.10","v0.9."].indexOf(e.version.slice(0,5))>-1?r:a.nextTick;v.WritableState=m;var h=Object.create(n(48));h.inherits=n(43);var f={deprecate:n(342)},c=n(229),d=n(70).Buffer,p=("undefined"!==typeof i?i:"undefined"!==typeof o?o:"undefined"!==typeof self?self:{}).Uint8Array||function(){};function g(t){return d.from(t)}function _(t){return d.isBuffer(t)||t instanceof p}var y,b=n(230);function w(){}function m(t,e){l=l||n(44),t=t||{};var r=e instanceof l;this.objectMode=!!t.objectMode,r&&(this.objectMode=this.objectMode||!!t.writableObjectMode);var i=t.highWaterMark,o=t.writableHighWaterMark,a=this.objectMode?16:16384;this.highWaterMark=i||0===i?i:r&&(o||0===o)?o:a,this.highWaterMark=Math.floor(this.highWaterMark),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var u=!1===t.decodeStrings;this.decodeStrings=!u,this.defaultEncoding=t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(t){R(e,t)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new s(this)}function v(t){if(l=l||n(44),!y.call(v,this)&&!(this instanceof l))return new v(t);this._writableState=new m(t,this),this.writable=!0,t&&("function"===typeof t.write&&(this._write=t.write),"function"===typeof t.writev&&(this._writev=t.writev),"function"===typeof t.destroy&&(this._destroy=t.destroy),"function"===typeof t.final&&(this._final=t.final)),c.call(this)}function E(t,e){var n=new Error("write after end");t.emit("error",n),a.nextTick(e,n)}function k(t,e,n,r){var i=!0,o=!1;return null===n?o=new TypeError("May not write null values to stream"):"string"===typeof n||void 0===n||e.objectMode||(o=new TypeError("Invalid non-string/buffer chunk")),o&&(t.emit("error",o),a.nextTick(r,o),i=!1),i}function x(t,e,n){return t.objectMode||!1===t.decodeStrings||"string"!==typeof e||(e=d.from(e,n)),e}function A(t,e,n,r,i,o){if(!n){var a=x(e,r,i);r!==a&&(n=!0,i="buffer",r=a)}var s=e.objectMode?1:r.length;e.length+=s;var l=e.length<e.highWaterMark;if(l||(e.needDrain=!0),e.writing||e.corked){var u=e.lastBufferedRequest;e.lastBufferedRequest={chunk:r,encoding:i,isBuf:n,callback:o,next:null},u?u.next=e.lastBufferedRequest:e.bufferedRequest=e.lastBufferedRequest,e.bufferedRequestCount+=1}else T(t,e,!1,s,r,i,o);return l}function T(t,e,n,r,i,o,a){e.writelen=r,e.writecb=a,e.writing=!0,e.sync=!0,n?t._writev(i,e.onwrite):t._write(i,o,e.onwrite),e.sync=!1}function O(t,e,n,r,i){--e.pendingcb,n?(a.nextTick(i,r),a.nextTick(N,t,e),t._writableState.errorEmitted=!0,t.emit("error",r)):(i(r),t._writableState.errorEmitted=!0,t.emit("error",r),N(t,e))}function S(t){t.writing=!1,t.writecb=null,t.length-=t.writelen,t.writelen=0}function R(t,e){var n=t._writableState,r=n.sync,i=n.writecb;if(S(n),e)O(t,n,r,e,i);else{var o=C(n);o||n.corked||n.bufferProcessing||!n.bufferedRequest||L(t,n),r?u(I,t,n,o,i):I(t,n,o,i)}}function I(t,e,n,r){n||P(t,e),e.pendingcb--,r(),N(t,e)}function P(t,e){0===e.length&&e.needDrain&&(e.needDrain=!1,t.emit("drain"))}function L(t,e){e.bufferProcessing=!0;var n=e.bufferedRequest;if(t._writev&&n&&n.next){var r=e.bufferedRequestCount,i=new Array(r),o=e.corkedRequestsFree;o.entry=n;var a=0,l=!0;while(n)i[a]=n,n.isBuf||(l=!1),n=n.next,a+=1;i.allBuffers=l,T(t,e,!0,e.length,i,"",o.finish),e.pendingcb++,e.lastBufferedRequest=null,o.next?(e.corkedRequestsFree=o.next,o.next=null):e.corkedRequestsFree=new s(e),e.bufferedRequestCount=0}else{while(n){var u=n.chunk,h=n.encoding,f=n.callback,c=e.objectMode?1:u.length;if(T(t,e,!1,c,u,h,f),n=n.next,e.bufferedRequestCount--,e.writing)break}null===n&&(e.lastBufferedRequest=null)}e.bufferedRequest=n,e.bufferProcessing=!1}function C(t){return t.ending&&0===t.length&&null===t.bufferedRequest&&!t.finished&&!t.writing}function j(t,e){t._final((function(n){e.pendingcb--,n&&t.emit("error",n),e.prefinished=!0,t.emit("prefinish"),N(t,e)}))}function B(t,e){e.prefinished||e.finalCalled||("function"===typeof t._final?(e.pendingcb++,e.finalCalled=!0,a.nextTick(j,t,e)):(e.prefinished=!0,t.emit("prefinish")))}function N(t,e){var n=C(e);return n&&(B(t,e),0===e.pendingcb&&(e.finished=!0,t.emit("finish"))),n}function M(t,e,n){e.ending=!0,N(t,e),n&&(e.finished?a.nextTick(n):t.once("finish",n)),e.ended=!0,t.writable=!1}function U(t,e,n){var r=t.entry;t.entry=null;while(r){var i=r.callback;e.pendingcb--,i(n),r=r.next}e.corkedRequestsFree.next=t}h.inherits(v,c),m.prototype.getBuffer=function(){var t=this.bufferedRequest,e=[];while(t)e.push(t),t=t.next;return e},function(){try{Object.defineProperty(m.prototype,"buffer",{get:f.deprecate((function(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(t){}}(),"function"===typeof Symbol&&Symbol.hasInstance&&"function"===typeof Function.prototype[Symbol.hasInstance]?(y=Function.prototype[Symbol.hasInstance],Object.defineProperty(v,Symbol.hasInstance,{value:function(t){return!!y.call(this,t)||this===v&&(t&&t._writableState instanceof m)}})):y=function(t){return t instanceof this},v.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))},v.prototype.write=function(t,e,n){var r=this._writableState,i=!1,o=!r.objectMode&&_(t);return o&&!d.isBuffer(t)&&(t=g(t)),"function"===typeof e&&(n=e,e=null),o?e="buffer":e||(e=r.defaultEncoding),"function"!==typeof n&&(n=w),r.ended?E(this,n):(o||k(this,r,t,n))&&(r.pendingcb++,i=A(this,r,o,t,e,n)),i},v.prototype.cork=function(){var t=this._writableState;t.corked++},v.prototype.uncork=function(){var t=this._writableState;t.corked&&(t.corked--,t.writing||t.corked||t.bufferProcessing||!t.bufferedRequest||L(this,t))},v.prototype.setDefaultEncoding=function(t){if("string"===typeof t&&(t=t.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((t+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+t);return this._writableState.defaultEncoding=t,this},Object.defineProperty(v.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),v.prototype._write=function(t,e,n){n(new Error("_write() is not implemented"))},v.prototype._writev=null,v.prototype.end=function(t,e,n){var r=this._writableState;"function"===typeof t?(n=t,t=null,e=null):"function"===typeof e&&(n=e,e=null),null!==t&&void 0!==t&&this.write(t,e),r.corked&&(r.corked=1,this.uncork()),r.ending||M(this,r,n)},Object.defineProperty(v.prototype,"destroyed",{get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(t){this._writableState&&(this._writableState.destroyed=t)}}),v.prototype.destroy=b.destroy,v.prototype._undestroy=b.undestroy,v.prototype._destroy=function(t,e){this.end(),e(t)}}).call(this,n(26),n(340).setImmediate,n(29),n(9)["window"])},function(t,e,n){"use strict";(function(e){var r=n(351)();function i(t,e){if(t===e)return 0;for(var n=t.length,r=e.length,i=0,o=Math.min(n,r);i<o;++i)if(t[i]!==e[i]){n=t[i],r=e[i];break}return n<r?-1:r<n?1:0}function o(t){return e.Buffer&&"function"===typeof e.Buffer.isBuffer?e.Buffer.isBuffer(t):!(null==t||!t._isBuffer)}var a=n(30),s=Object.prototype.hasOwnProperty,l=Array.prototype.slice,u=function(){return"foo"===function(){}.name}();function h(t){return Object.prototype.toString.call(t)}function f(t){return!o(t)&&("function"===typeof e.ArrayBuffer&&("function"===typeof ArrayBuffer.isView?ArrayBuffer.isView(t):!!t&&(t instanceof DataView||!!(t.buffer&&t.buffer instanceof ArrayBuffer))))}var c=t.exports=w,d=/\s*function\s+([^\(\s]*)\s*/;function p(t){if(a.isFunction(t)){if(u)return t.name;var e=t.toString(),n=e.match(d);return n&&n[1]}}function g(t,e){return"string"===typeof t?t.length<e?t:t.slice(0,e):t}function _(t){if(u||!a.isFunction(t))return a.inspect(t);var e=p(t),n=e?": "+e:"";return"[Function"+n+"]"}function y(t){return g(_(t.actual),128)+" "+t.operator+" "+g(_(t.expected),128)}function b(t,e,n,r,i){throw new c.AssertionError({message:n,actual:t,expected:e,operator:r,stackStartFunction:i})}function w(t,e){t||b(t,!0,e,"==",c.ok)}function m(t,e,n,r){if(t===e)return!0;if(o(t)&&o(e))return 0===i(t,e);if(a.isDate(t)&&a.isDate(e))return t.getTime()===e.getTime();if(a.isRegExp(t)&&a.isRegExp(e))return t.source===e.source&&t.global===e.global&&t.multiline===e.multiline&&t.lastIndex===e.lastIndex&&t.ignoreCase===e.ignoreCase;if(null!==t&&"object"===typeof t||null!==e&&"object"===typeof e){if(f(t)&&f(e)&&h(t)===h(e)&&!(t instanceof Float32Array||t instanceof Float64Array))return 0===i(new Uint8Array(t.buffer),new Uint8Array(e.buffer));if(o(t)!==o(e))return!1;r=r||{actual:[],expected:[]};var s=r.actual.indexOf(t);return-1!==s&&s===r.expected.indexOf(e)||(r.actual.push(t),r.expected.push(e),E(t,e,n,r))}return n?t===e:t==e}function v(t){return"[object Arguments]"==Object.prototype.toString.call(t)}function E(t,e,n,r){if(null===t||void 0===t||null===e||void 0===e)return!1;if(a.isPrimitive(t)||a.isPrimitive(e))return t===e;if(n&&Object.getPrototypeOf(t)!==Object.getPrototypeOf(e))return!1;var i=v(t),o=v(e);if(i&&!o||!i&&o)return!1;if(i)return t=l.call(t),e=l.call(e),m(t,e,n);var s,u,h=S(t),f=S(e);if(h.length!==f.length)return!1;for(h.sort(),f.sort(),u=h.length-1;u>=0;u--)if(h[u]!==f[u])return!1;for(u=h.length-1;u>=0;u--)if(s=h[u],!m(t[s],e[s],n,r))return!1;return!0}function k(t,e,n){m(t,e,!0)&&b(t,e,n,"notDeepStrictEqual",k)}function x(t,e){if(!t||!e)return!1;if("[object RegExp]"==Object.prototype.toString.call(e))return e.test(t);try{if(t instanceof e)return!0}catch(t){}return!Error.isPrototypeOf(e)&&!0===e.call({},t)}function A(t){var e;try{t()}catch(t){e=t}return e}function T(t,e,n,r){var i;if("function"!==typeof e)throw new TypeError('"block" argument must be a function');"string"===typeof n&&(r=n,n=null),i=A(e),r=(n&&n.name?" ("+n.name+").":".")+(r?" "+r:"."),t&&!i&&b(i,n,"Missing expected exception"+r);var o="string"===typeof r,s=!t&&a.isError(i),l=!t&&i&&!n;if((s&&o&&x(i,n)||l)&&b(i,n,"Got unwanted exception"+r),t&&i&&n&&!x(i,n)||!t&&i)throw i}function O(t,e){t||b(t,!0,e,"==",O)}c.AssertionError=function(t){this.name="AssertionError",this.actual=t.actual,this.expected=t.expected,this.operator=t.operator,t.message?(this.message=t.message,this.generatedMessage=!1):(this.message=y(this),this.generatedMessage=!0);var e=t.stackStartFunction||b;if(Error.captureStackTrace)Error.captureStackTrace(this,e);else{var n=new Error;if(n.stack){var r=n.stack,i=p(e),o=r.indexOf("\n"+i);if(o>=0){var a=r.indexOf("\n",o+1);r=r.substring(a+1)}this.stack=r}}},a.inherits(c.AssertionError,Error),c.fail=b,c.ok=w,c.equal=function(t,e,n){t!=e&&b(t,e,n,"==",c.equal)},c.notEqual=function(t,e,n){t==e&&b(t,e,n,"!=",c.notEqual)},c.deepEqual=function(t,e,n){m(t,e,!1)||b(t,e,n,"deepEqual",c.deepEqual)},c.deepStrictEqual=function(t,e,n){m(t,e,!0)||b(t,e,n,"deepStrictEqual",c.deepStrictEqual)},c.notDeepEqual=function(t,e,n){m(t,e,!1)&&b(t,e,n,"notDeepEqual",c.notDeepEqual)},c.notDeepStrictEqual=k,c.strictEqual=function(t,e,n){t!==e&&b(t,e,n,"===",c.strictEqual)},c.notStrictEqual=function(t,e,n){t===e&&b(t,e,n,"!==",c.notStrictEqual)},c.throws=function(t,e,n){T(!0,t,e,n)},c.doesNotThrow=function(t,e,n){T(!1,t,e,n)},c.ifError=function(t){if(t)throw t},c.strict=r(O,c,{equal:c.strictEqual,deepEqual:c.deepStrictEqual,notEqual:c.notStrictEqual,notDeepEqual:c.notDeepStrictEqual}),c.strict.strict=c.strict;var S=Object.keys||function(t){var e=[];for(var n in t)s.call(t,n)&&e.push(n);return e}}).call(this,n(29))},function(t,e,n){"use strict";var r=n(358);t.exports=Function.prototype.bind||r},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(t,e){t.exports=function(){return"function"===typeof Promise&&Promise.prototype&&Promise.prototype.then}},function(t,e,n){const r=n(38),i=n(68),o=n(316),a=n(317),s=n(318),l=n(319),u=n(320),h=n(223),f=n(321),c=n(324),d=n(325),p=n(39),g=n(326);function _(t,e){const n=t.size,r=l.getPositions(e);for(let e=0;e<r.length;e++){const i=r[e][0],o=r[e][1];for(let e=-1;e<=7;e++)if(!(i+e<=-1||n<=i+e))for(let r=-1;r<=7;r++)o+r<=-1||n<=o+r||(e>=0&&e<=6&&(0===r||6===r)||r>=0&&r<=6&&(0===e||6===e)||e>=2&&e<=4&&r>=2&&r<=4?t.set(i+e,o+r,!0,!0):t.set(i+e,o+r,!1,!0))}}function y(t){const e=t.size;for(let n=8;n<e-8;n++){const e=n%2===0;t.set(n,6,e,!0),t.set(6,n,e,!0)}}function b(t,e){const n=s.getPositions(e);for(let e=0;e<n.length;e++){const r=n[e][0],i=n[e][1];for(let e=-2;e<=2;e++)for(let n=-2;n<=2;n++)-2===e||2===e||-2===n||2===n||0===e&&0===n?t.set(r+e,i+n,!0,!0):t.set(r+e,i+n,!1,!0)}}function w(t,e){const n=t.size,r=c.getEncodedBits(e);let i,o,a;for(let e=0;e<18;e++)i=Math.floor(e/3),o=e%3+n-8-3,a=1===(r>>e&1),t.set(i,o,a,!0),t.set(o,i,a,!0)}function m(t,e,n){const r=t.size,i=d.getEncodedBits(e,n);let o,a;for(o=0;o<15;o++)a=1===(i>>o&1),o<6?t.set(o,8,a,!0):o<8?t.set(o+1,8,a,!0):t.set(r-15+o,8,a,!0),o<8?t.set(8,r-o-1,a,!0):o<9?t.set(8,15-o-1+1,a,!0):t.set(8,15-o-1,a,!0);t.set(r-8,8,1,!0)}function v(t,e){const n=t.size;let r=-1,i=n-1,o=7,a=0;for(let s=n-1;s>0;s-=2){6===s&&s--;while(1){for(let n=0;n<2;n++)if(!t.isReserved(i,s-n)){let r=!1;a<e.length&&(r=1===(e[a]>>>o&1)),t.set(i,s-n,r),o--,-1===o&&(a++,o=7)}if(i+=r,i<0||n<=i){i-=r,r=-r;break}}}}function E(t,e,n){const i=new o;n.forEach((function(e){i.put(e.mode.bit,4),i.put(e.getLength(),p.getCharCountIndicator(e.mode,t)),e.write(i)}));const a=r.getSymbolTotalCodewords(t),s=h.getTotalCodewordsCount(t,e),l=8*(a-s);i.getLengthInBits()+4<=l&&i.put(0,4);while(i.getLengthInBits()%8!==0)i.putBit(0);const u=(l-i.getLengthInBits())/8;for(let t=0;t<u;t++)i.put(t%2?17:236,8);return k(i,t,e)}function k(t,e,n){const i=r.getSymbolTotalCodewords(e),o=h.getTotalCodewordsCount(e,n),a=i-o,s=h.getBlocksCount(e,n),l=i%s,u=s-l,c=Math.floor(i/s),d=Math.floor(a/s),p=d+1,g=c-d,_=new f(g);let y=0;const b=new Array(s),w=new Array(s);let m=0;const v=new Uint8Array(t.buffer);for(let t=0;t<s;t++){const e=t<u?d:p;b[t]=v.slice(y,y+e),w[t]=_.encode(b[t]),y+=e,m=Math.max(m,e)}const E=new Uint8Array(i);let k,x,A=0;for(k=0;k<m;k++)for(x=0;x<s;x++)k<b[x].length&&(E[A++]=b[x][k]);for(k=0;k<g;k++)for(x=0;x<s;x++)E[A++]=w[x][k];return E}function x(t,e,n,i){let o;if(Array.isArray(t))o=g.fromArray(t);else{if("string"!==typeof t)throw new Error("Invalid data");{let r=e;if(!r){const e=g.rawSplit(t);r=c.getBestVersionForData(e,n)}o=g.fromString(t,r||40)}}const s=c.getBestVersionForData(o,n);if(!s)throw new Error("The amount of data is too big to be stored in a QR Code");if(e){if(e<s)throw new Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+s+".\n")}else e=s;const l=E(e,n,o),h=r.getSymbolSize(e),f=new a(h);return _(f,e),y(f),b(f,e),m(f,n,0),e>=7&&w(f,e),v(f,l),isNaN(i)&&(i=u.getBestMask(f,m.bind(null,f,n))),u.applyMask(i,f),m(f,n,i),{modules:f,version:e,errorCorrectionLevel:n,maskPattern:i,segments:o}}e.create=function(t,e){if("undefined"===typeof t||""===t)throw new Error("No input text");let n,o,a=i.M;return"undefined"!==typeof e&&(a=i.from(e.errorCorrectionLevel,i.M),n=c.from(e.version),o=u.from(e.maskPattern),e.toSJISFunc&&r.setToSJISFunction(e.toSJISFunc)),x(t,n,a,o)}},function(t,e,n){const r=n(68),i=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],o=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];e.getBlocksCount=function(t,e){switch(e){case r.L:return i[4*(t-1)+0];case r.M:return i[4*(t-1)+1];case r.Q:return i[4*(t-1)+2];case r.H:return i[4*(t-1)+3];default:return}},e.getTotalCodewordsCount=function(t,e){switch(e){case r.L:return o[4*(t-1)+0];case r.M:return o[4*(t-1)+1];case r.Q:return o[4*(t-1)+2];case r.H:return o[4*(t-1)+3];default:return}}},function(t,e){e.isValid=function(t){return!isNaN(t)&&t>=1&&t<=40}},function(t,e){const n="[0-9]+",r="[A-Z $%*+\\-./:]+";let i="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+";i=i.replace(/u/g,"\\u");const o="(?:(?![A-Z0-9 $%*+\\-./:]|"+i+")(?:.|[\r\n]))+";e.KANJI=new RegExp(i,"g"),e.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),e.BYTE=new RegExp(o,"g"),e.NUMERIC=new RegExp(n,"g"),e.ALPHANUMERIC=new RegExp(r,"g");const a=new RegExp("^"+i+"$"),s=new RegExp("^"+n+"$"),l=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");e.testKanji=function(t){return a.test(t)},e.testNumeric=function(t){return s.test(t)},e.testAlphanumeric=function(t){return l.test(t)}},function(t,e){var n={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==n.call(t)}},function(t,e,n){"use strict";var r,i="object"===typeof Reflect?Reflect:null,o=i&&"function"===typeof i.apply?i.apply:function(t,e,n){return Function.prototype.apply.call(t,e,n)};function a(t){console&&console.warn&&console.warn(t)}r=i&&"function"===typeof i.ownKeys?i.ownKeys:Object.getOwnPropertySymbols?function(t){return Object.getOwnPropertyNames(t).concat(Object.getOwnPropertySymbols(t))}:function(t){return Object.getOwnPropertyNames(t)};var s=Number.isNaN||function(t){return t!==t};function l(){l.init.call(this)}t.exports=l,t.exports.once=m,l.EventEmitter=l,l.prototype._events=void 0,l.prototype._eventsCount=0,l.prototype._maxListeners=void 0;var u=10;function h(t){if("function"!==typeof t)throw new TypeError('The "listener" argument must be of type Function. Received type '+typeof t)}function f(t){return void 0===t._maxListeners?l.defaultMaxListeners:t._maxListeners}function c(t,e,n,r){var i,o,s;if(h(n),o=t._events,void 0===o?(o=t._events=Object.create(null),t._eventsCount=0):(void 0!==o.newListener&&(t.emit("newListener",e,n.listener?n.listener:n),o=t._events),s=o[e]),void 0===s)s=o[e]=n,++t._eventsCount;else if("function"===typeof s?s=o[e]=r?[n,s]:[s,n]:r?s.unshift(n):s.push(n),i=f(t),i>0&&s.length>i&&!s.warned){s.warned=!0;var l=new Error("Possible EventEmitter memory leak detected. "+s.length+" "+String(e)+" listeners added. Use emitter.setMaxListeners() to increase limit");l.name="MaxListenersExceededWarning",l.emitter=t,l.type=e,l.count=s.length,a(l)}return t}function d(){if(!this.fired)return this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0===arguments.length?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function p(t,e,n){var r={fired:!1,wrapFn:void 0,target:t,type:e,listener:n},i=d.bind(r);return i.listener=n,r.wrapFn=i,i}function g(t,e,n){var r=t._events;if(void 0===r)return[];var i=r[e];return void 0===i?[]:"function"===typeof i?n?[i.listener||i]:[i]:n?w(i):y(i,i.length)}function _(t){var e=this._events;if(void 0!==e){var n=e[t];if("function"===typeof n)return 1;if(void 0!==n)return n.length}return 0}function y(t,e){for(var n=new Array(e),r=0;r<e;++r)n[r]=t[r];return n}function b(t,e){for(;e+1<t.length;e++)t[e]=t[e+1];t.pop()}function w(t){for(var e=new Array(t.length),n=0;n<e.length;++n)e[n]=t[n].listener||t[n];return e}function m(t,e){return new Promise((function(n,r){function i(n){t.removeListener(e,o),r(n)}function o(){"function"===typeof t.removeListener&&t.removeListener("error",i),n([].slice.call(arguments))}E(t,e,o,{once:!0}),"error"!==e&&v(t,i,{once:!0})}))}function v(t,e,n){"function"===typeof t.on&&E(t,"error",e,n)}function E(t,e,n,r){if("function"===typeof t.on)r.once?t.once(e,n):t.on(e,n);else{if("function"!==typeof t.addEventListener)throw new TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof t);t.addEventListener(e,(function i(o){r.once&&t.removeEventListener(e,i),n(o)}))}}Object.defineProperty(l,"defaultMaxListeners",{enumerable:!0,get:function(){return u},set:function(t){if("number"!==typeof t||t<0||s(t))throw new RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+t+".");u=t}}),l.init=function(){void 0!==this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},l.prototype.setMaxListeners=function(t){if("number"!==typeof t||t<0||s(t))throw new RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+t+".");return this._maxListeners=t,this},l.prototype.getMaxListeners=function(){return f(this)},l.prototype.emit=function(t){for(var e=[],n=1;n<arguments.length;n++)e.push(arguments[n]);var r="error"===t,i=this._events;if(void 0!==i)r=r&&void 0===i.error;else if(!r)return!1;if(r){var a;if(e.length>0&&(a=e[0]),a instanceof Error)throw a;var s=new Error("Unhandled error."+(a?" ("+a.message+")":""));throw s.context=a,s}var l=i[t];if(void 0===l)return!1;if("function"===typeof l)o(l,this,e);else{var u=l.length,h=y(l,u);for(n=0;n<u;++n)o(h[n],this,e)}return!0},l.prototype.addListener=function(t,e){return c(this,t,e,!1)},l.prototype.on=l.prototype.addListener,l.prototype.prependListener=function(t,e){return c(this,t,e,!0)},l.prototype.once=function(t,e){return h(e),this.on(t,p(this,t,e)),this},l.prototype.prependOnceListener=function(t,e){return h(e),this.prependListener(t,p(this,t,e)),this},l.prototype.removeListener=function(t,e){var n,r,i,o,a;if(h(e),r=this._events,void 0===r)return this;if(n=r[t],void 0===n)return this;if(n===e||n.listener===e)0===--this._eventsCount?this._events=Object.create(null):(delete r[t],r.removeListener&&this.emit("removeListener",t,n.listener||e));else if("function"!==typeof n){for(i=-1,o=n.length-1;o>=0;o--)if(n[o]===e||n[o].listener===e){a=n[o].listener,i=o;break}if(i<0)return this;0===i?n.shift():b(n,i),1===n.length&&(r[t]=n[0]),void 0!==r.removeListener&&this.emit("removeListener",t,a||e)}return this},l.prototype.off=l.prototype.removeListener,l.prototype.removeAllListeners=function(t){var e,n,r;if(n=this._events,void 0===n)return this;if(void 0===n.removeListener)return 0===arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==n[t]&&(0===--this._eventsCount?this._events=Object.create(null):delete n[t]),this;if(0===arguments.length){var i,o=Object.keys(n);for(r=0;r<o.length;++r)i=o[r],"removeListener"!==i&&this.removeAllListeners(i);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if(e=n[t],"function"===typeof e)this.removeListener(t,e);else if(void 0!==e)for(r=e.length-1;r>=0;r--)this.removeListener(t,e[r]);return this},l.prototype.listeners=function(t){return g(this,t,!0)},l.prototype.rawListeners=function(t){return g(this,t,!1)},l.listenerCount=function(t,e){return"function"===typeof t.listenerCount?t.listenerCount(e):_.call(t,e)},l.prototype.listenerCount=_,l.prototype.eventNames=function(){return this._eventsCount>0?r(this._events):[]}},function(t,e,n){"use strict";(function(e,r,i){var o=n(55);t.exports=k;var a,s=n(226);k.ReadableState=E;n(227).EventEmitter;var l=function(t,e){return t.listeners(e).length},u=n(229),h=n(70).Buffer,f=("undefined"!==typeof e?e:"undefined"!==typeof r?r:"undefined"!==typeof self?self:{}).Uint8Array||function(){};function c(t){return h.from(t)}function d(t){return h.isBuffer(t)||t instanceof f}var p=Object.create(n(48));p.inherits=n(43);var g=n(30),_=void 0;_=g&&g.debuglog?g.debuglog("stream"):function(){};var y,b=n(339),w=n(230);p.inherits(k,u);var m=["error","close","destroy","pause","resume"];function v(t,e,n){if("function"===typeof t.prependListener)return t.prependListener(e,n);t._events&&t._events[e]?s(t._events[e])?t._events[e].unshift(n):t._events[e]=[n,t._events[e]]:t.on(e,n)}function E(t,e){a=a||n(44),t=t||{};var r=e instanceof a;this.objectMode=!!t.objectMode,r&&(this.objectMode=this.objectMode||!!t.readableObjectMode);var i=t.highWaterMark,o=t.readableHighWaterMark,s=this.objectMode?16:16384;this.highWaterMark=i||0===i?i:r&&(o||0===o)?o:s,this.highWaterMark=Math.floor(this.highWaterMark),this.buffer=new b,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.destroyed=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(y||(y=n(231).StringDecoder),this.decoder=new y(t.encoding),this.encoding=t.encoding)}function k(t){if(a=a||n(44),!(this instanceof k))return new k(t);this._readableState=new E(t,this),this.readable=!0,t&&("function"===typeof t.read&&(this._read=t.read),"function"===typeof t.destroy&&(this._destroy=t.destroy)),u.call(this)}function x(t,e,n,r,i){var o,a=t._readableState;null===e?(a.reading=!1,P(t,a)):(i||(o=T(a,e)),o?t.emit("error",o):a.objectMode||e&&e.length>0?("string"===typeof e||a.objectMode||Object.getPrototypeOf(e)===h.prototype||(e=c(e)),r?a.endEmitted?t.emit("error",new Error("stream.unshift() after end event")):A(t,a,e,!0):a.ended?t.emit("error",new Error("stream.push() after EOF")):(a.reading=!1,a.decoder&&!n?(e=a.decoder.write(e),a.objectMode||0!==e.length?A(t,a,e,!1):j(t,a)):A(t,a,e,!1))):r||(a.reading=!1));return O(a)}function A(t,e,n,r){e.flowing&&0===e.length&&!e.sync?(t.emit("data",n),t.read(0)):(e.length+=e.objectMode?1:n.length,r?e.buffer.unshift(n):e.buffer.push(n),e.needReadable&&L(t)),j(t,e)}function T(t,e){var n;return d(e)||"string"===typeof e||void 0===e||t.objectMode||(n=new TypeError("Invalid non-string/buffer chunk")),n}function O(t){return!t.ended&&(t.needReadable||t.length<t.highWaterMark||0===t.length)}Object.defineProperty(k.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(t){this._readableState&&(this._readableState.destroyed=t)}}),k.prototype.destroy=w.destroy,k.prototype._undestroy=w.undestroy,k.prototype._destroy=function(t,e){this.push(null),e(t)},k.prototype.push=function(t,e){var n,r=this._readableState;return r.objectMode?n=!0:"string"===typeof t&&(e=e||r.defaultEncoding,e!==r.encoding&&(t=h.from(t,e),e=""),n=!0),x(this,t,e,!1,n)},k.prototype.unshift=function(t){return x(this,t,null,!0,!1)},k.prototype.isPaused=function(){return!1===this._readableState.flowing},k.prototype.setEncoding=function(t){return y||(y=n(231).StringDecoder),this._readableState.decoder=new y(t),this._readableState.encoding=t,this};var S=8388608;function R(t){return t>=S?t=S:(t--,t|=t>>>1,t|=t>>>2,t|=t>>>4,t|=t>>>8,t|=t>>>16,t++),t}function I(t,e){return t<=0||0===e.length&&e.ended?0:e.objectMode?1:t!==t?e.flowing&&e.length?e.buffer.head.data.length:e.length:(t>e.highWaterMark&&(e.highWaterMark=R(t)),t<=e.length?t:e.ended?e.length:(e.needReadable=!0,0))}function P(t,e){if(!e.ended){if(e.decoder){var n=e.decoder.end();n&&n.length&&(e.buffer.push(n),e.length+=e.objectMode?1:n.length)}e.ended=!0,L(t)}}function L(t){var e=t._readableState;e.needReadable=!1,e.emittedReadable||(_("emitReadable",e.flowing),e.emittedReadable=!0,e.sync?o.nextTick(C,t):C(t))}function C(t){_("emit readable"),t.emit("readable"),F(t)}function j(t,e){e.readingMore||(e.readingMore=!0,o.nextTick(B,t,e))}function B(t,e){var n=e.length;while(!e.reading&&!e.flowing&&!e.ended&&e.length<e.highWaterMark){if(_("maybeReadMore read 0"),t.read(0),n===e.length)break;n=e.length}e.readingMore=!1}function N(t){return function(){var e=t._readableState;_("pipeOnDrain",e.awaitDrain),e.awaitDrain&&e.awaitDrain--,0===e.awaitDrain&&l(t,"data")&&(e.flowing=!0,F(t))}}function M(t){_("readable nexttick read 0"),t.read(0)}function U(t,e){e.resumeScheduled||(e.resumeScheduled=!0,o.nextTick(D,t,e))}function D(t,e){e.reading||(_("resume read 0"),t.read(0)),e.resumeScheduled=!1,e.awaitDrain=0,t.emit("resume"),F(t),e.flowing&&!e.reading&&t.read(0)}function F(t){var e=t._readableState;_("flow",e.flowing);while(e.flowing&&null!==t.read());}function z(t,e){return 0===e.length?null:(e.objectMode?n=e.buffer.shift():!t||t>=e.length?(n=e.decoder?e.buffer.join(""):1===e.buffer.length?e.buffer.head.data:e.buffer.concat(e.length),e.buffer.clear()):n=Z(t,e.buffer,e.decoder),n);var n}function Z(t,e,n){var r;return t<e.head.data.length?(r=e.head.data.slice(0,t),e.head.data=e.head.data.slice(t)):r=t===e.head.data.length?e.shift():n?Y(t,e):H(t,e),r}function Y(t,e){var n=e.head,r=1,i=n.data;t-=i.length;while(n=n.next){var o=n.data,a=t>o.length?o.length:t;if(a===o.length?i+=o:i+=o.slice(0,t),t-=a,0===t){a===o.length?(++r,n.next?e.head=n.next:e.head=e.tail=null):(e.head=n,n.data=o.slice(a));break}++r}return e.length-=r,i}function H(t,e){var n=h.allocUnsafe(t),r=e.head,i=1;r.data.copy(n),t-=r.data.length;while(r=r.next){var o=r.data,a=t>o.length?o.length:t;if(o.copy(n,n.length-t,0,a),t-=a,0===t){a===o.length?(++i,r.next?e.head=r.next:e.head=e.tail=null):(e.head=r,r.data=o.slice(a));break}++i}return e.length-=i,n}function W(t){var e=t._readableState;if(e.length>0)throw new Error('"endReadable()" called on non-empty stream');e.endEmitted||(e.ended=!0,o.nextTick(q,e,t))}function q(t,e){t.endEmitted||0!==t.length||(t.endEmitted=!0,e.readable=!1,e.emit("end"))}function G(t,e){for(var n=0,r=t.length;n<r;n++)if(t[n]===e)return n;return-1}k.prototype.read=function(t){_("read",t),t=parseInt(t,10);var e=this._readableState,n=t;if(0!==t&&(e.emittedReadable=!1),0===t&&e.needReadable&&(e.length>=e.highWaterMark||e.ended))return _("read: emitReadable",e.length,e.ended),0===e.length&&e.ended?W(this):L(this),null;if(t=I(t,e),0===t&&e.ended)return 0===e.length&&W(this),null;var r,i=e.needReadable;return _("need readable",i),(0===e.length||e.length-t<e.highWaterMark)&&(i=!0,_("length less than watermark",i)),e.ended||e.reading?(i=!1,_("reading or ended",i)):i&&(_("do read"),e.reading=!0,e.sync=!0,0===e.length&&(e.needReadable=!0),this._read(e.highWaterMark),e.sync=!1,e.reading||(t=I(n,e))),r=t>0?z(t,e):null,null===r?(e.needReadable=!0,t=0):e.length-=t,0===e.length&&(e.ended||(e.needReadable=!0),n!==t&&e.ended&&W(this)),null!==r&&this.emit("data",r),r},k.prototype._read=function(t){this.emit("error",new Error("_read() is not implemented"))},k.prototype.pipe=function(t,e){var n=this,r=this._readableState;switch(r.pipesCount){case 0:r.pipes=t;break;case 1:r.pipes=[r.pipes,t];break;default:r.pipes.push(t);break}r.pipesCount+=1,_("pipe count=%d opts=%j",r.pipesCount,e);var a=(!e||!1!==e.end)&&t!==i.stdout&&t!==i.stderr,s=a?h:m;function u(t,e){_("onunpipe"),t===n&&e&&!1===e.hasUnpiped&&(e.hasUnpiped=!0,d())}function h(){_("onend"),t.end()}r.endEmitted?o.nextTick(s):n.once("end",s),t.on("unpipe",u);var f=N(n);t.on("drain",f);var c=!1;function d(){_("cleanup"),t.removeListener("close",b),t.removeListener("finish",w),t.removeListener("drain",f),t.removeListener("error",y),t.removeListener("unpipe",u),n.removeListener("end",h),n.removeListener("end",m),n.removeListener("data",g),c=!0,!r.awaitDrain||t._writableState&&!t._writableState.needDrain||f()}var p=!1;function g(e){_("ondata"),p=!1;var i=t.write(e);!1!==i||p||((1===r.pipesCount&&r.pipes===t||r.pipesCount>1&&-1!==G(r.pipes,t))&&!c&&(_("false write response, pause",r.awaitDrain),r.awaitDrain++,p=!0),n.pause())}function y(e){_("onerror",e),m(),t.removeListener("error",y),0===l(t,"error")&&t.emit("error",e)}function b(){t.removeListener("finish",w),m()}function w(){_("onfinish"),t.removeListener("close",b),m()}function m(){_("unpipe"),n.unpipe(t)}return n.on("data",g),v(t,"error",y),t.once("close",b),t.once("finish",w),t.emit("pipe",n),r.flowing||(_("pipe resume"),n.resume()),t},k.prototype.unpipe=function(t){var e=this._readableState,n={hasUnpiped:!1};if(0===e.pipesCount)return this;if(1===e.pipesCount)return t&&t!==e.pipes||(t||(t=e.pipes),e.pipes=null,e.pipesCount=0,e.flowing=!1,t&&t.emit("unpipe",this,n)),this;if(!t){var r=e.pipes,i=e.pipesCount;e.pipes=null,e.pipesCount=0,e.flowing=!1;for(var o=0;o<i;o++)r[o].emit("unpipe",this,{hasUnpiped:!1});return this}var a=G(e.pipes,t);return-1===a||(e.pipes.splice(a,1),e.pipesCount-=1,1===e.pipesCount&&(e.pipes=e.pipes[0]),t.emit("unpipe",this,n)),this},k.prototype.on=function(t,e){var n=u.prototype.on.call(this,t,e);if("data"===t)!1!==this._readableState.flowing&&this.resume();else if("readable"===t){var r=this._readableState;r.endEmitted||r.readableListening||(r.readableListening=r.needReadable=!0,r.emittedReadable=!1,r.reading?r.length&&L(this):o.nextTick(M,this))}return n},k.prototype.addListener=k.prototype.on,k.prototype.resume=function(){var t=this._readableState;return t.flowing||(_("resume"),t.flowing=!0,U(this,t)),this},k.prototype.pause=function(){return _("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(_("pause"),this._readableState.flowing=!1,this.emit("pause")),this},k.prototype.wrap=function(t){var e=this,n=this._readableState,r=!1;for(var i in t.on("end",(function(){if(_("wrapped end"),n.decoder&&!n.ended){var t=n.decoder.end();t&&t.length&&e.push(t)}e.push(null)})),t.on("data",(function(i){if(_("wrapped data"),n.decoder&&(i=n.decoder.write(i)),(!n.objectMode||null!==i&&void 0!==i)&&(n.objectMode||i&&i.length)){var o=e.push(i);o||(r=!0,t.pause())}})),t)void 0===this[i]&&"function"===typeof t[i]&&(this[i]=function(e){return function(){return t[e].apply(t,arguments)}}(i));for(var o=0;o<m.length;o++)t.on(m[o],this.emit.bind(this,m[o]));return this._read=function(e){_("wrapped _read",e),r&&(r=!1,t.resume())},this},Object.defineProperty(k.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),k._fromList=z}).call(this,n(29),n(9)["window"],n(26))},function(t,e,n){t.exports=n(40)},function(t,e,n){"use strict";var r=n(55);function i(t,e){var n=this,i=this._readableState&&this._readableState.destroyed,o=this._writableState&&this._writableState.destroyed;return i||o?(e?e(t):t&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,r.nextTick(a,this,t)):r.nextTick(a,this,t)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(t||null,(function(t){!e&&t?n._writableState?n._writableState.errorEmitted||(n._writableState.errorEmitted=!0,r.nextTick(a,n,t)):r.nextTick(a,n,t):e&&e(t)})),this)}function o(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}function a(t,e){t.emit("error",e)}t.exports={destroy:i,undestroy:o}},function(t,e,n){"use strict";var r=n(343).Buffer,i=r.isEncoding||function(t){switch(t=""+t,t&&t.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function o(t){if(!t)return"utf8";var e;while(1)switch(t){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return t;default:if(e)return;t=(""+t).toLowerCase(),e=!0}}function a(t){var e=o(t);if("string"!==typeof e&&(r.isEncoding===i||!i(t)))throw new Error("Unknown encoding: "+t);return e||t}function s(t){var e;switch(this.encoding=a(t),this.encoding){case"utf16le":this.text=p,this.end=g,e=4;break;case"utf8":this.fillLast=f,e=4;break;case"base64":this.text=_,this.end=y,e=3;break;default:return this.write=b,void(this.end=w)}this.lastNeed=0,this.lastTotal=0,this.lastChar=r.allocUnsafe(e)}function l(t){return t<=127?0:t>>5===6?2:t>>4===14?3:t>>3===30?4:t>>6===2?-1:-2}function u(t,e,n){var r=e.length-1;if(r<n)return 0;var i=l(e[r]);return i>=0?(i>0&&(t.lastNeed=i-1),i):--r<n||-2===i?0:(i=l(e[r]),i>=0?(i>0&&(t.lastNeed=i-2),i):--r<n||-2===i?0:(i=l(e[r]),i>=0?(i>0&&(2===i?i=0:t.lastNeed=i-3),i):0))}function h(t,e,n){if(128!==(192&e[0]))return t.lastNeed=0,"\ufffd";if(t.lastNeed>1&&e.length>1){if(128!==(192&e[1]))return t.lastNeed=1,"\ufffd";if(t.lastNeed>2&&e.length>2&&128!==(192&e[2]))return t.lastNeed=2,"\ufffd"}}function f(t){var e=this.lastTotal-this.lastNeed,n=h(this,t,e);return void 0!==n?n:this.lastNeed<=t.length?(t.copy(this.lastChar,e,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(t.copy(this.lastChar,e,0,t.length),void(this.lastNeed-=t.length))}function c(t,e){var n=u(this,t,e);if(!this.lastNeed)return t.toString("utf8",e);this.lastTotal=n;var r=t.length-(n-this.lastNeed);return t.copy(this.lastChar,0,r),t.toString("utf8",e,r)}function d(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+"\ufffd":e}function p(t,e){if((t.length-e)%2===0){var n=t.toString("utf16le",e);if(n){var r=n.charCodeAt(n.length-1);if(r>=55296&&r<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1],n.slice(0,-1)}return n}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=t[t.length-1],t.toString("utf16le",e,t.length-1)}function g(t){var e=t&&t.length?this.write(t):"";if(this.lastNeed){var n=this.lastTotal-this.lastNeed;return e+this.lastChar.toString("utf16le",0,n)}return e}function _(t,e){var n=(t.length-e)%3;return 0===n?t.toString("base64",e):(this.lastNeed=3-n,this.lastTotal=3,1===n?this.lastChar[0]=t[t.length-1]:(this.lastChar[0]=t[t.length-2],this.lastChar[1]=t[t.length-1]),t.toString("base64",e,t.length-n))}function y(t){var e=t&&t.length?this.write(t):"";return this.lastNeed?e+this.lastChar.toString("base64",0,3-this.lastNeed):e}function b(t){return t.toString(this.encoding)}function w(t){return t&&t.length?this.write(t):""}e.StringDecoder=s,s.prototype.write=function(t){if(0===t.length)return"";var e,n;if(this.lastNeed){if(e=this.fillLast(t),void 0===e)return"";n=this.lastNeed,this.lastNeed=0}else n=0;return n<t.length?e?e+this.text(t,n):this.text(t,n):e||""},s.prototype.end=d,s.prototype.text=c,s.prototype.fillLast=function(t){if(this.lastNeed<=t.length)return t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);t.copy(this.lastChar,this.lastTotal-this.lastNeed,0,t.length),this.lastNeed-=t.length}},function(t,e,n){"use strict";t.exports=a;var r=n(44),i=Object.create(n(48));function o(t,e){var n=this._transformState;n.transforming=!1;var r=n.writecb;if(!r)return this.emit("error",new Error("write callback called multiple times"));n.writechunk=null,n.writecb=null,null!=e&&this.push(e),r(t);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function a(t){if(!(this instanceof a))return new a(t);r.call(this,t),this._transformState={afterTransform:o.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,t&&("function"===typeof t.transform&&(this._transform=t.transform),"function"===typeof t.flush&&(this._flush=t.flush)),this.on("prefinish",s)}function s(){var t=this;"function"===typeof this._flush?this._flush((function(e,n){l(t,e,n)})):l(this,null,null)}function l(t,e,n){if(e)return t.emit("error",e);if(null!=n&&t.push(n),t._writableState.length)throw new Error("Calling transform done when ws.length != 0");if(t._transformState.transforming)throw new Error("Calling transform done when still transforming");return t.push(null)}i.inherits=n(43),i.inherits(a,r),a.prototype.push=function(t,e){return this._transformState.needTransform=!1,r.prototype.push.call(this,t,e)},a.prototype._transform=function(t,e,n){throw new Error("_transform() is not implemented")},a.prototype._write=function(t,e,n){var r=this._transformState;if(r.writecb=n,r.writechunk=t,r.writeencoding=e,!r.transforming){var i=this._readableState;(r.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},a.prototype._read=function(t){var e=this._transformState;null!==e.writechunk&&e.writecb&&!e.transforming?(e.transforming=!0,this._transform(e.writechunk,e.writeencoding,e.afterTransform)):e.needTransform=!0},a.prototype._destroy=function(t,e){var n=this;r.prototype._destroy.call(this,t,(function(t){e(t),n.emit("close")}))}},function(t,e,n){"use strict";var r=Object.prototype.toString;t.exports=function(t){var e=r.call(t),n="[object Arguments]"===e;return n||(n="[object Array]"!==e&&null!==t&&"object"===typeof t&&"number"===typeof t.length&&t.length>=0&&"[object Function]"===r.call(t.callee)),n}},function(t,e,n){"use strict";t.exports=function(){if("function"!==typeof Symbol||"function"!==typeof Object.getOwnPropertySymbols)return!1;if("symbol"===typeof Symbol.iterator)return!0;var t={},e=Symbol("test"),n=Object(e);if("string"===typeof e)return!1;if("[object Symbol]"!==Object.prototype.toString.call(e))return!1;if("[object Symbol]"!==Object.prototype.toString.call(n))return!1;var r=42;for(e in t[e]=r,t)return!1;if("function"===typeof Object.keys&&0!==Object.keys(t).length)return!1;if("function"===typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(t).length)return!1;var i=Object.getOwnPropertySymbols(t);if(1!==i.length||i[0]!==e)return!1;if(!Object.prototype.propertyIsEnumerable.call(t,e))return!1;if("function"===typeof Object.getOwnPropertyDescriptor){var o=Object.getOwnPropertyDescriptor(t,e);if(o.value!==r||!0!==o.enumerable)return!1}return!0}},function(t,e,n){"use strict";var r=n(45),i=r("%Object.defineProperty%",!0),o=function(){if(i)try{return i({},"a",{value:1}),!0}catch(t){return!1}return!1};o.hasArrayLengthDefineBug=function(){if(!o())return null;try{return 1!==i([],"length",{value:1}).length}catch(t){return!0}},t.exports=o},function(t,e,n){"use strict";var r=n(45),i=r("%Object.getOwnPropertyDescriptor%",!0);if(i)try{i([],"length")}catch(t){i=null}t.exports=i},function(t,e,n){"use strict";function r(t,e,n,r){var i=65535&t|0,o=t>>>16&65535|0,a=0;while(0!==n){a=n>2e3?2e3:n,n-=a;do{i=i+e[r++]|0,o=o+i|0}while(--a);i%=65521,o%=65521}return i|o<<16|0}t.exports=r},function(t,e,n){"use strict";function r(){for(var t,e=[],n=0;n<256;n++){t=n;for(var r=0;r<8;r++)t=1&t?3988292384^t>>>1:t>>>1;e[n]=t}return e}var i=r();function o(t,e,n,r){var o=i,a=r+n;t^=-1;for(var s=r;s<a;s++)t=t>>>8^o[255&(t^e[s])];return-1^t}t.exports=o},function(t,e,n){"use strict";(function(e,r){let i=n(30),o=n(40),a=t.exports=function(){o.call(this),this._buffers=[],this._buffered=0,this._reads=[],this._paused=!1,this._encoding="utf8",this.writable=!0};i.inherits(a,o),a.prototype.read=function(t,n){this._reads.push({length:Math.abs(t),allowLess:t<0,func:n}),e.nextTick(function(){this._process(),this._paused&&this._reads&&this._reads.length>0&&(this._paused=!1,this.emit("drain"))}.bind(this))},a.prototype.write=function(t,e){if(!this.writable)return this.emit("error",new Error("Stream not writable")),!1;let n;return n=r.isBuffer(t)?t:r.from(t,e||this._encoding),this._buffers.push(n),this._buffered+=n.length,this._process(),this._reads&&0===this._reads.length&&(this._paused=!0),this.writable&&!this._paused},a.prototype.end=function(t,e){t&&this.write(t,e),this.writable=!1,this._buffers&&(0===this._buffers.length?this._end():(this._buffers.push(null),this._process()))},a.prototype.destroySoon=a.prototype.end,a.prototype._end=function(){this._reads.length>0&&this.emit("error",new Error("Unexpected end of input")),this.destroy()},a.prototype.destroy=function(){this._buffers&&(this.writable=!1,this._reads=null,this._buffers=null,this.emit("close"))},a.prototype._processReadAllowingLess=function(t){this._reads.shift();let e=this._buffers[0];e.length>t.length?(this._buffered-=t.length,this._buffers[0]=e.slice(t.length),t.func.call(this,e.slice(0,t.length))):(this._buffered-=e.length,this._buffers.shift(),t.func.call(this,e))},a.prototype._processRead=function(t){this._reads.shift();let e=0,n=0,i=r.alloc(t.length);while(e<t.length){let r=this._buffers[n++],o=Math.min(r.length,t.length-e);r.copy(i,e,0,o),e+=o,o!==r.length&&(this._buffers[--n]=r.slice(o))}n>0&&this._buffers.splice(0,n),this._buffered-=t.length,t.func.call(this,i)},a.prototype._process=function(){try{while(this._buffered>0&&this._reads&&this._reads.length>0){let t=this._reads[0];if(t.allowLess)this._processReadAllowingLess(t);else{if(!(this._buffered>=t.length))break;this._processRead(t)}}this._buffers&&!this.writable&&this._end()}catch(t){this.emit("error",t)}}}).call(this,n(26),n(19).Buffer)},function(t,e,n){"use strict";(function(e){let r=n(241),i=n(242);function o(t,e,n){let r=t*e;return 8!==n&&(r=Math.ceil(r/(8/n))),r}let a=t.exports=function(t,e){let n=t.width,i=t.height,a=t.interlace,s=t.bpp,l=t.depth;if(this.read=e.read,this.write=e.write,this.complete=e.complete,this._imageIndex=0,this._images=[],a){let t=r.getImagePasses(n,i);for(let e=0;e<t.length;e++)this._images.push({byteWidth:o(t[e].width,s,l),height:t[e].height,lineIndex:0})}else this._images.push({byteWidth:o(n,s,l),height:i,lineIndex:0});this._xComparison=8===l?s:16===l?2*s:1};a.prototype.start=function(){this.read(this._images[this._imageIndex].byteWidth+1,this._reverseFilterLine.bind(this))},a.prototype._unFilterType1=function(t,e,n){let r=this._xComparison,i=r-1;for(let o=0;o<n;o++){let n=t[1+o],a=o>i?e[o-r]:0;e[o]=n+a}},a.prototype._unFilterType2=function(t,e,n){let r=this._lastLine;for(let i=0;i<n;i++){let n=t[1+i],o=r?r[i]:0;e[i]=n+o}},a.prototype._unFilterType3=function(t,e,n){let r=this._xComparison,i=r-1,o=this._lastLine;for(let a=0;a<n;a++){let n=t[1+a],s=o?o[a]:0,l=a>i?e[a-r]:0,u=Math.floor((l+s)/2);e[a]=n+u}},a.prototype._unFilterType4=function(t,e,n){let r=this._xComparison,o=r-1,a=this._lastLine;for(let s=0;s<n;s++){let n=t[1+s],l=a?a[s]:0,u=s>o?e[s-r]:0,h=s>o&&a?a[s-r]:0,f=i(u,l,h);e[s]=n+f}},a.prototype._reverseFilterLine=function(t){let n,r=t[0],i=this._images[this._imageIndex],o=i.byteWidth;if(0===r)n=t.slice(1,o+1);else switch(n=e.alloc(o),r){case 1:this._unFilterType1(t,n,o);break;case 2:this._unFilterType2(t,n,o);break;case 3:this._unFilterType3(t,n,o);break;case 4:this._unFilterType4(t,n,o);break;default:throw new Error("Unrecognised filter type - "+r)}this.write(n),i.lineIndex++,i.lineIndex>=i.height?(this._lastLine=null,this._imageIndex++,i=this._images[this._imageIndex]):this._lastLine=n,i?this.read(i.byteWidth+1,this._reverseFilterLine.bind(this)):(this._lastLine=null,this.complete())}}).call(this,n(19).Buffer)},function(t,e,n){"use strict";let r=[{x:[0],y:[0]},{x:[4],y:[0]},{x:[0,4],y:[4]},{x:[2,6],y:[0,4]},{x:[0,2,4,6],y:[2,6]},{x:[1,3,5,7],y:[0,2,4,6]},{x:[0,1,2,3,4,5,6,7],y:[1,3,5,7]}];e.getImagePasses=function(t,e){let n=[],i=t%8,o=e%8,a=(t-i)/8,s=(e-o)/8;for(let t=0;t<r.length;t++){let e=r[t],l=a*e.x.length,u=s*e.y.length;for(let t=0;t<e.x.length;t++){if(!(e.x[t]<i))break;l++}for(let t=0;t<e.y.length;t++){if(!(e.y[t]<o))break;u++}l>0&&u>0&&n.push({width:l,height:u,index:t})}return n},e.getInterlaceIterator=function(t){return function(e,n,i){let o=e%r[i].x.length,a=(e-o)/r[i].x.length*8+r[i].x[o],s=n%r[i].y.length,l=(n-s)/r[i].y.length*8+r[i].y[s];return 4*a+l*t*4}}},function(t,e,n){"use strict";t.exports=function(t,e,n){let r=t+e-n,i=Math.abs(r-t),o=Math.abs(r-e),a=Math.abs(r-n);return i<=o&&i<=a?t:o<=a?e:n}},function(t,e,n){"use strict";(function(e){let r=n(50),i=n(244),o=t.exports=function(t,e){this._options=t,t.checkCRC=!1!==t.checkCRC,this._hasIHDR=!1,this._hasIEND=!1,this._emittedHeadersFinished=!1,this._palette=[],this._colorType=0,this._chunks={},this._chunks[r.TYPE_IHDR]=this._handleIHDR.bind(this),this._chunks[r.TYPE_IEND]=this._handleIEND.bind(this),this._chunks[r.TYPE_IDAT]=this._handleIDAT.bind(this),this._chunks[r.TYPE_PLTE]=this._handlePLTE.bind(this),this._chunks[r.TYPE_tRNS]=this._handleTRNS.bind(this),this._chunks[r.TYPE_gAMA]=this._handleGAMA.bind(this),this.read=e.read,this.error=e.error,this.metadata=e.metadata,this.gamma=e.gamma,this.transColor=e.transColor,this.palette=e.palette,this.parsed=e.parsed,this.inflateData=e.inflateData,this.finished=e.finished,this.simpleTransparency=e.simpleTransparency,this.headersFinished=e.headersFinished||function(){}};o.prototype.start=function(){this.read(r.PNG_SIGNATURE.length,this._parseSignature.bind(this))},o.prototype._parseSignature=function(t){let e=r.PNG_SIGNATURE;for(let n=0;n<e.length;n++)if(t[n]!==e[n])return void this.error(new Error("Invalid file signature"));this.read(8,this._parseChunkBegin.bind(this))},o.prototype._parseChunkBegin=function(t){let n=t.readUInt32BE(0),o=t.readUInt32BE(4),a="";for(let e=4;e<8;e++)a+=String.fromCharCode(t[e]);let s=Boolean(32&t[4]);if(this._hasIHDR||o===r.TYPE_IHDR){if(this._crc=new i,this._crc.write(e.from(a)),this._chunks[o])return this._chunks[o](n);s?this.read(n+4,this._skipChunk.bind(this)):this.error(new Error("Unsupported critical chunk type "+a))}else this.error(new Error("Expected IHDR on beggining"))},o.prototype._skipChunk=function(){this.read(8,this._parseChunkBegin.bind(this))},o.prototype._handleChunkEnd=function(){this.read(4,this._parseChunkEnd.bind(this))},o.prototype._parseChunkEnd=function(t){let e=t.readInt32BE(0),n=this._crc.crc32();this._options.checkCRC&&n!==e?this.error(new Error("Crc error - "+e+" - "+n)):this._hasIEND||this.read(8,this._parseChunkBegin.bind(this))},o.prototype._handleIHDR=function(t){this.read(t,this._parseIHDR.bind(this))},o.prototype._parseIHDR=function(t){this._crc.write(t);let e=t.readUInt32BE(0),n=t.readUInt32BE(4),i=t[8],o=t[9],a=t[10],s=t[11],l=t[12];if(8!==i&&4!==i&&2!==i&&1!==i&&16!==i)return void this.error(new Error("Unsupported bit depth "+i));if(!(o in r.COLORTYPE_TO_BPP_MAP))return void this.error(new Error("Unsupported color type"));if(0!==a)return void this.error(new Error("Unsupported compression method"));if(0!==s)return void this.error(new Error("Unsupported filter method"));if(0!==l&&1!==l)return void this.error(new Error("Unsupported interlace method"));this._colorType=o;let u=r.COLORTYPE_TO_BPP_MAP[this._colorType];this._hasIHDR=!0,this.metadata({width:e,height:n,depth:i,interlace:Boolean(l),palette:Boolean(o&r.COLORTYPE_PALETTE),color:Boolean(o&r.COLORTYPE_COLOR),alpha:Boolean(o&r.COLORTYPE_ALPHA),bpp:u,colorType:o}),this._handleChunkEnd()},o.prototype._handlePLTE=function(t){this.read(t,this._parsePLTE.bind(this))},o.prototype._parsePLTE=function(t){this._crc.write(t);let e=Math.floor(t.length/3);for(let n=0;n<e;n++)this._palette.push([t[3*n],t[3*n+1],t[3*n+2],255]);this.palette(this._palette),this._handleChunkEnd()},o.prototype._handleTRNS=function(t){this.simpleTransparency(),this.read(t,this._parseTRNS.bind(this))},o.prototype._parseTRNS=function(t){if(this._crc.write(t),this._colorType===r.COLORTYPE_PALETTE_COLOR){if(0===this._palette.length)return void this.error(new Error("Transparency chunk must be after palette"));if(t.length>this._palette.length)return void this.error(new Error("More transparent colors than palette size"));for(let e=0;e<t.length;e++)this._palette[e][3]=t[e];this.palette(this._palette)}this._colorType===r.COLORTYPE_GRAYSCALE&&this.transColor([t.readUInt16BE(0)]),this._colorType===r.COLORTYPE_COLOR&&this.transColor([t.readUInt16BE(0),t.readUInt16BE(2),t.readUInt16BE(4)]),this._handleChunkEnd()},o.prototype._handleGAMA=function(t){this.read(t,this._parseGAMA.bind(this))},o.prototype._parseGAMA=function(t){this._crc.write(t),this.gamma(t.readUInt32BE(0)/r.GAMMA_DIVISION),this._handleChunkEnd()},o.prototype._handleIDAT=function(t){this._emittedHeadersFinished||(this._emittedHeadersFinished=!0,this.headersFinished()),this.read(-t,this._parseIDAT.bind(this,t))},o.prototype._parseIDAT=function(t,e){if(this._crc.write(e),this._colorType===r.COLORTYPE_PALETTE_COLOR&&0===this._palette.length)throw new Error("Expected palette not found");this.inflateData(e);let n=t-e.length;n>0?this._handleIDAT(n):this._handleChunkEnd()},o.prototype._handleIEND=function(t){this.read(t,this._parseIEND.bind(this))},o.prototype._parseIEND=function(t){this._crc.write(t),this._hasIEND=!0,this._handleChunkEnd(),this.finished&&this.finished()}}).call(this,n(19).Buffer)},function(t,e,n){"use strict";let r=[];(function(){for(let t=0;t<256;t++){let e=t;for(let t=0;t<8;t++)1&e?e=3988292384^e>>>1:e>>>=1;r[t]=e}})();let i=t.exports=function(){this._crc=-1};i.prototype.write=function(t){for(let e=0;e<t.length;e++)this._crc=r[255&(this._crc^t[e])]^this._crc>>>8;return!0},i.prototype.crc32=function(){return-1^this._crc},i.crc32=function(t){let e=-1;for(let n=0;n<t.length;n++)e=r[255&(e^t[n])]^e>>>8;return-1^e}},function(t,e,n){"use strict";(function(t){let r=n(241),i=[function(){},function(t,e,n,r){if(r===e.length)throw new Error("Ran out of data");let i=e[r];t[n]=i,t[n+1]=i,t[n+2]=i,t[n+3]=255},function(t,e,n,r){if(r+1>=e.length)throw new Error("Ran out of data");let i=e[r];t[n]=i,t[n+1]=i,t[n+2]=i,t[n+3]=e[r+1]},function(t,e,n,r){if(r+2>=e.length)throw new Error("Ran out of data");t[n]=e[r],t[n+1]=e[r+1],t[n+2]=e[r+2],t[n+3]=255},function(t,e,n,r){if(r+3>=e.length)throw new Error("Ran out of data");t[n]=e[r],t[n+1]=e[r+1],t[n+2]=e[r+2],t[n+3]=e[r+3]}],o=[function(){},function(t,e,n,r){let i=e[0];t[n]=i,t[n+1]=i,t[n+2]=i,t[n+3]=r},function(t,e,n){let r=e[0];t[n]=r,t[n+1]=r,t[n+2]=r,t[n+3]=e[1]},function(t,e,n,r){t[n]=e[0],t[n+1]=e[1],t[n+2]=e[2],t[n+3]=r},function(t,e,n){t[n]=e[0],t[n+1]=e[1],t[n+2]=e[2],t[n+3]=e[3]}];function a(t,e){let n=[],r=0;function i(){if(r===t.length)throw new Error("Ran out of data");let i,o,a,s,l,u,h,f,c=t[r];switch(r++,e){default:throw new Error("unrecognised depth");case 16:h=t[r],r++,n.push((c<<8)+h);break;case 4:h=15&c,f=c>>4,n.push(f,h);break;case 2:l=3&c,u=c>>2&3,h=c>>4&3,f=c>>6&3,n.push(f,h,u,l);break;case 1:i=1&c,o=c>>1&1,a=c>>2&1,s=c>>3&1,l=c>>4&1,u=c>>5&1,h=c>>6&1,f=c>>7&1,n.push(f,h,u,l,s,a,o,i);break}}return{get:function(t){while(n.length<t)i();let e=n.slice(0,t);return n=n.slice(t),e},resetAfterLine:function(){n.length=0},end:function(){if(r!==t.length)throw new Error("extra data found")}}}function s(t,e,n,r,o,a){let s=t.width,l=t.height,u=t.index;for(let t=0;t<l;t++)for(let l=0;l<s;l++){let s=n(l,t,u);i[r](e,o,s,a),a+=r}return a}function l(t,e,n,r,i,a){let s=t.width,l=t.height,u=t.index;for(let t=0;t<l;t++){for(let l=0;l<s;l++){let s=i.get(r),h=n(l,t,u);o[r](e,s,h,a)}i.resetAfterLine()}}e.dataToBitMap=function(e,n){let i,o,u=n.width,h=n.height,f=n.depth,c=n.bpp,d=n.interlace;8!==f&&(i=a(e,f)),o=f<=8?t.alloc(u*h*4):new Uint16Array(u*h*4);let p,g,_=Math.pow(2,f)-1,y=0;if(d)p=r.getImagePasses(u,h),g=r.getInterlaceIterator(u,h);else{let t=0;g=function(){let e=t;return t+=4,e},p=[{width:u,height:h}]}for(let t=0;t<p.length;t++)8===f?y=s(p[t],o,g,c,e,y):l(p[t],o,g,c,i,_);if(8===f){if(y!==e.length)throw new Error("extra data found")}else i.end();return o}}).call(this,n(19).Buffer)},function(t,e,n){"use strict";(function(e){function n(t,e,n,r,i){let o=0;for(let a=0;a<r;a++)for(let r=0;r<n;r++){let n=i[t[o]];if(!n)throw new Error("index "+t[o]+" not in palette");for(let t=0;t<4;t++)e[o+t]=n[t];o+=4}}function r(t,e,n,r,i){let o=0;for(let a=0;a<r;a++)for(let r=0;r<n;r++){let n=!1;if(1===i.length?i[0]===t[o]&&(n=!0):i[0]===t[o]&&i[1]===t[o+1]&&i[2]===t[o+2]&&(n=!0),n)for(let t=0;t<4;t++)e[o+t]=0;o+=4}}function i(t,e,n,r,i){let o=255,a=Math.pow(2,i)-1,s=0;for(let i=0;i<r;i++)for(let r=0;r<n;r++){for(let n=0;n<4;n++)e[s+n]=Math.floor(t[s+n]*o/a+.5);s+=4}}t.exports=function(t,o){let a=o.depth,s=o.width,l=o.height,u=o.colorType,h=o.transColor,f=o.palette,c=t;return 3===u?n(t,c,s,l,f):(h&&r(t,c,s,l,h),8!==a&&(16===a&&(c=e.alloc(s*l*4)),i(t,c,s,l,a))),c}}).call(this,n(19).Buffer)},function(t,e,n){"use strict";(function(e){let r=n(50),i=n(244),o=n(373),a=n(374),s=n(49),l=t.exports=function(t){if(this._options=t,t.deflateChunkSize=t.deflateChunkSize||32768,t.deflateLevel=null!=t.deflateLevel?t.deflateLevel:9,t.deflateStrategy=null!=t.deflateStrategy?t.deflateStrategy:3,t.inputHasAlpha=null==t.inputHasAlpha||t.inputHasAlpha,t.deflateFactory=t.deflateFactory||s.createDeflate,t.bitDepth=t.bitDepth||8,t.colorType="number"===typeof t.colorType?t.colorType:r.COLORTYPE_COLOR_ALPHA,t.inputColorType="number"===typeof t.inputColorType?t.inputColorType:r.COLORTYPE_COLOR_ALPHA,-1===[r.COLORTYPE_GRAYSCALE,r.COLORTYPE_COLOR,r.COLORTYPE_COLOR_ALPHA,r.COLORTYPE_ALPHA].indexOf(t.colorType))throw new Error("option color type:"+t.colorType+" is not supported at present");if(-1===[r.COLORTYPE_GRAYSCALE,r.COLORTYPE_COLOR,r.COLORTYPE_COLOR_ALPHA,r.COLORTYPE_ALPHA].indexOf(t.inputColorType))throw new Error("option input color type:"+t.inputColorType+" is not supported at present");if(8!==t.bitDepth&&16!==t.bitDepth)throw new Error("option bit depth:"+t.bitDepth+" is not supported at present")};l.prototype.getDeflateOptions=function(){return{chunkSize:this._options.deflateChunkSize,level:this._options.deflateLevel,strategy:this._options.deflateStrategy}},l.prototype.createDeflate=function(){return this._options.deflateFactory(this.getDeflateOptions())},l.prototype.filterData=function(t,e,n){let i=o(t,e,n,this._options),s=r.COLORTYPE_TO_BPP_MAP[this._options.colorType],l=a(i,e,n,this._options,s);return l},l.prototype._packChunk=function(t,n){let r=n?n.length:0,o=e.alloc(r+12);return o.writeUInt32BE(r,0),o.writeUInt32BE(t,4),n&&n.copy(o,8),o.writeInt32BE(i.crc32(o.slice(4,o.length-4)),o.length-4),o},l.prototype.packGAMA=function(t){let n=e.alloc(4);return n.writeUInt32BE(Math.floor(t*r.GAMMA_DIVISION),0),this._packChunk(r.TYPE_gAMA,n)},l.prototype.packIHDR=function(t,n){let i=e.alloc(13);return i.writeUInt32BE(t,0),i.writeUInt32BE(n,4),i[8]=this._options.bitDepth,i[9]=this._options.colorType,i[10]=0,i[11]=0,i[12]=0,this._packChunk(r.TYPE_IHDR,i)},l.prototype.packIDAT=function(t){return this._packChunk(r.TYPE_IDAT,t)},l.prototype.packIEND=function(){return this._packChunk(r.TYPE_IEND,null)}}).call(this,n(19).Buffer)},function(t,e,n){"use strict";let r=t.exports=function(t){this._buffer=t,this._reads=[]};r.prototype.read=function(t,e){this._reads.push({length:Math.abs(t),allowLess:t<0,func:e})},r.prototype.process=function(){while(this._reads.length>0&&this._buffer.length){let t=this._reads[0];if(!this._buffer.length||!(this._buffer.length>=t.length||t.allowLess))break;{this._reads.shift();let e=this._buffer;this._buffer=e.slice(t.length),t.func.call(this,e.slice(0,t.length))}}return this._reads.length>0?new Error("There are some read requests waitng on finished stream"):this._buffer.length>0?new Error("unrecognised content at end of stream"):void 0}},function(t,e,n){const r=n(57);function i(t,e){const n=t.a/255,r=e+'="'+t.hex+'"';return n<1?r+" "+e+'-opacity="'+n.toFixed(2).slice(1)+'"':r}function o(t,e,n){let r=t+e;return"undefined"!==typeof n&&(r+=" "+n),r}function a(t,e,n){let r="",i=0,a=!1,s=0;for(let l=0;l<t.length;l++){const u=Math.floor(l%e),h=Math.floor(l/e);u||a||(a=!0),t[l]?(s++,l>0&&u>0&&t[l-1]||(r+=a?o("M",u+n,.5+h+n):o("m",i,0),i=0,a=!1),u+1<e&&t[l+1]||(r+=o("h",s),s=0)):i++}return r}e.render=function(t,e,n){const o=r.getOptions(e),s=t.modules.size,l=t.modules.data,u=s+2*o.margin,h=o.color.light.a?"<path "+i(o.color.light,"fill")+' d="M0 0h'+u+"v"+u+'H0z"/>':"",f="<path "+i(o.color.dark,"stroke")+' d="'+a(l,s,o.margin)+'"/>',c='viewBox="0 0 '+u+" "+u+'"',d=o.width?'width="'+o.width+'" height="'+o.width+'" ':"",p='<svg xmlns="http://www.w3.org/2000/svg" '+d+c+' shape-rendering="crispEdges">'+h+f+"</svg>\n";return"function"===typeof n&&n(null,p),p}},,,,,,,,,,,,function(t,e,n){t.exports=n(315)},function(t,e){var n=1e3,r=60*n,i=60*r,o=24*i,a=7*o,s=365.25*o;function l(t){if(t=String(t),!(t.length>100)){var e=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(t);if(e){var l=parseFloat(e[1]),u=(e[2]||"ms").toLowerCase();switch(u){case"years":case"year":case"yrs":case"yr":case"y":return l*s;case"weeks":case"week":case"w":return l*a;case"days":case"day":case"d":return l*o;case"hours":case"hour":case"hrs":case"hr":case"h":return l*i;case"minutes":case"minute":case"mins":case"min":case"m":return l*r;case"seconds":case"second":case"secs":case"sec":case"s":return l*n;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return l;default:return}}}}function u(t){var e=Math.abs(t);return e>=o?Math.round(t/o)+"d":e>=i?Math.round(t/i)+"h":e>=r?Math.round(t/r)+"m":e>=n?Math.round(t/n)+"s":t+"ms"}function h(t){var e=Math.abs(t);return e>=o?f(t,e,o,"day"):e>=i?f(t,e,i,"hour"):e>=r?f(t,e,r,"minute"):e>=n?f(t,e,n,"second"):t+" ms"}function f(t,e,n,r){var i=e>=1.5*n;return Math.round(t/n)+" "+r+(i?"s":"")}t.exports=function(t,e){e=e||{};var n=typeof t;if("string"===n&&t.length>0)return l(t);if("number"===n&&isFinite(t))return e.long?h(t):u(t);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))}},,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,,function(t,e,n){const r=n(221),i=n(222),o=n(333),a=n(380),s=n(381),l=n(384);function u(t,e,n){if("undefined"===typeof t)throw new Error("String required as first argument");if("undefined"===typeof n&&(n=e,e={}),"function"!==typeof n){if(!r())throw new Error("Callback required as last argument");e=n||{},n=null}return{opts:e,cb:n}}function h(t){return t.slice(2+(t.lastIndexOf(".")-1>>>0)).toLowerCase()}function f(t){switch(t){case"svg":return l;case"txt":case"utf8":return a;case"png":case"image/png":default:return o}}function c(t){switch(t){case"svg":return l;case"terminal":return s;case"utf8":default:return a}}function d(t,e,n){if(!n.cb)return new Promise((function(r,o){try{const a=i.create(e,n.opts);return t(a,n.opts,(function(t,e){return t?o(t):r(e)}))}catch(t){o(t)}}));try{const r=i.create(e,n.opts);return t(r,n.opts,n.cb)}catch(t){n.cb(t)}}e.create=i.create,e.toCanvas=n(385).toCanvas,e.toString=function(t,e,n){const r=u(t,e,n),i=r.opts?r.opts.type:void 0,o=c(i);return d(o.render,t,r)},e.toDataURL=function(t,e,n){const r=u(t,e,n),i=f(r.opts.type);return d(i.renderToDataURL,t,r)},e.toBuffer=function(t,e,n){const r=u(t,e,n),i=f(r.opts.type);return d(i.renderToBuffer,t,r)},e.toFile=function(t,e,n,i){if("string"!==typeof t||"string"!==typeof e&&"object"!==typeof e)throw new Error("Invalid argument");if(arguments.length<3&&!r())throw new Error("Too few arguments provided");const o=u(e,n,i),a=o.opts.type||h(t),s=f(a),l=s.renderToFile.bind(null,t);return d(l,e,o)},e.toFileStream=function(t,e,n){if(arguments.length<2)throw new Error("Too few arguments provided");const r=u(e,n,t.emit.bind(t,"error")),i=f("png"),o=i.renderToFileStream.bind(null,t);d(o,e,r)}},function(t,e){function n(){this.buffer=[],this.length=0}n.prototype={get:function(t){const e=Math.floor(t/8);return 1===(this.buffer[e]>>>7-t%8&1)},put:function(t,e){for(let n=0;n<e;n++)this.putBit(1===(t>>>e-n-1&1))},getLengthInBits:function(){return this.length},putBit:function(t){const e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}},t.exports=n},function(t,e){function n(t){if(!t||t<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}n.prototype.set=function(t,e,n,r){const i=t*this.size+e;this.data[i]=n,r&&(this.reservedBit[i]=!0)},n.prototype.get=function(t,e){return this.data[t*this.size+e]},n.prototype.xor=function(t,e,n){this.data[t*this.size+e]^=n},n.prototype.isReserved=function(t,e){return this.reservedBit[t*this.size+e]},t.exports=n},function(t,e,n){const r=n(38).getSymbolSize;e.getRowColCoords=function(t){if(1===t)return[];const e=Math.floor(t/7)+2,n=r(t),i=145===n?26:2*Math.ceil((n-13)/(2*e-2)),o=[n-7];for(let t=1;t<e-1;t++)o[t]=o[t-1]-i;return o.push(6),o.reverse()},e.getPositions=function(t){const n=[],r=e.getRowColCoords(t),i=r.length;for(let t=0;t<i;t++)for(let e=0;e<i;e++)0===t&&0===e||0===t&&e===i-1||t===i-1&&0===e||n.push([r[t],r[e]]);return n}},function(t,e,n){const r=n(38).getSymbolSize,i=7;e.getPositions=function(t){const e=r(t);return[[0,0],[e-i,0],[0,e-i]]}},function(t,e){e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};const n={N1:3,N2:3,N3:40,N4:10};function r(t,n,r){switch(t){case e.Patterns.PATTERN000:return(n+r)%2===0;case e.Patterns.PATTERN001:return n%2===0;case e.Patterns.PATTERN010:return r%3===0;case e.Patterns.PATTERN011:return(n+r)%3===0;case e.Patterns.PATTERN100:return(Math.floor(n/2)+Math.floor(r/3))%2===0;case e.Patterns.PATTERN101:return n*r%2+n*r%3===0;case e.Patterns.PATTERN110:return(n*r%2+n*r%3)%2===0;case e.Patterns.PATTERN111:return(n*r%3+(n+r)%2)%2===0;default:throw new Error("bad maskPattern:"+t)}}e.isValid=function(t){return null!=t&&""!==t&&!isNaN(t)&&t>=0&&t<=7},e.from=function(t){return e.isValid(t)?parseInt(t,10):void 0},e.getPenaltyN1=function(t){const e=t.size;let r=0,i=0,o=0,a=null,s=null;for(let l=0;l<e;l++){i=o=0,a=s=null;for(let u=0;u<e;u++){let e=t.get(l,u);e===a?i++:(i>=5&&(r+=n.N1+(i-5)),a=e,i=1),e=t.get(u,l),e===s?o++:(o>=5&&(r+=n.N1+(o-5)),s=e,o=1)}i>=5&&(r+=n.N1+(i-5)),o>=5&&(r+=n.N1+(o-5))}return r},e.getPenaltyN2=function(t){const e=t.size;let r=0;for(let n=0;n<e-1;n++)for(let i=0;i<e-1;i++){const e=t.get(n,i)+t.get(n,i+1)+t.get(n+1,i)+t.get(n+1,i+1);4!==e&&0!==e||r++}return r*n.N2},e.getPenaltyN3=function(t){const e=t.size;let r=0,i=0,o=0;for(let n=0;n<e;n++){i=o=0;for(let a=0;a<e;a++)i=i<<1&2047|t.get(n,a),a>=10&&(1488===i||93===i)&&r++,o=o<<1&2047|t.get(a,n),a>=10&&(1488===o||93===o)&&r++}return r*n.N3},e.getPenaltyN4=function(t){let e=0;const r=t.data.length;for(let n=0;n<r;n++)e+=t.data[n];const i=Math.abs(Math.ceil(100*e/r/5)-10);return i*n.N4},e.applyMask=function(t,e){const n=e.size;for(let i=0;i<n;i++)for(let o=0;o<n;o++)e.isReserved(o,i)||e.xor(o,i,r(t,o,i))},e.getBestMask=function(t,n){const r=Object.keys(e.Patterns).length;let i=0,o=1/0;for(let a=0;a<r;a++){n(a),e.applyMask(a,t);const r=e.getPenaltyN1(t)+e.getPenaltyN2(t)+e.getPenaltyN3(t)+e.getPenaltyN4(t);e.applyMask(a,t),r<o&&(o=r,i=a)}return i}},function(t,e,n){const r=n(322);function i(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}i.prototype.initialize=function(t){this.degree=t,this.genPoly=r.generateECPolynomial(this.degree)},i.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");const e=new Uint8Array(t.length+this.degree);e.set(t);const n=r.mod(e,this.genPoly),i=this.degree-n.length;if(i>0){const t=new Uint8Array(this.degree);return t.set(n,i),t}return n},t.exports=i},function(t,e,n){const r=n(323);e.mul=function(t,e){const n=new Uint8Array(t.length+e.length-1);for(let i=0;i<t.length;i++)for(let o=0;o<e.length;o++)n[i+o]^=r.mul(t[i],e[o]);return n},e.mod=function(t,e){let n=new Uint8Array(t);while(n.length-e.length>=0){const t=n[0];for(let i=0;i<e.length;i++)n[i]^=r.mul(e[i],t);let i=0;while(i<n.length&&0===n[i])i++;n=n.slice(i)}return n},e.generateECPolynomial=function(t){let n=new Uint8Array([1]);for(let i=0;i<t;i++)n=e.mul(n,new Uint8Array([1,r.exp(i)]));return n}},function(t,e){const n=new Uint8Array(512),r=new Uint8Array(256);(function(){let t=1;for(let e=0;e<255;e++)n[e]=t,r[t]=e,t<<=1,256&t&&(t^=285);for(let t=255;t<512;t++)n[t]=n[t-255]})(),e.log=function(t){if(t<1)throw new Error("log("+t+")");return r[t]},e.exp=function(t){return n[t]},e.mul=function(t,e){return 0===t||0===e?0:n[r[t]+r[e]]}},function(t,e,n){const r=n(38),i=n(223),o=n(68),a=n(39),s=n(224),l=7973,u=r.getBCHDigit(l);function h(t,n,r){for(let i=1;i<=40;i++)if(n<=e.getCapacity(i,r,t))return i}function f(t,e){return a.getCharCountIndicator(t,e)+4}function c(t,e){let n=0;return t.forEach((function(t){const r=f(t.mode,e);n+=r+t.getBitsLength()})),n}function d(t,n){for(let r=1;r<=40;r++){const i=c(t,r);if(i<=e.getCapacity(r,n,a.MIXED))return r}}e.from=function(t,e){return s.isValid(t)?parseInt(t,10):e},e.getCapacity=function(t,e,n){if(!s.isValid(t))throw new Error("Invalid QR Code version");"undefined"===typeof n&&(n=a.BYTE);const o=r.getSymbolTotalCodewords(t),l=i.getTotalCodewordsCount(t,e),u=8*(o-l);if(n===a.MIXED)return u;const h=u-f(n,t);switch(n){case a.NUMERIC:return Math.floor(h/10*3);case a.ALPHANUMERIC:return Math.floor(h/11*2);case a.KANJI:return Math.floor(h/13);case a.BYTE:default:return Math.floor(h/8)}},e.getBestVersionForData=function(t,e){let n;const r=o.from(e,o.M);if(Array.isArray(t)){if(t.length>1)return d(t,r);if(0===t.length)return 1;n=t[0]}else n=t;return h(n.mode,n.getLength(),r)},e.getEncodedBits=function(t){if(!s.isValid(t)||t<7)throw new Error("Invalid QR Code version");let e=t<<12;while(r.getBCHDigit(e)-u>=0)e^=l<<r.getBCHDigit(e)-u;return t<<12|e}},function(t,e,n){const r=n(38),i=1335,o=21522,a=r.getBCHDigit(i);e.getEncodedBits=function(t,e){const n=t.bit<<3|e;let s=n<<10;while(r.getBCHDigit(s)-a>=0)s^=i<<r.getBCHDigit(s)-a;return(n<<10|s)^o}},function(t,e,n){const r=n(39),i=n(327),o=n(328),a=n(329),s=n(331),l=n(225),u=n(38),h=n(332);function f(t){return unescape(encodeURIComponent(t)).length}function c(t,e,n){const r=[];let i;while(null!==(i=t.exec(n)))r.push({data:i[0],index:i.index,mode:e,length:i[0].length});return r}function d(t){const e=c(l.NUMERIC,r.NUMERIC,t),n=c(l.ALPHANUMERIC,r.ALPHANUMERIC,t);let i,o;u.isKanjiModeEnabled()?(i=c(l.BYTE,r.BYTE,t),o=c(l.KANJI,r.KANJI,t)):(i=c(l.BYTE_KANJI,r.BYTE,t),o=[]);const a=e.concat(n,i,o);return a.sort((function(t,e){return t.index-e.index})).map((function(t){return{data:t.data,mode:t.mode,length:t.length}}))}function p(t,e){switch(e){case r.NUMERIC:return i.getBitsLength(t);case r.ALPHANUMERIC:return o.getBitsLength(t);case r.KANJI:return s.getBitsLength(t);case r.BYTE:return a.getBitsLength(t)}}function g(t){return t.reduce((function(t,e){const n=t.length-1>=0?t[t.length-1]:null;return n&&n.mode===e.mode?(t[t.length-1].data+=e.data,t):(t.push(e),t)}),[])}function _(t){const e=[];for(let n=0;n<t.length;n++){const i=t[n];switch(i.mode){case r.NUMERIC:e.push([i,{data:i.data,mode:r.ALPHANUMERIC,length:i.length},{data:i.data,mode:r.BYTE,length:i.length}]);break;case r.ALPHANUMERIC:e.push([i,{data:i.data,mode:r.BYTE,length:i.length}]);break;case r.KANJI:e.push([i,{data:i.data,mode:r.BYTE,length:f(i.data)}]);break;case r.BYTE:e.push([{data:i.data,mode:r.BYTE,length:f(i.data)}])}}return e}function y(t,e){const n={},i={start:{}};let o=["start"];for(let a=0;a<t.length;a++){const s=t[a],l=[];for(let t=0;t<s.length;t++){const u=s[t],h=""+a+t;l.push(h),n[h]={node:u,lastCount:0},i[h]={};for(let t=0;t<o.length;t++){const a=o[t];n[a]&&n[a].node.mode===u.mode?(i[a][h]=p(n[a].lastCount+u.length,u.mode)-p(n[a].lastCount,u.mode),n[a].lastCount+=u.length):(n[a]&&(n[a].lastCount=u.length),i[a][h]=p(u.length,u.mode)+4+r.getCharCountIndicator(u.mode,e))}}o=l}for(let t=0;t<o.length;t++)i[o[t]].end=0;return{map:i,table:n}}function b(t,e){let n;const l=r.getBestModeForData(t);if(n=r.from(e,l),n!==r.BYTE&&n.bit<l.bit)throw new Error('"'+t+'" cannot be encoded with mode '+r.toString(n)+".\n Suggested mode is: "+r.toString(l));switch(n!==r.KANJI||u.isKanjiModeEnabled()||(n=r.BYTE),n){case r.NUMERIC:return new i(t);case r.ALPHANUMERIC:return new o(t);case r.KANJI:return new s(t);case r.BYTE:return new a(t)}}e.fromArray=function(t){return t.reduce((function(t,e){return"string"===typeof e?t.push(b(e,null)):e.data&&t.push(b(e.data,e.mode)),t}),[])},e.fromString=function(t,n){const r=d(t,u.isKanjiModeEnabled()),i=_(r),o=y(i,n),a=h.find_path(o.map,"start","end"),s=[];for(let t=1;t<a.length-1;t++)s.push(o.table[a[t]].node);return e.fromArray(g(s))},e.rawSplit=function(t){return e.fromArray(d(t,u.isKanjiModeEnabled()))}},function(t,e,n){const r=n(39);function i(t){this.mode=r.NUMERIC,this.data=t.toString()}i.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(t){let e,n,r;for(e=0;e+3<=this.data.length;e+=3)n=this.data.substr(e,3),r=parseInt(n,10),t.put(r,10);const i=this.data.length-e;i>0&&(n=this.data.substr(e),r=parseInt(n,10),t.put(r,3*i+1))},t.exports=i},function(t,e,n){const r=n(39),i=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function o(t){this.mode=r.ALPHANUMERIC,this.data=t}o.getBitsLength=function(t){return 11*Math.floor(t/2)+t%2*6},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){let e;for(e=0;e+2<=this.data.length;e+=2){let n=45*i.indexOf(this.data[e]);n+=i.indexOf(this.data[e+1]),t.put(n,11)}this.data.length%2&&t.put(i.indexOf(this.data[e]),6)},t.exports=o},function(t,e,n){const r=n(330),i=n(39);function o(t){this.mode=i.BYTE,"string"===typeof t&&(t=r(t)),this.data=new Uint8Array(t)}o.getBitsLength=function(t){return 8*t},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){for(let e=0,n=this.data.length;e<n;e++)t.put(this.data[e],8)},t.exports=o},function(t,e,n){"use strict";t.exports=function(t){for(var e=[],n=t.length,r=0;r<n;r++){var i=t.charCodeAt(r);if(i>=55296&&i<=56319&&n>r+1){var o=t.charCodeAt(r+1);o>=56320&&o<=57343&&(i=1024*(i-55296)+o-56320+65536,r+=1)}i<128?e.push(i):i<2048?(e.push(i>>6|192),e.push(63&i|128)):i<55296||i>=57344&&i<65536?(e.push(i>>12|224),e.push(i>>6&63|128),e.push(63&i|128)):i>=65536&&i<=1114111?(e.push(i>>18|240),e.push(i>>12&63|128),e.push(i>>6&63|128),e.push(63&i|128)):e.push(239,191,189)}return new Uint8Array(e).buffer}},function(t,e,n){const r=n(39),i=n(38);function o(t){this.mode=r.KANJI,this.data=t}o.getBitsLength=function(t){return 13*t},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){let e;for(e=0;e<this.data.length;e++){let n=i.toSJIS(this.data[e]);if(n>=33088&&n<=40956)n-=33088;else{if(!(n>=57408&&n<=60351))throw new Error("Invalid SJIS character: "+this.data[e]+"\nMake sure your charset is UTF-8");n-=49472}n=192*(n>>>8&255)+(255&n),t.put(n,13)}},t.exports=o},function(t,e,n){"use strict";var r={single_source_shortest_paths:function(t,e,n){var i={},o={};o[e]=0;var a,s,l,u,h,f,c,d,p,g=r.PriorityQueue.make();g.push(e,0);while(!g.empty())for(l in a=g.pop(),s=a.value,u=a.cost,h=t[s]||{},h)h.hasOwnProperty(l)&&(f=h[l],c=u+f,d=o[l],p="undefined"===typeof o[l],(p||d>c)&&(o[l]=c,g.push(l,c),i[l]=s));if("undefined"!==typeof n&&"undefined"===typeof o[n]){var _=["Could not find a path from ",e," to ",n,"."].join("");throw new Error(_)}return i},extract_shortest_path_from_predecessor_list:function(t,e){var n=[],r=e;while(r)n.push(r),t[r],r=t[r];return n.reverse(),n},find_path:function(t,e,n){var i=r.single_source_shortest_paths(t,e,n);return r.extract_shortest_path_from_predecessor_list(i,n)},PriorityQueue:{make:function(t){var e,n=r.PriorityQueue,i={};for(e in t=t||{},n)n.hasOwnProperty(e)&&(i[e]=n[e]);return i.queue=[],i.sorter=t.sorter||n.default_sorter,i},default_sorter:function(t,e){return t.cost-e.cost},push:function(t,e){var n={value:t,cost:e};this.queue.push(n),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};t.exports=r},function(t,e,n){(function(t){const r=n(69),i=n(336).PNG,o=n(57);e.render=function(t,e){const n=o.getOptions(e),r=n.rendererOpts,a=o.getImageWidth(t.modules.size,n);r.width=a,r.height=a;const s=new i(r);return o.qrToImageData(s.data,t,n),s},e.renderToDataURL=function(t,n,r){"undefined"===typeof r&&(r=n,n=void 0),e.renderToBuffer(t,n,(function(t,e){t&&r(t);let n="data:image/png;base64,";n+=e.toString("base64"),r(null,n)}))},e.renderToBuffer=function(n,r,i){"undefined"===typeof i&&(i=r,r=void 0);const o=e.render(n,r),a=[];o.on("error",i),o.on("data",(function(t){a.push(t)})),o.on("end",(function(){i(null,t.concat(a))})),o.pack()},e.renderToFile=function(t,n,i,o){"undefined"===typeof o&&(o=i,i=void 0);let a=!1;const s=(...t)=>{a||(a=!0,o.apply(null,t))},l=r.createWriteStream(t);l.on("error",s),l.on("close",s),e.renderToFileStream(l,n,i)},e.renderToFileStream=function(t,n,r){const i=e.render(n,r);i.pack().pipe(t)}}).call(this,n(19).Buffer)},function(t,e,n){"use strict";e.byteLength=h,e.toByteArray=c,e.fromByteArray=g;for(var r=[],i=[],o="undefined"!==typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",s=0,l=a.length;s<l;++s)r[s]=a[s],i[a.charCodeAt(s)]=s;function u(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=t.indexOf("=");-1===n&&(n=e);var r=n===e?0:4-n%4;return[n,r]}function h(t){var e=u(t),n=e[0],r=e[1];return 3*(n+r)/4-r}function f(t,e,n){return 3*(e+n)/4-n}function c(t){var e,n,r=u(t),a=r[0],s=r[1],l=new o(f(t,a,s)),h=0,c=s>0?a-4:a;for(n=0;n<c;n+=4)e=i[t.charCodeAt(n)]<<18|i[t.charCodeAt(n+1)]<<12|i[t.charCodeAt(n+2)]<<6|i[t.charCodeAt(n+3)],l[h++]=e>>16&255,l[h++]=e>>8&255,l[h++]=255&e;return 2===s&&(e=i[t.charCodeAt(n)]<<2|i[t.charCodeAt(n+1)]>>4,l[h++]=255&e),1===s&&(e=i[t.charCodeAt(n)]<<10|i[t.charCodeAt(n+1)]<<4|i[t.charCodeAt(n+2)]>>2,l[h++]=e>>8&255,l[h++]=255&e),l}function d(t){return r[t>>18&63]+r[t>>12&63]+r[t>>6&63]+r[63&t]}function p(t,e,n){for(var r,i=[],o=e;o<n;o+=3)r=(t[o]<<16&16711680)+(t[o+1]<<8&65280)+(255&t[o+2]),i.push(d(r));return i.join("")}function g(t){for(var e,n=t.length,i=n%3,o=[],a=16383,s=0,l=n-i;s<l;s+=a)o.push(p(t,s,s+a>l?l:s+a));return 1===i?(e=t[n-1],o.push(r[e>>2]+r[e<<4&63]+"==")):2===i&&(e=(t[n-2]<<8)+t[n-1],o.push(r[e>>10]+r[e>>4&63]+r[e<<2&63]+"=")),o.join("")}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},function(t,e){e.read=function(t,e,n,r,i){var o,a,s=8*i-r-1,l=(1<<s)-1,u=l>>1,h=-7,f=n?i-1:0,c=n?-1:1,d=t[e+f];for(f+=c,o=d&(1<<-h)-1,d>>=-h,h+=s;h>0;o=256*o+t[e+f],f+=c,h-=8);for(a=o&(1<<-h)-1,o>>=-h,h+=r;h>0;a=256*a+t[e+f],f+=c,h-=8);if(0===o)o=1-u;else{if(o===l)return a?NaN:1/0*(d?-1:1);a+=Math.pow(2,r),o-=u}return(d?-1:1)*a*Math.pow(2,o-r)},e.write=function(t,e,n,r,i,o){var a,s,l,u=8*o-i-1,h=(1<<u)-1,f=h>>1,c=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=r?0:o-1,p=r?1:-1,g=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(s=isNaN(e)?1:0,a=h):(a=Math.floor(Math.log(e)/Math.LN2),e*(l=Math.pow(2,-a))<1&&(a--,l*=2),e+=a+f>=1?c/l:c*Math.pow(2,1-f),e*l>=2&&(a++,l/=2),a+f>=h?(s=0,a=h):a+f>=1?(s=(e*l-1)*Math.pow(2,i),a+=f):(s=e*Math.pow(2,f-1)*Math.pow(2,i),a=0));i>=8;t[n+d]=255&s,d+=p,s/=256,i-=8);for(a=a<<i|s,u+=i;u>0;t[n+d]=255&a,d+=p,a/=256,u-=8);t[n+d-p]|=128*g}},function(t,e,n){"use strict";(function(t,r){let i=n(30),o=n(40),a=n(349),s=n(372),l=n(375),u=e.PNG=function(e){o.call(this),e=e||{},this.width=0|e.width,this.height=0|e.height,this.data=this.width>0&&this.height>0?t.alloc(4*this.width*this.height):null,e.fill&&this.data&&this.data.fill(0),this.gamma=0,this.readable=this.writable=!0,this._parser=new a(e),this._parser.on("error",this.emit.bind(this,"error")),this._parser.on("close",this._handleClose.bind(this)),this._parser.on("metadata",this._metadata.bind(this)),this._parser.on("gamma",this._gamma.bind(this)),this._parser.on("parsed",function(t){this.data=t,this.emit("parsed",t)}.bind(this)),this._packer=new s(e),this._packer.on("data",this.emit.bind(this,"data")),this._packer.on("end",this.emit.bind(this,"end")),this._parser.on("close",this._handleClose.bind(this)),this._packer.on("error",this.emit.bind(this,"error"))};i.inherits(u,o),u.sync=l,u.prototype.pack=function(){return this.data&&this.data.length?(r.nextTick(function(){this._packer.pack(this.data,this.width,this.height,this.gamma)}.bind(this)),this):(this.emit("error","No data provided"),this)},u.prototype.parse=function(t,e){if(e){let t,n;t=function(t){this.removeListener("error",n),this.data=t,e(null,this)}.bind(this),n=function(n){this.removeListener("parsed",t),e(n,null)}.bind(this),this.once("parsed",t),this.once("error",n)}return this.end(t),this},u.prototype.write=function(t){return this._parser.write(t),!0},u.prototype.end=function(t){this._parser.end(t)},u.prototype._metadata=function(t){this.width=t.width,this.height=t.height,this.emit("metadata",t)},u.prototype._gamma=function(t){this.gamma=t},u.prototype._handleClose=function(){this._parser.writable||this._packer.readable||this.emit("close")},u.bitblt=function(t,e,n,r,i,o,a,s){if(n|=0,r|=0,i|=0,o|=0,a|=0,s|=0,n>t.width||r>t.height||n+i>t.width||r+o>t.height)throw new Error("bitblt reading outside image");if(a>e.width||s>e.height||a+i>e.width||s+o>e.height)throw new Error("bitblt writing outside image");for(let l=0;l<o;l++)t.data.copy(e.data,(s+l)*e.width+a<<2,(r+l)*t.width+n<<2,(r+l)*t.width+n+i<<2)},u.prototype.bitblt=function(t,e,n,r,i,o,a){return u.bitblt(this,t,e,n,r,i,o,a),this},u.adjustGamma=function(t){if(t.gamma){for(let e=0;e<t.height;e++)for(let n=0;n<t.width;n++){let r=t.width*e+n<<2;for(let e=0;e<3;e++){let n=t.data[r+e]/255;n=Math.pow(n,1/2.2/t.gamma),t.data[r+e]=Math.round(255*n)}}t.gamma=0}},u.prototype.adjustGamma=function(){u.adjustGamma(this)}}).call(this,n(19).Buffer,n(26))},function(t,e,n){(function(e){t.exports=function(t){return t instanceof e}}).call(this,n(19).Buffer)},function(t,e){"function"===typeof Object.create?t.exports=function(t,e){t.super_=e,t.prototype=Object.create(e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}})}:t.exports=function(t,e){t.super_=e;var n=function(){};n.prototype=e.prototype,t.prototype=new n,t.prototype.constructor=t}},function(t,e,n){"use strict";function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var i=n(70).Buffer,o=n(30);function a(t,e,n){t.copy(e,n)}t.exports=function(){function t(){r(this,t),this.head=null,this.tail=null,this.length=0}return t.prototype.push=function(t){var e={data:t,next:null};this.length>0?this.tail.next=e:this.head=e,this.tail=e,++this.length},t.prototype.unshift=function(t){var e={data:t,next:this.head};0===this.length&&(this.tail=e),this.head=e,++this.length},t.prototype.shift=function(){if(0!==this.length){var t=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,t}},t.prototype.clear=function(){this.head=this.tail=null,this.length=0},t.prototype.join=function(t){if(0===this.length)return"";var e=this.head,n=""+e.data;while(e=e.next)n+=t+e.data;return n},t.prototype.concat=function(t){if(0===this.length)return i.alloc(0);var e=i.allocUnsafe(t>>>0),n=this.head,r=0;while(n)a(n.data,e,r),r+=n.data.length,n=n.next;return e},t}(),o&&o.inspect&&o.inspect.custom&&(t.exports.prototype[o.inspect.custom]=function(){var t=o.inspect({length:this.length});return this.constructor.name+" "+t})},function(t,e,n){(function(t,r){var i="undefined"!==typeof t&&t||"undefined"!==typeof self&&self||r,o=Function.prototype.apply;function a(t,e){this._id=t,this._clearFn=e}e.setTimeout=function(){return new a(o.call(setTimeout,i,arguments),clearTimeout)},e.setInterval=function(){return new a(o.call(setInterval,i,arguments),clearInterval)},e.clearTimeout=e.clearInterval=function(t){t&&t.close()},a.prototype.unref=a.prototype.ref=function(){},a.prototype.close=function(){this._clearFn.call(i,this._id)},e.enroll=function(t,e){clearTimeout(t._idleTimeoutId),t._idleTimeout=e},e.unenroll=function(t){clearTimeout(t._idleTimeoutId),t._idleTimeout=-1},e._unrefActive=e.active=function(t){clearTimeout(t._idleTimeoutId);var e=t._idleTimeout;e>=0&&(t._idleTimeoutId=setTimeout((function(){t._onTimeout&&t._onTimeout()}),e))},n(341),e.setImmediate="undefined"!==typeof self&&self.setImmediate||"undefined"!==typeof t&&t.setImmediate||this&&this.setImmediate,e.clearImmediate="undefined"!==typeof self&&self.clearImmediate||"undefined"!==typeof t&&t.clearImmediate||this&&this.clearImmediate}).call(this,n(29),n(9)["window"])},function(t,e,n){(function(t,e){(function(t,n){"use strict";if(!t.setImmediate){var r,i=1,o={},a=!1,s=t.document,l=Object.getPrototypeOf&&Object.getPrototypeOf(t);l=l&&l.setTimeout?l:t,"[object process]"==={}.toString.call(t.process)?d():p()?g():t.MessageChannel?_():s&&"onreadystatechange"in s.createElement("script")?y():b(),l.setImmediate=u,l.clearImmediate=h}function u(t){"function"!==typeof t&&(t=new Function(""+t));for(var e=new Array(arguments.length-1),n=0;n<e.length;n++)e[n]=arguments[n+1];var a={callback:t,args:e};return o[i]=a,r(i),i++}function h(t){delete o[t]}function f(t){var e=t.callback,r=t.args;switch(r.length){case 0:e();break;case 1:e(r[0]);break;case 2:e(r[0],r[1]);break;case 3:e(r[0],r[1],r[2]);break;default:e.apply(n,r);break}}function c(t){if(a)setTimeout(c,0,t);else{var e=o[t];if(e){a=!0;try{f(e)}finally{h(t),a=!1}}}}function d(){r=function(t){e.nextTick((function(){c(t)}))}}function p(){if(t.postMessage&&!t.importScripts){var e=!0,n=t.onmessage;return t.onmessage=function(){e=!1},t.postMessage("","*"),t.onmessage=n,e}}function g(){var e="setImmediate$"+Math.random()+"$",n=function(n){n.source===t&&"string"===typeof n.data&&0===n.data.indexOf(e)&&c(+n.data.slice(e.length))};t.addEventListener?t.addEventListener("message",n,!1):t.attachEvent("onmessage",n),r=function(n){t.postMessage(e+n,"*")}}function _(){var t=new MessageChannel;t.port1.onmessage=function(t){var e=t.data;c(e)},r=function(e){t.port2.postMessage(e)}}function y(){var t=s.documentElement;r=function(e){var n=s.createElement("script");n.onreadystatechange=function(){c(e),n.onreadystatechange=null,t.removeChild(n),n=null},t.appendChild(n)}}function b(){r=function(t){setTimeout(c,0,t)}}})("undefined"===typeof self?"undefined"===typeof t?this:t:self)}).call(this,n(29),n(26))},function(t,e,n){(function(e){function n(t,e){if(r("noDeprecation"))return t;var n=!1;function i(){if(!n){if(r("throwDeprecation"))throw new Error(e);r("traceDeprecation")?console.trace(e):console.warn(e),n=!0}return t.apply(this,arguments)}return i}function r(t){try{if(!e.localStorage)return!1}catch(t){return!1}var n=e.localStorage[t];return null!=n&&"true"===String(n).toLowerCase()}t.exports=n}).call(this,n(29))},function(t,e,n){var r=n(19),i=r.Buffer;function o(t,e){for(var n in t)e[n]=t[n]}function a(t,e,n){return i(t,e,n)}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?t.exports=r:(o(r,e),e.Buffer=a),a.prototype=Object.create(i.prototype),o(i,a),a.from=function(t,e,n){if("number"===typeof t)throw new TypeError("Argument must not be a number");return i(t,e,n)},a.alloc=function(t,e,n){if("number"!==typeof t)throw new TypeError("Argument must be a number");var r=i(t);return void 0!==e?"string"===typeof n?r.fill(e,n):r.fill(e):r.fill(0),r},a.allocUnsafe=function(t){if("number"!==typeof t)throw new TypeError("Argument must be a number");return i(t)},a.allocUnsafeSlow=function(t){if("number"!==typeof t)throw new TypeError("Argument must be a number");return r.SlowBuffer(t)}},function(t,e,n){"use strict";t.exports=o;var r=n(232),i=Object.create(n(48));function o(t){if(!(this instanceof o))return new o(t);r.call(this,t)}i.inherits=n(43),i.inherits(o,r),o.prototype._transform=function(t,e,n){n(null,t)}},function(t,e,n){(function(e){var r=n(40),i=n(71);"disable"===e.env.READABLE_STREAM?t.exports=r&&r.Writable||i:t.exports=i}).call(this,n(26))},function(t,e,n){t.exports=n(54).Duplex},function(t,e,n){t.exports=n(54).Transform},function(t,e,n){t.exports=n(54).PassThrough},function(t,e,n){"use strict";let r=n(30),i=n(49),o=n(239),a=n(371),s=n(243),l=n(245),u=n(246),h=t.exports=function(t){o.call(this),this._parser=new s(t,{read:this.read.bind(this),error:this._handleError.bind(this),metadata:this._handleMetaData.bind(this),gamma:this.emit.bind(this,"gamma"),palette:this._handlePalette.bind(this),transColor:this._handleTransColor.bind(this),finished:this._finished.bind(this),inflateData:this._inflateData.bind(this),simpleTransparency:this._simpleTransparency.bind(this),headersFinished:this._headersFinished.bind(this)}),this._options=t,this.writable=!0,this._parser.start()};r.inherits(h,o),h.prototype._handleError=function(t){this.emit("error",t),this.writable=!1,this.destroy(),this._inflate&&this._inflate.destroy&&this._inflate.destroy(),this._filter&&(this._filter.destroy(),this._filter.on("error",(function(){}))),this.errord=!0},h.prototype._inflateData=function(t){if(!this._inflate)if(this._bitmapInfo.interlace)this._inflate=i.createInflate(),this._inflate.on("error",this.emit.bind(this,"error")),this._filter.on("complete",this._complete.bind(this)),this._inflate.pipe(this._filter);else{let t=1+(this._bitmapInfo.width*this._bitmapInfo.bpp*this._bitmapInfo.depth+7>>3),e=t*this._bitmapInfo.height,n=Math.max(e,i.Z_MIN_CHUNK);this._inflate=i.createInflate({chunkSize:n});let r=e,o=this.emit.bind(this,"error");this._inflate.on("error",(function(t){r&&o(t)})),this._filter.on("complete",this._complete.bind(this));let a=this._filter.write.bind(this._filter);this._inflate.on("data",(function(t){r&&(t.length>r&&(t=t.slice(0,r)),r-=t.length,a(t))})),this._inflate.on("end",this._filter.end.bind(this._filter))}this._inflate.write(t)},h.prototype._handleMetaData=function(t){this._metaData=t,this._bitmapInfo=Object.create(t),this._filter=new a(this._bitmapInfo)},h.prototype._handleTransColor=function(t){this._bitmapInfo.transColor=t},h.prototype._handlePalette=function(t){this._bitmapInfo.palette=t},h.prototype._simpleTransparency=function(){this._metaData.alpha=!0},h.prototype._headersFinished=function(){this.emit("metadata",this._metaData)},h.prototype._finished=function(){this.errord||(this._inflate?this._inflate.end():this.emit("error","No Inflate block"))},h.prototype._complete=function(t){if(this.errord)return;let e;try{let n=l.dataToBitMap(t,this._bitmapInfo);e=u(n,this._bitmapInfo),n=null}catch(t){return void this._handleError(t)}this.emit("parsed",e)}},function(t,e,n){"use strict";(function(t,r){var i=n(72),o=n(363),a=n(364),s=n(367),l=n(370);for(var u in l)e[u]=l[u];e.NONE=0,e.DEFLATE=1,e.INFLATE=2,e.GZIP=3,e.GUNZIP=4,e.DEFLATERAW=5,e.INFLATERAW=6,e.UNZIP=7;var h=31,f=139;function c(t){if("number"!==typeof t||t<e.DEFLATE||t>e.UNZIP)throw new TypeError("Bad argument");this.dictionary=null,this.err=0,this.flush=0,this.init_done=!1,this.level=0,this.memLevel=0,this.mode=t,this.strategy=0,this.windowBits=0,this.write_in_progress=!1,this.pending_close=!1,this.gzip_id_bytes_read=0}c.prototype.close=function(){this.write_in_progress?this.pending_close=!0:(this.pending_close=!1,i(this.init_done,"close before init"),i(this.mode<=e.UNZIP),this.mode===e.DEFLATE||this.mode===e.GZIP||this.mode===e.DEFLATERAW?a.deflateEnd(this.strm):this.mode!==e.INFLATE&&this.mode!==e.GUNZIP&&this.mode!==e.INFLATERAW&&this.mode!==e.UNZIP||s.inflateEnd(this.strm),this.mode=e.NONE,this.dictionary=null)},c.prototype.write=function(t,e,n,r,i,o,a){return this._write(!0,t,e,n,r,i,o,a)},c.prototype.writeSync=function(t,e,n,r,i,o,a){return this._write(!1,t,e,n,r,i,o,a)},c.prototype._write=function(n,o,a,s,l,u,h,f){if(i.equal(arguments.length,8),i(this.init_done,"write before init"),i(this.mode!==e.NONE,"already finalized"),i.equal(!1,this.write_in_progress,"write already in progress"),i.equal(!1,this.pending_close,"close is pending"),this.write_in_progress=!0,i.equal(!1,void 0===o,"must provide flush value"),this.write_in_progress=!0,o!==e.Z_NO_FLUSH&&o!==e.Z_PARTIAL_FLUSH&&o!==e.Z_SYNC_FLUSH&&o!==e.Z_FULL_FLUSH&&o!==e.Z_FINISH&&o!==e.Z_BLOCK)throw new Error("Invalid flush value");if(null==a&&(a=t.alloc(0),l=0,s=0),this.strm.avail_in=l,this.strm.input=a,this.strm.next_in=s,this.strm.avail_out=f,this.strm.output=u,this.strm.next_out=h,this.flush=o,!n)return this._process(),this._checkError()?this._afterSync():void 0;var c=this;return r.nextTick((function(){c._process(),c._after()})),this},c.prototype._afterSync=function(){var t=this.strm.avail_out,e=this.strm.avail_in;return this.write_in_progress=!1,[e,t]},c.prototype._process=function(){var t=null;switch(this.mode){case e.DEFLATE:case e.GZIP:case e.DEFLATERAW:this.err=a.deflate(this.strm,this.flush);break;case e.UNZIP:switch(this.strm.avail_in>0&&(t=this.strm.next_in),this.gzip_id_bytes_read){case 0:if(null===t)break;if(this.strm.input[t]!==h){this.mode=e.INFLATE;break}if(this.gzip_id_bytes_read=1,t++,1===this.strm.avail_in)break;case 1:if(null===t)break;this.strm.input[t]===f?(this.gzip_id_bytes_read=2,this.mode=e.GUNZIP):this.mode=e.INFLATE;break;default:throw new Error("invalid number of gzip magic number bytes read")}case e.INFLATE:case e.GUNZIP:case e.INFLATERAW:this.err=s.inflate(this.strm,this.flush),this.err===e.Z_NEED_DICT&&this.dictionary&&(this.err=s.inflateSetDictionary(this.strm,this.dictionary),this.err===e.Z_OK?this.err=s.inflate(this.strm,this.flush):this.err===e.Z_DATA_ERROR&&(this.err=e.Z_NEED_DICT));while(this.strm.avail_in>0&&this.mode===e.GUNZIP&&this.err===e.Z_STREAM_END&&0!==this.strm.next_in[0])this.reset(),this.err=s.inflate(this.strm,this.flush);break;default:throw new Error("Unknown mode "+this.mode)}},c.prototype._checkError=function(){switch(this.err){case e.Z_OK:case e.Z_BUF_ERROR:if(0!==this.strm.avail_out&&this.flush===e.Z_FINISH)return this._error("unexpected end of file"),!1;break;case e.Z_STREAM_END:break;case e.Z_NEED_DICT:return null==this.dictionary?this._error("Missing dictionary"):this._error("Bad dictionary"),!1;default:return this._error("Zlib error"),!1}return!0},c.prototype._after=function(){if(this._checkError()){var t=this.strm.avail_out,e=this.strm.avail_in;this.write_in_progress=!1,this.callback(e,t),this.pending_close&&this.close()}},c.prototype._error=function(t){this.strm.msg&&(t=this.strm.msg),this.onerror(t,this.err),this.write_in_progress=!1,this.pending_close&&this.close()},c.prototype.init=function(t,n,r,o,a){i(4===arguments.length||5===arguments.length,"init(windowBits, level, memLevel, strategy, [dictionary])"),i(t>=8&&t<=15,"invalid windowBits"),i(n>=-1&&n<=9,"invalid compression level"),i(r>=1&&r<=9,"invalid memlevel"),i(o===e.Z_FILTERED||o===e.Z_HUFFMAN_ONLY||o===e.Z_RLE||o===e.Z_FIXED||o===e.Z_DEFAULT_STRATEGY,"invalid strategy"),this._init(n,t,r,o,a),this._setDictionary()},c.prototype.params=function(){throw new Error("deflateParams Not supported")},c.prototype.reset=function(){this._reset(),this._setDictionary()},c.prototype._init=function(t,n,r,i,l){switch(this.level=t,this.windowBits=n,this.memLevel=r,this.strategy=i,this.flush=e.Z_NO_FLUSH,this.err=e.Z_OK,this.mode!==e.GZIP&&this.mode!==e.GUNZIP||(this.windowBits+=16),this.mode===e.UNZIP&&(this.windowBits+=32),this.mode!==e.DEFLATERAW&&this.mode!==e.INFLATERAW||(this.windowBits=-1*this.windowBits),this.strm=new o,this.mode){case e.DEFLATE:case e.GZIP:case e.DEFLATERAW:this.err=a.deflateInit2(this.strm,this.level,e.Z_DEFLATED,this.windowBits,this.memLevel,this.strategy);break;case e.INFLATE:case e.GUNZIP:case e.INFLATERAW:case e.UNZIP:this.err=s.inflateInit2(this.strm,this.windowBits);break;default:throw new Error("Unknown mode "+this.mode)}this.err!==e.Z_OK&&this._error("Init error"),this.dictionary=l,this.write_in_progress=!1,this.init_done=!0},c.prototype._setDictionary=function(){if(null!=this.dictionary){switch(this.err=e.Z_OK,this.mode){case e.DEFLATE:case e.DEFLATERAW:this.err=a.deflateSetDictionary(this.strm,this.dictionary);break;default:break}this.err!==e.Z_OK&&this._error("Failed to set dictionary")}},c.prototype._reset=function(){switch(this.err=e.Z_OK,this.mode){case e.DEFLATE:case e.DEFLATERAW:case e.GZIP:this.err=a.deflateReset(this.strm);break;case e.INFLATE:case e.INFLATERAW:case e.GUNZIP:this.err=s.inflateReset(this.strm);break;default:break}this.err!==e.Z_OK&&this._error("Failed to reset stream")},e.Zlib=c}).call(this,n(19).Buffer,n(26))},function(t,e,n){"use strict";var r=n(352),i=function(){if(!Object.assign)return!1;for(var t="abcdefghijklmnopqrst",e=t.split(""),n={},r=0;r<e.length;++r)n[e[r]]=e[r];var i=Object.assign({},n),o="";for(var a in i)o+=a;return t!==o},o=function(){if(!Object.assign||!Object.preventExtensions)return!1;var t=Object.preventExtensions({1:2});try{Object.assign(t,"xy")}catch(e){return"y"===t[1]}return!1};t.exports=function(){return Object.assign?i()||o()?r:Object.assign:r}},function(t,e,n){"use strict";var r=n(353),i=n(234)(),o=n(355),a=Object,s=o("Array.prototype.push"),l=o("Object.prototype.propertyIsEnumerable"),u=i?Object.getOwnPropertySymbols:null;t.exports=function(t,e){if(null==t)throw new TypeError("target must be an object");var n=a(t);if(1===arguments.length)return n;for(var o=1;o<arguments.length;++o){var h=a(arguments[o]),f=r(h),c=i&&(Object.getOwnPropertySymbols||u);if(c)for(var d=c(h),p=0;p<d.length;++p){var g=d[p];l(h,g)&&s(f,g)}for(var _=0;_<f.length;++_){var y=f[_];if(l(h,y)){var b=h[y];n[y]=b}}}return n}},function(t,e,n){"use strict";var r=Array.prototype.slice,i=n(233),o=Object.keys,a=o?function(t){return o(t)}:n(354),s=Object.keys;a.shim=function(){if(Object.keys){var t=function(){var t=Object.keys(arguments);return t&&t.length===arguments.length}(1,2);t||(Object.keys=function(t){return i(t)?s(r.call(t)):s(t)})}else Object.keys=a;return Object.keys||a},t.exports=a},function(t,e,n){"use strict";(function(e){var r;if(!Object.keys){var i=Object.prototype.hasOwnProperty,o=Object.prototype.toString,a=n(233),s=Object.prototype.propertyIsEnumerable,l=!s.call({toString:null},"toString"),u=s.call((function(){}),"prototype"),h=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],f=function(t){var e=t.constructor;return e&&e.prototype===t},c={$applicationCache:!0,$console:!0,$external:!0,$frame:!0,$frameElement:!0,$frames:!0,$innerHeight:!0,$innerWidth:!0,$onmozfullscreenchange:!0,$onmozfullscreenerror:!0,$outerHeight:!0,$outerWidth:!0,$pageXOffset:!0,$pageYOffset:!0,$parent:!0,$scrollLeft:!0,$scrollTop:!0,$scrollX:!0,$scrollY:!0,$self:!0,$webkitIndexedDB:!0,$webkitStorageInfo:!0,$window:!0},d=function(){if("undefined"===typeof e)return!1;for(var t in e)try{if(!c["$"+t]&&i.call(e,t)&&null!==e[t]&&"object"===typeof e[t])try{f(e[t])}catch(t){return!0}}catch(t){return!0}return!1}(),p=function(t){if("undefined"===typeof e||!d)return f(t);try{return f(t)}catch(t){return!1}};r=function(t){var e=null!==t&&"object"===typeof t,n="[object Function]"===o.call(t),r=a(t),s=e&&"[object String]"===o.call(t),f=[];if(!e&&!n&&!r)throw new TypeError("Object.keys called on a non-object");var c=u&&n;if(s&&t.length>0&&!i.call(t,0))for(var d=0;d<t.length;++d)f.push(String(d));if(r&&t.length>0)for(var g=0;g<t.length;++g)f.push(String(g));else for(var _ in t)c&&"prototype"===_||!i.call(t,_)||f.push(String(_));if(l)for(var y=p(t),b=0;b<h.length;++b)y&&"constructor"===h[b]||!i.call(t,h[b])||f.push(h[b]);return f}}t.exports=r}).call(this,n(9)["window"])},function(t,e,n){"use strict";var r=n(45),i=n(360),o=i(r("String.prototype.indexOf"));t.exports=function(t,e){var n=r(t,!!e);return"function"===typeof n&&o(t,".prototype.")>-1?i(n):n}},function(t,e,n){"use strict";var r="undefined"!==typeof Symbol&&Symbol,i=n(234);t.exports=function(){return"function"===typeof r&&("function"===typeof Symbol&&("symbol"===typeof r("foo")&&("symbol"===typeof Symbol("bar")&&i())))}},function(t,e,n){"use strict";var r={foo:{}},i=Object;t.exports=function(){return{__proto__:r}.foo===r.foo&&!({__proto__:null}instanceof i)}},function(t,e,n){"use strict";var r="Function.prototype.bind called on incompatible ",i=Object.prototype.toString,o=Math.max,a="[object Function]",s=function(t,e){for(var n=[],r=0;r<t.length;r+=1)n[r]=t[r];for(var i=0;i<e.length;i+=1)n[i+t.length]=e[i];return n},l=function(t,e){for(var n=[],r=e||0,i=0;r<t.length;r+=1,i+=1)n[i]=t[r];return n},u=function(t,e){for(var n="",r=0;r<t.length;r+=1)n+=t[r],r+1<t.length&&(n+=e);return n};t.exports=function(t){var e=this;if("function"!==typeof e||i.apply(e)!==a)throw new TypeError(r+e);for(var n,h=l(arguments,1),f=function(){if(this instanceof n){var r=e.apply(this,s(h,arguments));return Object(r)===r?r:this}return e.apply(t,s(h,arguments))},c=o(0,e.length-h.length),d=[],p=0;p<c;p++)d[p]="$"+p;if(n=Function("binder","return function ("+u(d,",")+"){ return binder.apply(this,arguments); }")(f),e.prototype){var g=function(){};g.prototype=e.prototype,n.prototype=new g,g.prototype=null}return n}},function(t,e,n){"use strict";var r=Function.prototype.call,i=Object.prototype.hasOwnProperty,o=n(73);t.exports=o.call(r,i)},function(t,e,n){"use strict";var r=n(73),i=n(45),o=n(361),a=i("%TypeError%"),s=i("%Function.prototype.apply%"),l=i("%Function.prototype.call%"),u=i("%Reflect.apply%",!0)||r.call(l,s),h=i("%Object.defineProperty%",!0),f=i("%Math.max%");if(h)try{h({},"a",{value:1})}catch(t){h=null}t.exports=function(t){if("function"!==typeof t)throw new a("a function is required");var e=u(r,l,arguments);return o(e,1+f(0,t.length-(arguments.length-1)),!0)};var c=function(){return u(r,s,arguments)};h?h(t.exports,"apply",{value:c}):t.exports.apply=c},function(t,e,n){"use strict";var r=n(45),i=n(362),o=n(235)(),a=n(236),s=r("%TypeError%"),l=r("%Math.floor%");t.exports=function(t,e){if("function"!==typeof t)throw new s("`fn` is not a function");if("number"!==typeof e||e<0||e>4294967295||l(e)!==e)throw new s("`length` must be a positive 32-bit integer");var n=arguments.length>2&&!!arguments[2],r=!0,u=!0;if("length"in t&&a){var h=a(t,"length");h&&!h.configurable&&(r=!1),h&&!h.writable&&(u=!1)}return(r||u||!n)&&(o?i(t,"length",e,!0,!0):i(t,"length",e)),t}},function(t,e,n){"use strict";var r=n(235)(),i=n(45),o=r&&i("%Object.defineProperty%",!0);if(o)try{o({},"a",{value:1})}catch(t){o=!1}var a=i("%SyntaxError%"),s=i("%TypeError%"),l=n(236);t.exports=function(t,e,n){if(!t||"object"!==typeof t&&"function"!==typeof t)throw new s("`obj` must be an object or a function`");if("string"!==typeof e&&"symbol"!==typeof e)throw new s("`property` must be a string or a symbol`");if(arguments.length>3&&"boolean"!==typeof arguments[3]&&null!==arguments[3])throw new s("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&"boolean"!==typeof arguments[4]&&null!==arguments[4])throw new s("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&"boolean"!==typeof arguments[5]&&null!==arguments[5])throw new s("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&"boolean"!==typeof arguments[6])throw new s("`loose`, if provided, must be a boolean");var r=arguments.length>3?arguments[3]:null,i=arguments.length>4?arguments[4]:null,u=arguments.length>5?arguments[5]:null,h=arguments.length>6&&arguments[6],f=!!l&&l(t,e);if(o)o(t,e,{configurable:null===u&&f?f.configurable:!u,enumerable:null===r&&f?f.enumerable:!r,value:n,writable:null===i&&f?f.writable:!i});else{if(!h&&(r||i||u))throw new a("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.");t[e]=n}}},function(t,e,n){"use strict";function r(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}t.exports=r},function(t,e,n){"use strict";var r,i=n(56),o=n(365),a=n(237),s=n(238),l=n(366),u=0,h=1,f=3,c=4,d=5,p=0,g=1,_=-2,y=-3,b=-5,w=-1,m=1,v=2,E=3,k=4,x=0,A=2,T=8,O=9,S=15,R=8,I=29,P=256,L=P+1+I,C=30,j=19,B=2*L+1,N=15,M=3,U=258,D=U+M+1,F=32,z=42,Z=69,Y=73,H=91,W=103,q=113,G=666,V=1,K=2,$=3,J=4,X=3;function Q(t,e){return t.msg=l[e],e}function tt(t){return(t<<1)-(t>4?9:0)}function et(t){var e=t.length;while(--e>=0)t[e]=0}function nt(t){var e=t.state,n=e.pending;n>t.avail_out&&(n=t.avail_out),0!==n&&(i.arraySet(t.output,e.pending_buf,e.pending_out,n,t.next_out),t.next_out+=n,e.pending_out+=n,t.total_out+=n,t.avail_out-=n,e.pending-=n,0===e.pending&&(e.pending_out=0))}function rt(t,e){o._tr_flush_block(t,t.block_start>=0?t.block_start:-1,t.strstart-t.block_start,e),t.block_start=t.strstart,nt(t.strm)}function it(t,e){t.pending_buf[t.pending++]=e}function ot(t,e){t.pending_buf[t.pending++]=e>>>8&255,t.pending_buf[t.pending++]=255&e}function at(t,e,n,r){var o=t.avail_in;return o>r&&(o=r),0===o?0:(t.avail_in-=o,i.arraySet(e,t.input,t.next_in,o,n),1===t.state.wrap?t.adler=a(t.adler,e,o,n):2===t.state.wrap&&(t.adler=s(t.adler,e,o,n)),t.next_in+=o,t.total_in+=o,o)}function st(t,e){var n,r,i=t.max_chain_length,o=t.strstart,a=t.prev_length,s=t.nice_match,l=t.strstart>t.w_size-D?t.strstart-(t.w_size-D):0,u=t.window,h=t.w_mask,f=t.prev,c=t.strstart+U,d=u[o+a-1],p=u[o+a];t.prev_length>=t.good_match&&(i>>=2),s>t.lookahead&&(s=t.lookahead);do{if(n=e,u[n+a]===p&&u[n+a-1]===d&&u[n]===u[o]&&u[++n]===u[o+1]){o+=2,n++;do{}while(u[++o]===u[++n]&&u[++o]===u[++n]&&u[++o]===u[++n]&&u[++o]===u[++n]&&u[++o]===u[++n]&&u[++o]===u[++n]&&u[++o]===u[++n]&&u[++o]===u[++n]&&o<c);if(r=U-(c-o),o=c-U,r>a){if(t.match_start=e,a=r,r>=s)break;d=u[o+a-1],p=u[o+a]}}}while((e=f[e&h])>l&&0!==--i);return a<=t.lookahead?a:t.lookahead}function lt(t){var e,n,r,o,a,s=t.w_size;do{if(o=t.window_size-t.lookahead-t.strstart,t.strstart>=s+(s-D)){i.arraySet(t.window,t.window,s,s,0),t.match_start-=s,t.strstart-=s,t.block_start-=s,n=t.hash_size,e=n;do{r=t.head[--e],t.head[e]=r>=s?r-s:0}while(--n);n=s,e=n;do{r=t.prev[--e],t.prev[e]=r>=s?r-s:0}while(--n);o+=s}if(0===t.strm.avail_in)break;if(n=at(t.strm,t.window,t.strstart+t.lookahead,o),t.lookahead+=n,t.lookahead+t.insert>=M){a=t.strstart-t.insert,t.ins_h=t.window[a],t.ins_h=(t.ins_h<<t.hash_shift^t.window[a+1])&t.hash_mask;while(t.insert)if(t.ins_h=(t.ins_h<<t.hash_shift^t.window[a+M-1])&t.hash_mask,t.prev[a&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=a,a++,t.insert--,t.lookahead+t.insert<M)break}}while(t.lookahead<D&&0!==t.strm.avail_in)}function ut(t,e){var n=65535;for(n>t.pending_buf_size-5&&(n=t.pending_buf_size-5);;){if(t.lookahead<=1){if(lt(t),0===t.lookahead&&e===u)return V;if(0===t.lookahead)break}t.strstart+=t.lookahead,t.lookahead=0;var r=t.block_start+n;if((0===t.strstart||t.strstart>=r)&&(t.lookahead=t.strstart-r,t.strstart=r,rt(t,!1),0===t.strm.avail_out))return V;if(t.strstart-t.block_start>=t.w_size-D&&(rt(t,!1),0===t.strm.avail_out))return V}return t.insert=0,e===c?(rt(t,!0),0===t.strm.avail_out?$:J):(t.strstart>t.block_start&&(rt(t,!1),t.strm.avail_out),V)}function ht(t,e){for(var n,r;;){if(t.lookahead<D){if(lt(t),t.lookahead<D&&e===u)return V;if(0===t.lookahead)break}if(n=0,t.lookahead>=M&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+M-1])&t.hash_mask,n=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),0!==n&&t.strstart-n<=t.w_size-D&&(t.match_length=st(t,n)),t.match_length>=M)if(r=o._tr_tally(t,t.strstart-t.match_start,t.match_length-M),t.lookahead-=t.match_length,t.match_length<=t.max_lazy_match&&t.lookahead>=M){t.match_length--;do{t.strstart++,t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+M-1])&t.hash_mask,n=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart}while(0!==--t.match_length);t.strstart++}else t.strstart+=t.match_length,t.match_length=0,t.ins_h=t.window[t.strstart],t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+1])&t.hash_mask;else r=o._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++;if(r&&(rt(t,!1),0===t.strm.avail_out))return V}return t.insert=t.strstart<M-1?t.strstart:M-1,e===c?(rt(t,!0),0===t.strm.avail_out?$:J):t.last_lit&&(rt(t,!1),0===t.strm.avail_out)?V:K}function ft(t,e){for(var n,r,i;;){if(t.lookahead<D){if(lt(t),t.lookahead<D&&e===u)return V;if(0===t.lookahead)break}if(n=0,t.lookahead>=M&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+M-1])&t.hash_mask,n=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart),t.prev_length=t.match_length,t.prev_match=t.match_start,t.match_length=M-1,0!==n&&t.prev_length<t.max_lazy_match&&t.strstart-n<=t.w_size-D&&(t.match_length=st(t,n),t.match_length<=5&&(t.strategy===m||t.match_length===M&&t.strstart-t.match_start>4096)&&(t.match_length=M-1)),t.prev_length>=M&&t.match_length<=t.prev_length){i=t.strstart+t.lookahead-M,r=o._tr_tally(t,t.strstart-1-t.prev_match,t.prev_length-M),t.lookahead-=t.prev_length-1,t.prev_length-=2;do{++t.strstart<=i&&(t.ins_h=(t.ins_h<<t.hash_shift^t.window[t.strstart+M-1])&t.hash_mask,n=t.prev[t.strstart&t.w_mask]=t.head[t.ins_h],t.head[t.ins_h]=t.strstart)}while(0!==--t.prev_length);if(t.match_available=0,t.match_length=M-1,t.strstart++,r&&(rt(t,!1),0===t.strm.avail_out))return V}else if(t.match_available){if(r=o._tr_tally(t,0,t.window[t.strstart-1]),r&&rt(t,!1),t.strstart++,t.lookahead--,0===t.strm.avail_out)return V}else t.match_available=1,t.strstart++,t.lookahead--}return t.match_available&&(r=o._tr_tally(t,0,t.window[t.strstart-1]),t.match_available=0),t.insert=t.strstart<M-1?t.strstart:M-1,e===c?(rt(t,!0),0===t.strm.avail_out?$:J):t.last_lit&&(rt(t,!1),0===t.strm.avail_out)?V:K}function ct(t,e){for(var n,r,i,a,s=t.window;;){if(t.lookahead<=U){if(lt(t),t.lookahead<=U&&e===u)return V;if(0===t.lookahead)break}if(t.match_length=0,t.lookahead>=M&&t.strstart>0&&(i=t.strstart-1,r=s[i],r===s[++i]&&r===s[++i]&&r===s[++i])){a=t.strstart+U;do{}while(r===s[++i]&&r===s[++i]&&r===s[++i]&&r===s[++i]&&r===s[++i]&&r===s[++i]&&r===s[++i]&&r===s[++i]&&i<a);t.match_length=U-(a-i),t.match_length>t.lookahead&&(t.match_length=t.lookahead)}if(t.match_length>=M?(n=o._tr_tally(t,1,t.match_length-M),t.lookahead-=t.match_length,t.strstart+=t.match_length,t.match_length=0):(n=o._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++),n&&(rt(t,!1),0===t.strm.avail_out))return V}return t.insert=0,e===c?(rt(t,!0),0===t.strm.avail_out?$:J):t.last_lit&&(rt(t,!1),0===t.strm.avail_out)?V:K}function dt(t,e){for(var n;;){if(0===t.lookahead&&(lt(t),0===t.lookahead)){if(e===u)return V;break}if(t.match_length=0,n=o._tr_tally(t,0,t.window[t.strstart]),t.lookahead--,t.strstart++,n&&(rt(t,!1),0===t.strm.avail_out))return V}return t.insert=0,e===c?(rt(t,!0),0===t.strm.avail_out?$:J):t.last_lit&&(rt(t,!1),0===t.strm.avail_out)?V:K}function pt(t,e,n,r,i){this.good_length=t,this.max_lazy=e,this.nice_length=n,this.max_chain=r,this.func=i}function gt(t){t.window_size=2*t.w_size,et(t.head),t.max_lazy_match=r[t.level].max_lazy,t.good_match=r[t.level].good_length,t.nice_match=r[t.level].nice_length,t.max_chain_length=r[t.level].max_chain,t.strstart=0,t.block_start=0,t.lookahead=0,t.insert=0,t.match_length=t.prev_length=M-1,t.match_available=0,t.ins_h=0}function _t(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=T,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new i.Buf16(2*B),this.dyn_dtree=new i.Buf16(2*(2*C+1)),this.bl_tree=new i.Buf16(2*(2*j+1)),et(this.dyn_ltree),et(this.dyn_dtree),et(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new i.Buf16(N+1),this.heap=new i.Buf16(2*L+1),et(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new i.Buf16(2*L+1),et(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function yt(t){var e;return t&&t.state?(t.total_in=t.total_out=0,t.data_type=A,e=t.state,e.pending=0,e.pending_out=0,e.wrap<0&&(e.wrap=-e.wrap),e.status=e.wrap?z:q,t.adler=2===e.wrap?0:1,e.last_flush=u,o._tr_init(e),p):Q(t,_)}function bt(t){var e=yt(t);return e===p&&gt(t.state),e}function wt(t,e){return t&&t.state?2!==t.state.wrap?_:(t.state.gzhead=e,p):_}function mt(t,e,n,r,o,a){if(!t)return _;var s=1;if(e===w&&(e=6),r<0?(s=0,r=-r):r>15&&(s=2,r-=16),o<1||o>O||n!==T||r<8||r>15||e<0||e>9||a<0||a>k)return Q(t,_);8===r&&(r=9);var l=new _t;return t.state=l,l.strm=t,l.wrap=s,l.gzhead=null,l.w_bits=r,l.w_size=1<<l.w_bits,l.w_mask=l.w_size-1,l.hash_bits=o+7,l.hash_size=1<<l.hash_bits,l.hash_mask=l.hash_size-1,l.hash_shift=~~((l.hash_bits+M-1)/M),l.window=new i.Buf8(2*l.w_size),l.head=new i.Buf16(l.hash_size),l.prev=new i.Buf16(l.w_size),l.lit_bufsize=1<<o+6,l.pending_buf_size=4*l.lit_bufsize,l.pending_buf=new i.Buf8(l.pending_buf_size),l.d_buf=1*l.lit_bufsize,l.l_buf=3*l.lit_bufsize,l.level=e,l.strategy=a,l.method=n,bt(t)}function vt(t,e){return mt(t,e,T,S,R,x)}function Et(t,e){var n,i,a,l;if(!t||!t.state||e>d||e<0)return t?Q(t,_):_;if(i=t.state,!t.output||!t.input&&0!==t.avail_in||i.status===G&&e!==c)return Q(t,0===t.avail_out?b:_);if(i.strm=t,n=i.last_flush,i.last_flush=e,i.status===z)if(2===i.wrap)t.adler=0,it(i,31),it(i,139),it(i,8),i.gzhead?(it(i,(i.gzhead.text?1:0)+(i.gzhead.hcrc?2:0)+(i.gzhead.extra?4:0)+(i.gzhead.name?8:0)+(i.gzhead.comment?16:0)),it(i,255&i.gzhead.time),it(i,i.gzhead.time>>8&255),it(i,i.gzhead.time>>16&255),it(i,i.gzhead.time>>24&255),it(i,9===i.level?2:i.strategy>=v||i.level<2?4:0),it(i,255&i.gzhead.os),i.gzhead.extra&&i.gzhead.extra.length&&(it(i,255&i.gzhead.extra.length),it(i,i.gzhead.extra.length>>8&255)),i.gzhead.hcrc&&(t.adler=s(t.adler,i.pending_buf,i.pending,0)),i.gzindex=0,i.status=Z):(it(i,0),it(i,0),it(i,0),it(i,0),it(i,0),it(i,9===i.level?2:i.strategy>=v||i.level<2?4:0),it(i,X),i.status=q);else{var y=T+(i.w_bits-8<<4)<<8,w=-1;w=i.strategy>=v||i.level<2?0:i.level<6?1:6===i.level?2:3,y|=w<<6,0!==i.strstart&&(y|=F),y+=31-y%31,i.status=q,ot(i,y),0!==i.strstart&&(ot(i,t.adler>>>16),ot(i,65535&t.adler)),t.adler=1}if(i.status===Z)if(i.gzhead.extra){a=i.pending;while(i.gzindex<(65535&i.gzhead.extra.length)){if(i.pending===i.pending_buf_size&&(i.gzhead.hcrc&&i.pending>a&&(t.adler=s(t.adler,i.pending_buf,i.pending-a,a)),nt(t),a=i.pending,i.pending===i.pending_buf_size))break;it(i,255&i.gzhead.extra[i.gzindex]),i.gzindex++}i.gzhead.hcrc&&i.pending>a&&(t.adler=s(t.adler,i.pending_buf,i.pending-a,a)),i.gzindex===i.gzhead.extra.length&&(i.gzindex=0,i.status=Y)}else i.status=Y;if(i.status===Y)if(i.gzhead.name){a=i.pending;do{if(i.pending===i.pending_buf_size&&(i.gzhead.hcrc&&i.pending>a&&(t.adler=s(t.adler,i.pending_buf,i.pending-a,a)),nt(t),a=i.pending,i.pending===i.pending_buf_size)){l=1;break}l=i.gzindex<i.gzhead.name.length?255&i.gzhead.name.charCodeAt(i.gzindex++):0,it(i,l)}while(0!==l);i.gzhead.hcrc&&i.pending>a&&(t.adler=s(t.adler,i.pending_buf,i.pending-a,a)),0===l&&(i.gzindex=0,i.status=H)}else i.status=H;if(i.status===H)if(i.gzhead.comment){a=i.pending;do{if(i.pending===i.pending_buf_size&&(i.gzhead.hcrc&&i.pending>a&&(t.adler=s(t.adler,i.pending_buf,i.pending-a,a)),nt(t),a=i.pending,i.pending===i.pending_buf_size)){l=1;break}l=i.gzindex<i.gzhead.comment.length?255&i.gzhead.comment.charCodeAt(i.gzindex++):0,it(i,l)}while(0!==l);i.gzhead.hcrc&&i.pending>a&&(t.adler=s(t.adler,i.pending_buf,i.pending-a,a)),0===l&&(i.status=W)}else i.status=W;if(i.status===W&&(i.gzhead.hcrc?(i.pending+2>i.pending_buf_size&&nt(t),i.pending+2<=i.pending_buf_size&&(it(i,255&t.adler),it(i,t.adler>>8&255),t.adler=0,i.status=q)):i.status=q),0!==i.pending){if(nt(t),0===t.avail_out)return i.last_flush=-1,p}else if(0===t.avail_in&&tt(e)<=tt(n)&&e!==c)return Q(t,b);if(i.status===G&&0!==t.avail_in)return Q(t,b);if(0!==t.avail_in||0!==i.lookahead||e!==u&&i.status!==G){var m=i.strategy===v?dt(i,e):i.strategy===E?ct(i,e):r[i.level].func(i,e);if(m!==$&&m!==J||(i.status=G),m===V||m===$)return 0===t.avail_out&&(i.last_flush=-1),p;if(m===K&&(e===h?o._tr_align(i):e!==d&&(o._tr_stored_block(i,0,0,!1),e===f&&(et(i.head),0===i.lookahead&&(i.strstart=0,i.block_start=0,i.insert=0))),nt(t),0===t.avail_out))return i.last_flush=-1,p}return e!==c?p:i.wrap<=0?g:(2===i.wrap?(it(i,255&t.adler),it(i,t.adler>>8&255),it(i,t.adler>>16&255),it(i,t.adler>>24&255),it(i,255&t.total_in),it(i,t.total_in>>8&255),it(i,t.total_in>>16&255),it(i,t.total_in>>24&255)):(ot(i,t.adler>>>16),ot(i,65535&t.adler)),nt(t),i.wrap>0&&(i.wrap=-i.wrap),0!==i.pending?p:g)}function kt(t){var e;return t&&t.state?(e=t.state.status,e!==z&&e!==Z&&e!==Y&&e!==H&&e!==W&&e!==q&&e!==G?Q(t,_):(t.state=null,e===q?Q(t,y):p)):_}function xt(t,e){var n,r,o,s,l,u,h,f,c=e.length;if(!t||!t.state)return _;if(n=t.state,s=n.wrap,2===s||1===s&&n.status!==z||n.lookahead)return _;1===s&&(t.adler=a(t.adler,e,c,0)),n.wrap=0,c>=n.w_size&&(0===s&&(et(n.head),n.strstart=0,n.block_start=0,n.insert=0),f=new i.Buf8(n.w_size),i.arraySet(f,e,c-n.w_size,n.w_size,0),e=f,c=n.w_size),l=t.avail_in,u=t.next_in,h=t.input,t.avail_in=c,t.next_in=0,t.input=e,lt(n);while(n.lookahead>=M){r=n.strstart,o=n.lookahead-(M-1);do{n.ins_h=(n.ins_h<<n.hash_shift^n.window[r+M-1])&n.hash_mask,n.prev[r&n.w_mask]=n.head[n.ins_h],n.head[n.ins_h]=r,r++}while(--o);n.strstart=r,n.lookahead=M-1,lt(n)}return n.strstart+=n.lookahead,n.block_start=n.strstart,n.insert=n.lookahead,n.lookahead=0,n.match_length=n.prev_length=M-1,n.match_available=0,t.next_in=u,t.input=h,t.avail_in=l,n.wrap=s,p}r=[new pt(0,0,0,0,ut),new pt(4,4,8,4,ht),new pt(4,5,16,8,ht),new pt(4,6,32,32,ht),new pt(4,4,16,16,ft),new pt(8,16,32,32,ft),new pt(8,16,128,128,ft),new pt(8,32,128,256,ft),new pt(32,128,258,1024,ft),new pt(32,258,258,4096,ft)],e.deflateInit=vt,e.deflateInit2=mt,e.deflateReset=bt,e.deflateResetKeep=yt,e.deflateSetHeader=wt,e.deflate=Et,e.deflateEnd=kt,e.deflateSetDictionary=xt,e.deflateInfo="pako deflate (from Nodeca project)"},function(t,e,n){"use strict";var r=n(56),i=4,o=0,a=1,s=2;function l(t){var e=t.length;while(--e>=0)t[e]=0}var u=0,h=1,f=2,c=3,d=258,p=29,g=256,_=g+1+p,y=30,b=19,w=2*_+1,m=15,v=16,E=7,k=256,x=16,A=17,T=18,O=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],S=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],R=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],I=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],P=512,L=new Array(2*(_+2));l(L);var C=new Array(2*y);l(C);var j=new Array(P);l(j);var B=new Array(d-c+1);l(B);var N=new Array(p);l(N);var M,U,D,F=new Array(y);function z(t,e,n,r,i){this.static_tree=t,this.extra_bits=e,this.extra_base=n,this.elems=r,this.max_length=i,this.has_stree=t&&t.length}function Z(t,e){this.dyn_tree=t,this.max_code=0,this.stat_desc=e}function Y(t){return t<256?j[t]:j[256+(t>>>7)]}function H(t,e){t.pending_buf[t.pending++]=255&e,t.pending_buf[t.pending++]=e>>>8&255}function W(t,e,n){t.bi_valid>v-n?(t.bi_buf|=e<<t.bi_valid&65535,H(t,t.bi_buf),t.bi_buf=e>>v-t.bi_valid,t.bi_valid+=n-v):(t.bi_buf|=e<<t.bi_valid&65535,t.bi_valid+=n)}function q(t,e,n){W(t,n[2*e],n[2*e+1])}function G(t,e){var n=0;do{n|=1&t,t>>>=1,n<<=1}while(--e>0);return n>>>1}function V(t){16===t.bi_valid?(H(t,t.bi_buf),t.bi_buf=0,t.bi_valid=0):t.bi_valid>=8&&(t.pending_buf[t.pending++]=255&t.bi_buf,t.bi_buf>>=8,t.bi_valid-=8)}function K(t,e){var n,r,i,o,a,s,l=e.dyn_tree,u=e.max_code,h=e.stat_desc.static_tree,f=e.stat_desc.has_stree,c=e.stat_desc.extra_bits,d=e.stat_desc.extra_base,p=e.stat_desc.max_length,g=0;for(o=0;o<=m;o++)t.bl_count[o]=0;for(l[2*t.heap[t.heap_max]+1]=0,n=t.heap_max+1;n<w;n++)r=t.heap[n],o=l[2*l[2*r+1]+1]+1,o>p&&(o=p,g++),l[2*r+1]=o,r>u||(t.bl_count[o]++,a=0,r>=d&&(a=c[r-d]),s=l[2*r],t.opt_len+=s*(o+a),f&&(t.static_len+=s*(h[2*r+1]+a)));if(0!==g){do{o=p-1;while(0===t.bl_count[o])o--;t.bl_count[o]--,t.bl_count[o+1]+=2,t.bl_count[p]--,g-=2}while(g>0);for(o=p;0!==o;o--){r=t.bl_count[o];while(0!==r)i=t.heap[--n],i>u||(l[2*i+1]!==o&&(t.opt_len+=(o-l[2*i+1])*l[2*i],l[2*i+1]=o),r--)}}}function $(t,e,n){var r,i,o=new Array(m+1),a=0;for(r=1;r<=m;r++)o[r]=a=a+n[r-1]<<1;for(i=0;i<=e;i++){var s=t[2*i+1];0!==s&&(t[2*i]=G(o[s]++,s))}}function J(){var t,e,n,r,i,o=new Array(m+1);for(n=0,r=0;r<p-1;r++)for(N[r]=n,t=0;t<1<<O[r];t++)B[n++]=r;for(B[n-1]=r,i=0,r=0;r<16;r++)for(F[r]=i,t=0;t<1<<S[r];t++)j[i++]=r;for(i>>=7;r<y;r++)for(F[r]=i<<7,t=0;t<1<<S[r]-7;t++)j[256+i++]=r;for(e=0;e<=m;e++)o[e]=0;t=0;while(t<=143)L[2*t+1]=8,t++,o[8]++;while(t<=255)L[2*t+1]=9,t++,o[9]++;while(t<=279)L[2*t+1]=7,t++,o[7]++;while(t<=287)L[2*t+1]=8,t++,o[8]++;for($(L,_+1,o),t=0;t<y;t++)C[2*t+1]=5,C[2*t]=G(t,5);M=new z(L,O,g+1,_,m),U=new z(C,S,0,y,m),D=new z(new Array(0),R,0,b,E)}function X(t){var e;for(e=0;e<_;e++)t.dyn_ltree[2*e]=0;for(e=0;e<y;e++)t.dyn_dtree[2*e]=0;for(e=0;e<b;e++)t.bl_tree[2*e]=0;t.dyn_ltree[2*k]=1,t.opt_len=t.static_len=0,t.last_lit=t.matches=0}function Q(t){t.bi_valid>8?H(t,t.bi_buf):t.bi_valid>0&&(t.pending_buf[t.pending++]=t.bi_buf),t.bi_buf=0,t.bi_valid=0}function tt(t,e,n,i){Q(t),i&&(H(t,n),H(t,~n)),r.arraySet(t.pending_buf,t.window,e,n,t.pending),t.pending+=n}function et(t,e,n,r){var i=2*e,o=2*n;return t[i]<t[o]||t[i]===t[o]&&r[e]<=r[n]}function nt(t,e,n){var r=t.heap[n],i=n<<1;while(i<=t.heap_len){if(i<t.heap_len&&et(e,t.heap[i+1],t.heap[i],t.depth)&&i++,et(e,r,t.heap[i],t.depth))break;t.heap[n]=t.heap[i],n=i,i<<=1}t.heap[n]=r}function rt(t,e,n){var r,i,o,a,s=0;if(0!==t.last_lit)do{r=t.pending_buf[t.d_buf+2*s]<<8|t.pending_buf[t.d_buf+2*s+1],i=t.pending_buf[t.l_buf+s],s++,0===r?q(t,i,e):(o=B[i],q(t,o+g+1,e),a=O[o],0!==a&&(i-=N[o],W(t,i,a)),r--,o=Y(r),q(t,o,n),a=S[o],0!==a&&(r-=F[o],W(t,r,a)))}while(s<t.last_lit);q(t,k,e)}function it(t,e){var n,r,i,o=e.dyn_tree,a=e.stat_desc.static_tree,s=e.stat_desc.has_stree,l=e.stat_desc.elems,u=-1;for(t.heap_len=0,t.heap_max=w,n=0;n<l;n++)0!==o[2*n]?(t.heap[++t.heap_len]=u=n,t.depth[n]=0):o[2*n+1]=0;while(t.heap_len<2)i=t.heap[++t.heap_len]=u<2?++u:0,o[2*i]=1,t.depth[i]=0,t.opt_len--,s&&(t.static_len-=a[2*i+1]);for(e.max_code=u,n=t.heap_len>>1;n>=1;n--)nt(t,o,n);i=l;do{n=t.heap[1],t.heap[1]=t.heap[t.heap_len--],nt(t,o,1),r=t.heap[1],t.heap[--t.heap_max]=n,t.heap[--t.heap_max]=r,o[2*i]=o[2*n]+o[2*r],t.depth[i]=(t.depth[n]>=t.depth[r]?t.depth[n]:t.depth[r])+1,o[2*n+1]=o[2*r+1]=i,t.heap[1]=i++,nt(t,o,1)}while(t.heap_len>=2);t.heap[--t.heap_max]=t.heap[1],K(t,e),$(o,u,t.bl_count)}function ot(t,e,n){var r,i,o=-1,a=e[1],s=0,l=7,u=4;for(0===a&&(l=138,u=3),e[2*(n+1)+1]=65535,r=0;r<=n;r++)i=a,a=e[2*(r+1)+1],++s<l&&i===a||(s<u?t.bl_tree[2*i]+=s:0!==i?(i!==o&&t.bl_tree[2*i]++,t.bl_tree[2*x]++):s<=10?t.bl_tree[2*A]++:t.bl_tree[2*T]++,s=0,o=i,0===a?(l=138,u=3):i===a?(l=6,u=3):(l=7,u=4))}function at(t,e,n){var r,i,o=-1,a=e[1],s=0,l=7,u=4;for(0===a&&(l=138,u=3),r=0;r<=n;r++)if(i=a,a=e[2*(r+1)+1],!(++s<l&&i===a)){if(s<u)do{q(t,i,t.bl_tree)}while(0!==--s);else 0!==i?(i!==o&&(q(t,i,t.bl_tree),s--),q(t,x,t.bl_tree),W(t,s-3,2)):s<=10?(q(t,A,t.bl_tree),W(t,s-3,3)):(q(t,T,t.bl_tree),W(t,s-11,7));s=0,o=i,0===a?(l=138,u=3):i===a?(l=6,u=3):(l=7,u=4)}}function st(t){var e;for(ot(t,t.dyn_ltree,t.l_desc.max_code),ot(t,t.dyn_dtree,t.d_desc.max_code),it(t,t.bl_desc),e=b-1;e>=3;e--)if(0!==t.bl_tree[2*I[e]+1])break;return t.opt_len+=3*(e+1)+5+5+4,e}function lt(t,e,n,r){var i;for(W(t,e-257,5),W(t,n-1,5),W(t,r-4,4),i=0;i<r;i++)W(t,t.bl_tree[2*I[i]+1],3);at(t,t.dyn_ltree,e-1),at(t,t.dyn_dtree,n-1)}function ut(t){var e,n=4093624447;for(e=0;e<=31;e++,n>>>=1)if(1&n&&0!==t.dyn_ltree[2*e])return o;if(0!==t.dyn_ltree[18]||0!==t.dyn_ltree[20]||0!==t.dyn_ltree[26])return a;for(e=32;e<g;e++)if(0!==t.dyn_ltree[2*e])return a;return o}l(F);var ht=!1;function ft(t){ht||(J(),ht=!0),t.l_desc=new Z(t.dyn_ltree,M),t.d_desc=new Z(t.dyn_dtree,U),t.bl_desc=new Z(t.bl_tree,D),t.bi_buf=0,t.bi_valid=0,X(t)}function ct(t,e,n,r){W(t,(u<<1)+(r?1:0),3),tt(t,e,n,!0)}function dt(t){W(t,h<<1,3),q(t,k,L),V(t)}function pt(t,e,n,r){var o,a,l=0;t.level>0?(t.strm.data_type===s&&(t.strm.data_type=ut(t)),it(t,t.l_desc),it(t,t.d_desc),l=st(t),o=t.opt_len+3+7>>>3,a=t.static_len+3+7>>>3,a<=o&&(o=a)):o=a=n+5,n+4<=o&&-1!==e?ct(t,e,n,r):t.strategy===i||a===o?(W(t,(h<<1)+(r?1:0),3),rt(t,L,C)):(W(t,(f<<1)+(r?1:0),3),lt(t,t.l_desc.max_code+1,t.d_desc.max_code+1,l+1),rt(t,t.dyn_ltree,t.dyn_dtree)),X(t),r&&Q(t)}function gt(t,e,n){return t.pending_buf[t.d_buf+2*t.last_lit]=e>>>8&255,t.pending_buf[t.d_buf+2*t.last_lit+1]=255&e,t.pending_buf[t.l_buf+t.last_lit]=255&n,t.last_lit++,0===e?t.dyn_ltree[2*n]++:(t.matches++,e--,t.dyn_ltree[2*(B[n]+g+1)]++,t.dyn_dtree[2*Y(e)]++),t.last_lit===t.lit_bufsize-1}e._tr_init=ft,e._tr_stored_block=ct,e._tr_flush_block=pt,e._tr_tally=gt,e._tr_align=dt},function(t,e,n){"use strict";t.exports={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"}},function(t,e,n){"use strict";var r=n(56),i=n(237),o=n(238),a=n(368),s=n(369),l=0,u=1,h=2,f=4,c=5,d=6,p=0,g=1,_=2,y=-2,b=-3,w=-4,m=-5,v=8,E=1,k=2,x=3,A=4,T=5,O=6,S=7,R=8,I=9,P=10,L=11,C=12,j=13,B=14,N=15,M=16,U=17,D=18,F=19,z=20,Z=21,Y=22,H=23,W=24,q=25,G=26,V=27,K=28,$=29,J=30,X=31,Q=32,tt=852,et=592,nt=15,rt=nt;function it(t){return(t>>>24&255)+(t>>>8&65280)+((65280&t)<<8)+((255&t)<<24)}function ot(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new r.Buf16(320),this.work=new r.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function at(t){var e;return t&&t.state?(e=t.state,t.total_in=t.total_out=e.total=0,t.msg="",e.wrap&&(t.adler=1&e.wrap),e.mode=E,e.last=0,e.havedict=0,e.dmax=32768,e.head=null,e.hold=0,e.bits=0,e.lencode=e.lendyn=new r.Buf32(tt),e.distcode=e.distdyn=new r.Buf32(et),e.sane=1,e.back=-1,p):y}function st(t){var e;return t&&t.state?(e=t.state,e.wsize=0,e.whave=0,e.wnext=0,at(t)):y}function lt(t,e){var n,r;return t&&t.state?(r=t.state,e<0?(n=0,e=-e):(n=1+(e>>4),e<48&&(e&=15)),e&&(e<8||e>15)?y:(null!==r.window&&r.wbits!==e&&(r.window=null),r.wrap=n,r.wbits=e,st(t))):y}function ut(t,e){var n,r;return t?(r=new ot,t.state=r,r.window=null,n=lt(t,e),n!==p&&(t.state=null),n):y}function ht(t){return ut(t,rt)}var ft,ct,dt=!0;function pt(t){if(dt){var e;ft=new r.Buf32(512),ct=new r.Buf32(32),e=0;while(e<144)t.lens[e++]=8;while(e<256)t.lens[e++]=9;while(e<280)t.lens[e++]=7;while(e<288)t.lens[e++]=8;s(u,t.lens,0,288,ft,0,t.work,{bits:9}),e=0;while(e<32)t.lens[e++]=5;s(h,t.lens,0,32,ct,0,t.work,{bits:5}),dt=!1}t.lencode=ft,t.lenbits=9,t.distcode=ct,t.distbits=5}function gt(t,e,n,i){var o,a=t.state;return null===a.window&&(a.wsize=1<<a.wbits,a.wnext=0,a.whave=0,a.window=new r.Buf8(a.wsize)),i>=a.wsize?(r.arraySet(a.window,e,n-a.wsize,a.wsize,0),a.wnext=0,a.whave=a.wsize):(o=a.wsize-a.wnext,o>i&&(o=i),r.arraySet(a.window,e,n-i,o,a.wnext),i-=o,i?(r.arraySet(a.window,e,n-i,i,0),a.wnext=i,a.whave=a.wsize):(a.wnext+=o,a.wnext===a.wsize&&(a.wnext=0),a.whave<a.wsize&&(a.whave+=o))),0}function _t(t,e){var n,tt,et,nt,rt,ot,at,st,lt,ut,ht,ft,ct,dt,_t,yt,bt,wt,mt,vt,Et,kt,xt,At,Tt=0,Ot=new r.Buf8(4),St=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!t||!t.state||!t.output||!t.input&&0!==t.avail_in)return y;n=t.state,n.mode===C&&(n.mode=j),rt=t.next_out,et=t.output,at=t.avail_out,nt=t.next_in,tt=t.input,ot=t.avail_in,st=n.hold,lt=n.bits,ut=ot,ht=at,kt=p;t:for(;;)switch(n.mode){case E:if(0===n.wrap){n.mode=j;break}while(lt<16){if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}if(2&n.wrap&&35615===st){n.check=0,Ot[0]=255&st,Ot[1]=st>>>8&255,n.check=o(n.check,Ot,2,0),st=0,lt=0,n.mode=k;break}if(n.flags=0,n.head&&(n.head.done=!1),!(1&n.wrap)||(((255&st)<<8)+(st>>8))%31){t.msg="incorrect header check",n.mode=J;break}if((15&st)!==v){t.msg="unknown compression method",n.mode=J;break}if(st>>>=4,lt-=4,Et=8+(15&st),0===n.wbits)n.wbits=Et;else if(Et>n.wbits){t.msg="invalid window size",n.mode=J;break}n.dmax=1<<Et,t.adler=n.check=1,n.mode=512&st?P:C,st=0,lt=0;break;case k:while(lt<16){if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}if(n.flags=st,(255&n.flags)!==v){t.msg="unknown compression method",n.mode=J;break}if(57344&n.flags){t.msg="unknown header flags set",n.mode=J;break}n.head&&(n.head.text=st>>8&1),512&n.flags&&(Ot[0]=255&st,Ot[1]=st>>>8&255,n.check=o(n.check,Ot,2,0)),st=0,lt=0,n.mode=x;case x:while(lt<32){if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}n.head&&(n.head.time=st),512&n.flags&&(Ot[0]=255&st,Ot[1]=st>>>8&255,Ot[2]=st>>>16&255,Ot[3]=st>>>24&255,n.check=o(n.check,Ot,4,0)),st=0,lt=0,n.mode=A;case A:while(lt<16){if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}n.head&&(n.head.xflags=255&st,n.head.os=st>>8),512&n.flags&&(Ot[0]=255&st,Ot[1]=st>>>8&255,n.check=o(n.check,Ot,2,0)),st=0,lt=0,n.mode=T;case T:if(1024&n.flags){while(lt<16){if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}n.length=st,n.head&&(n.head.extra_len=st),512&n.flags&&(Ot[0]=255&st,Ot[1]=st>>>8&255,n.check=o(n.check,Ot,2,0)),st=0,lt=0}else n.head&&(n.head.extra=null);n.mode=O;case O:if(1024&n.flags&&(ft=n.length,ft>ot&&(ft=ot),ft&&(n.head&&(Et=n.head.extra_len-n.length,n.head.extra||(n.head.extra=new Array(n.head.extra_len)),r.arraySet(n.head.extra,tt,nt,ft,Et)),512&n.flags&&(n.check=o(n.check,tt,ft,nt)),ot-=ft,nt+=ft,n.length-=ft),n.length))break t;n.length=0,n.mode=S;case S:if(2048&n.flags){if(0===ot)break t;ft=0;do{Et=tt[nt+ft++],n.head&&Et&&n.length<65536&&(n.head.name+=String.fromCharCode(Et))}while(Et&&ft<ot);if(512&n.flags&&(n.check=o(n.check,tt,ft,nt)),ot-=ft,nt+=ft,Et)break t}else n.head&&(n.head.name=null);n.length=0,n.mode=R;case R:if(4096&n.flags){if(0===ot)break t;ft=0;do{Et=tt[nt+ft++],n.head&&Et&&n.length<65536&&(n.head.comment+=String.fromCharCode(Et))}while(Et&&ft<ot);if(512&n.flags&&(n.check=o(n.check,tt,ft,nt)),ot-=ft,nt+=ft,Et)break t}else n.head&&(n.head.comment=null);n.mode=I;case I:if(512&n.flags){while(lt<16){if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}if(st!==(65535&n.check)){t.msg="header crc mismatch",n.mode=J;break}st=0,lt=0}n.head&&(n.head.hcrc=n.flags>>9&1,n.head.done=!0),t.adler=n.check=0,n.mode=C;break;case P:while(lt<32){if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}t.adler=n.check=it(st),st=0,lt=0,n.mode=L;case L:if(0===n.havedict)return t.next_out=rt,t.avail_out=at,t.next_in=nt,t.avail_in=ot,n.hold=st,n.bits=lt,_;t.adler=n.check=1,n.mode=C;case C:if(e===c||e===d)break t;case j:if(n.last){st>>>=7&lt,lt-=7&lt,n.mode=V;break}while(lt<3){if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}switch(n.last=1&st,st>>>=1,lt-=1,3&st){case 0:n.mode=B;break;case 1:if(pt(n),n.mode=z,e===d){st>>>=2,lt-=2;break t}break;case 2:n.mode=U;break;case 3:t.msg="invalid block type",n.mode=J}st>>>=2,lt-=2;break;case B:st>>>=7&lt,lt-=7&lt;while(lt<32){if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}if((65535&st)!==(st>>>16^65535)){t.msg="invalid stored block lengths",n.mode=J;break}if(n.length=65535&st,st=0,lt=0,n.mode=N,e===d)break t;case N:n.mode=M;case M:if(ft=n.length,ft){if(ft>ot&&(ft=ot),ft>at&&(ft=at),0===ft)break t;r.arraySet(et,tt,nt,ft,rt),ot-=ft,nt+=ft,at-=ft,rt+=ft,n.length-=ft;break}n.mode=C;break;case U:while(lt<14){if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}if(n.nlen=257+(31&st),st>>>=5,lt-=5,n.ndist=1+(31&st),st>>>=5,lt-=5,n.ncode=4+(15&st),st>>>=4,lt-=4,n.nlen>286||n.ndist>30){t.msg="too many length or distance symbols",n.mode=J;break}n.have=0,n.mode=D;case D:while(n.have<n.ncode){while(lt<3){if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}n.lens[St[n.have++]]=7&st,st>>>=3,lt-=3}while(n.have<19)n.lens[St[n.have++]]=0;if(n.lencode=n.lendyn,n.lenbits=7,xt={bits:n.lenbits},kt=s(l,n.lens,0,19,n.lencode,0,n.work,xt),n.lenbits=xt.bits,kt){t.msg="invalid code lengths set",n.mode=J;break}n.have=0,n.mode=F;case F:while(n.have<n.nlen+n.ndist){for(;;){if(Tt=n.lencode[st&(1<<n.lenbits)-1],_t=Tt>>>24,yt=Tt>>>16&255,bt=65535&Tt,_t<=lt)break;if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}if(bt<16)st>>>=_t,lt-=_t,n.lens[n.have++]=bt;else{if(16===bt){At=_t+2;while(lt<At){if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}if(st>>>=_t,lt-=_t,0===n.have){t.msg="invalid bit length repeat",n.mode=J;break}Et=n.lens[n.have-1],ft=3+(3&st),st>>>=2,lt-=2}else if(17===bt){At=_t+3;while(lt<At){if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}st>>>=_t,lt-=_t,Et=0,ft=3+(7&st),st>>>=3,lt-=3}else{At=_t+7;while(lt<At){if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}st>>>=_t,lt-=_t,Et=0,ft=11+(127&st),st>>>=7,lt-=7}if(n.have+ft>n.nlen+n.ndist){t.msg="invalid bit length repeat",n.mode=J;break}while(ft--)n.lens[n.have++]=Et}}if(n.mode===J)break;if(0===n.lens[256]){t.msg="invalid code -- missing end-of-block",n.mode=J;break}if(n.lenbits=9,xt={bits:n.lenbits},kt=s(u,n.lens,0,n.nlen,n.lencode,0,n.work,xt),n.lenbits=xt.bits,kt){t.msg="invalid literal/lengths set",n.mode=J;break}if(n.distbits=6,n.distcode=n.distdyn,xt={bits:n.distbits},kt=s(h,n.lens,n.nlen,n.ndist,n.distcode,0,n.work,xt),n.distbits=xt.bits,kt){t.msg="invalid distances set",n.mode=J;break}if(n.mode=z,e===d)break t;case z:n.mode=Z;case Z:if(ot>=6&&at>=258){t.next_out=rt,t.avail_out=at,t.next_in=nt,t.avail_in=ot,n.hold=st,n.bits=lt,a(t,ht),rt=t.next_out,et=t.output,at=t.avail_out,nt=t.next_in,tt=t.input,ot=t.avail_in,st=n.hold,lt=n.bits,n.mode===C&&(n.back=-1);break}for(n.back=0;;){if(Tt=n.lencode[st&(1<<n.lenbits)-1],_t=Tt>>>24,yt=Tt>>>16&255,bt=65535&Tt,_t<=lt)break;if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}if(yt&&0===(240&yt)){for(wt=_t,mt=yt,vt=bt;;){if(Tt=n.lencode[vt+((st&(1<<wt+mt)-1)>>wt)],_t=Tt>>>24,yt=Tt>>>16&255,bt=65535&Tt,wt+_t<=lt)break;if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}st>>>=wt,lt-=wt,n.back+=wt}if(st>>>=_t,lt-=_t,n.back+=_t,n.length=bt,0===yt){n.mode=G;break}if(32&yt){n.back=-1,n.mode=C;break}if(64&yt){t.msg="invalid literal/length code",n.mode=J;break}n.extra=15&yt,n.mode=Y;case Y:if(n.extra){At=n.extra;while(lt<At){if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}n.length+=st&(1<<n.extra)-1,st>>>=n.extra,lt-=n.extra,n.back+=n.extra}n.was=n.length,n.mode=H;case H:for(;;){if(Tt=n.distcode[st&(1<<n.distbits)-1],_t=Tt>>>24,yt=Tt>>>16&255,bt=65535&Tt,_t<=lt)break;if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}if(0===(240&yt)){for(wt=_t,mt=yt,vt=bt;;){if(Tt=n.distcode[vt+((st&(1<<wt+mt)-1)>>wt)],_t=Tt>>>24,yt=Tt>>>16&255,bt=65535&Tt,wt+_t<=lt)break;if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}st>>>=wt,lt-=wt,n.back+=wt}if(st>>>=_t,lt-=_t,n.back+=_t,64&yt){t.msg="invalid distance code",n.mode=J;break}n.offset=bt,n.extra=15&yt,n.mode=W;case W:if(n.extra){At=n.extra;while(lt<At){if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}n.offset+=st&(1<<n.extra)-1,st>>>=n.extra,lt-=n.extra,n.back+=n.extra}if(n.offset>n.dmax){t.msg="invalid distance too far back",n.mode=J;break}n.mode=q;case q:if(0===at)break t;if(ft=ht-at,n.offset>ft){if(ft=n.offset-ft,ft>n.whave&&n.sane){t.msg="invalid distance too far back",n.mode=J;break}ft>n.wnext?(ft-=n.wnext,ct=n.wsize-ft):ct=n.wnext-ft,ft>n.length&&(ft=n.length),dt=n.window}else dt=et,ct=rt-n.offset,ft=n.length;ft>at&&(ft=at),at-=ft,n.length-=ft;do{et[rt++]=dt[ct++]}while(--ft);0===n.length&&(n.mode=Z);break;case G:if(0===at)break t;et[rt++]=n.length,at--,n.mode=Z;break;case V:if(n.wrap){while(lt<32){if(0===ot)break t;ot--,st|=tt[nt++]<<lt,lt+=8}if(ht-=at,t.total_out+=ht,n.total+=ht,ht&&(t.adler=n.check=n.flags?o(n.check,et,ht,rt-ht):i(n.check,et,ht,rt-ht)),ht=at,(n.flags?st:it(st))!==n.check){t.msg="incorrect data check",n.mode=J;break}st=0,lt=0}n.mode=K;case K:if(n.wrap&&n.flags){while(lt<32){if(0===ot)break t;ot--,st+=tt[nt++]<<lt,lt+=8}if(st!==(4294967295&n.total)){t.msg="incorrect length check",n.mode=J;break}st=0,lt=0}n.mode=$;case $:kt=g;break t;case J:kt=b;break t;case X:return w;case Q:default:return y}return t.next_out=rt,t.avail_out=at,t.next_in=nt,t.avail_in=ot,n.hold=st,n.bits=lt,(n.wsize||ht!==t.avail_out&&n.mode<J&&(n.mode<V||e!==f))&&gt(t,t.output,t.next_out,ht-t.avail_out)?(n.mode=X,w):(ut-=t.avail_in,ht-=t.avail_out,t.total_in+=ut,t.total_out+=ht,n.total+=ht,n.wrap&&ht&&(t.adler=n.check=n.flags?o(n.check,et,ht,t.next_out-ht):i(n.check,et,ht,t.next_out-ht)),t.data_type=n.bits+(n.last?64:0)+(n.mode===C?128:0)+(n.mode===z||n.mode===N?256:0),(0===ut&&0===ht||e===f)&&kt===p&&(kt=m),kt)}function yt(t){if(!t||!t.state)return y;var e=t.state;return e.window&&(e.window=null),t.state=null,p}function bt(t,e){var n;return t&&t.state?(n=t.state,0===(2&n.wrap)?y:(n.head=e,e.done=!1,p)):y}function wt(t,e){var n,r,o,a=e.length;return t&&t.state?(n=t.state,0!==n.wrap&&n.mode!==L?y:n.mode===L&&(r=1,r=i(r,e,a,0),r!==n.check)?b:(o=gt(t,e,a,a),o?(n.mode=X,w):(n.havedict=1,p))):y}e.inflateReset=st,e.inflateReset2=lt,e.inflateResetKeep=at,e.inflateInit=ht,e.inflateInit2=ut,e.inflate=_t,e.inflateEnd=yt,e.inflateGetHeader=bt,e.inflateSetDictionary=wt,e.inflateInfo="pako inflate (from Nodeca project)"},function(t,e,n){"use strict";var r=30,i=12;t.exports=function(t,e){var n,o,a,s,l,u,h,f,c,d,p,g,_,y,b,w,m,v,E,k,x,A,T,O,S;n=t.state,o=t.next_in,O=t.input,a=o+(t.avail_in-5),s=t.next_out,S=t.output,l=s-(e-t.avail_out),u=s+(t.avail_out-257),h=n.dmax,f=n.wsize,c=n.whave,d=n.wnext,p=n.window,g=n.hold,_=n.bits,y=n.lencode,b=n.distcode,w=(1<<n.lenbits)-1,m=(1<<n.distbits)-1;t:do{_<15&&(g+=O[o++]<<_,_+=8,g+=O[o++]<<_,_+=8),v=y[g&w];e:for(;;){if(E=v>>>24,g>>>=E,_-=E,E=v>>>16&255,0===E)S[s++]=65535&v;else{if(!(16&E)){if(0===(64&E)){v=y[(65535&v)+(g&(1<<E)-1)];continue e}if(32&E){n.mode=i;break t}t.msg="invalid literal/length code",n.mode=r;break t}k=65535&v,E&=15,E&&(_<E&&(g+=O[o++]<<_,_+=8),k+=g&(1<<E)-1,g>>>=E,_-=E),_<15&&(g+=O[o++]<<_,_+=8,g+=O[o++]<<_,_+=8),v=b[g&m];n:for(;;){if(E=v>>>24,g>>>=E,_-=E,E=v>>>16&255,!(16&E)){if(0===(64&E)){v=b[(65535&v)+(g&(1<<E)-1)];continue n}t.msg="invalid distance code",n.mode=r;break t}if(x=65535&v,E&=15,_<E&&(g+=O[o++]<<_,_+=8,_<E&&(g+=O[o++]<<_,_+=8)),x+=g&(1<<E)-1,x>h){t.msg="invalid distance too far back",n.mode=r;break t}if(g>>>=E,_-=E,E=s-l,x>E){if(E=x-E,E>c&&n.sane){t.msg="invalid distance too far back",n.mode=r;break t}if(A=0,T=p,0===d){if(A+=f-E,E<k){k-=E;do{S[s++]=p[A++]}while(--E);A=s-x,T=S}}else if(d<E){if(A+=f+d-E,E-=d,E<k){k-=E;do{S[s++]=p[A++]}while(--E);if(A=0,d<k){E=d,k-=E;do{S[s++]=p[A++]}while(--E);A=s-x,T=S}}}else if(A+=d-E,E<k){k-=E;do{S[s++]=p[A++]}while(--E);A=s-x,T=S}while(k>2)S[s++]=T[A++],S[s++]=T[A++],S[s++]=T[A++],k-=3;k&&(S[s++]=T[A++],k>1&&(S[s++]=T[A++]))}else{A=s-x;do{S[s++]=S[A++],S[s++]=S[A++],S[s++]=S[A++],k-=3}while(k>2);k&&(S[s++]=S[A++],k>1&&(S[s++]=S[A++]))}break}}break}}while(o<a&&s<u);k=_>>3,o-=k,_-=k<<3,g&=(1<<_)-1,t.next_in=o,t.next_out=s,t.avail_in=o<a?a-o+5:5-(o-a),t.avail_out=s<u?u-s+257:257-(s-u),n.hold=g,n.bits=_}},function(t,e,n){"use strict";var r=n(56),i=15,o=852,a=592,s=0,l=1,u=2,h=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],f=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],c=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],d=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];t.exports=function(t,e,n,p,g,_,y,b){var w,m,v,E,k,x,A,T,O,S=b.bits,R=0,I=0,P=0,L=0,C=0,j=0,B=0,N=0,M=0,U=0,D=null,F=0,z=new r.Buf16(i+1),Z=new r.Buf16(i+1),Y=null,H=0;for(R=0;R<=i;R++)z[R]=0;for(I=0;I<p;I++)z[e[n+I]]++;for(C=S,L=i;L>=1;L--)if(0!==z[L])break;if(C>L&&(C=L),0===L)return g[_++]=20971520,g[_++]=20971520,b.bits=1,0;for(P=1;P<L;P++)if(0!==z[P])break;for(C<P&&(C=P),N=1,R=1;R<=i;R++)if(N<<=1,N-=z[R],N<0)return-1;if(N>0&&(t===s||1!==L))return-1;for(Z[1]=0,R=1;R<i;R++)Z[R+1]=Z[R]+z[R];for(I=0;I<p;I++)0!==e[n+I]&&(y[Z[e[n+I]]++]=I);if(t===s?(D=Y=y,x=19):t===l?(D=h,F-=257,Y=f,H-=257,x=256):(D=c,Y=d,x=-1),U=0,I=0,R=P,k=_,j=C,B=0,v=-1,M=1<<C,E=M-1,t===l&&M>o||t===u&&M>a)return 1;for(;;){A=R-B,y[I]<x?(T=0,O=y[I]):y[I]>x?(T=Y[H+y[I]],O=D[F+y[I]]):(T=96,O=0),w=1<<R-B,m=1<<j,P=m;do{m-=w,g[k+(U>>B)+m]=A<<24|T<<16|O|0}while(0!==m);w=1<<R-1;while(U&w)w>>=1;if(0!==w?(U&=w-1,U+=w):U=0,I++,0===--z[R]){if(R===L)break;R=e[n+y[I]]}if(R>C&&(U&E)!==v){0===B&&(B=C),k+=P,j=R-B,N=1<<j;while(j+B<L){if(N-=z[j+B],N<=0)break;j++,N<<=1}if(M+=1<<j,t===l&&M>o||t===u&&M>a)return 1;v=U&E,g[v]=C<<24|j<<16|k-_|0}}return 0!==U&&(g[k+U]=R-B<<24|64<<16|0),b.bits=C,0}},function(t,e,n){"use strict";t.exports={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8}},function(t,e,n){"use strict";(function(e){let r=n(30),i=n(239),o=n(240),a=t.exports=function(t){i.call(this);let n=[],r=this;this._filter=new o(t,{read:this.read.bind(this),write:function(t){n.push(t)},complete:function(){r.emit("complete",e.concat(n))}}),this._filter.start()};r.inherits(a,i)}).call(this,n(19).Buffer)},function(t,e,n){"use strict";(function(e){let r=n(30),i=n(40),o=n(50),a=n(247),s=t.exports=function(t){i.call(this);let e=t||{};this._packer=new a(e),this._deflate=this._packer.createDeflate(),this.readable=!0};r.inherits(s,i),s.prototype.pack=function(t,n,r,i){this.emit("data",e.from(o.PNG_SIGNATURE)),this.emit("data",this._packer.packIHDR(n,r)),i&&this.emit("data",this._packer.packGAMA(i));let a=this._packer.filterData(t,n,r);this._deflate.on("error",this.emit.bind(this,"error")),this._deflate.on("data",function(t){this.emit("data",this._packer.packIDAT(t))}.bind(this)),this._deflate.on("end",function(){this.emit("data",this._packer.packIEND()),this.emit("end")}.bind(this)),this._deflate.end(a)}}).call(this,n(19).Buffer)},function(t,e,n){"use strict";(function(e){let r=n(50);t.exports=function(t,n,i,o){let a=-1!==[r.COLORTYPE_COLOR_ALPHA,r.COLORTYPE_ALPHA].indexOf(o.colorType);if(o.colorType===o.inputColorType){let e=function(){let t=new ArrayBuffer(2);return new DataView(t).setInt16(0,256,!0),256!==new Int16Array(t)[0]}();if(8===o.bitDepth||16===o.bitDepth&&e)return t}let s=16!==o.bitDepth?t:new Uint16Array(t.buffer),l=255,u=r.COLORTYPE_TO_BPP_MAP[o.inputColorType];4!==u||o.inputHasAlpha||(u=3);let h=r.COLORTYPE_TO_BPP_MAP[o.colorType];16===o.bitDepth&&(l=65535,h*=2);let f=e.alloc(n*i*h),c=0,d=0,p=o.bgColor||{};function g(){let t,e,n,i=l;switch(o.inputColorType){case r.COLORTYPE_COLOR_ALPHA:i=s[c+3],t=s[c],e=s[c+1],n=s[c+2];break;case r.COLORTYPE_COLOR:t=s[c],e=s[c+1],n=s[c+2];break;case r.COLORTYPE_ALPHA:i=s[c+1],t=s[c],e=t,n=t;break;case r.COLORTYPE_GRAYSCALE:t=s[c],e=t,n=t;break;default:throw new Error("input color type:"+o.inputColorType+" is not supported at present")}return o.inputHasAlpha&&(a||(i/=l,t=Math.min(Math.max(Math.round((1-i)*p.red+i*t),0),l),e=Math.min(Math.max(Math.round((1-i)*p.green+i*e),0),l),n=Math.min(Math.max(Math.round((1-i)*p.blue+i*n),0),l))),{red:t,green:e,blue:n,alpha:i}}void 0===p.red&&(p.red=l),void 0===p.green&&(p.green=l),void 0===p.blue&&(p.blue=l);for(let t=0;t<i;t++)for(let t=0;t<n;t++){let t=g(s,c);switch(o.colorType){case r.COLORTYPE_COLOR_ALPHA:case r.COLORTYPE_COLOR:8===o.bitDepth?(f[d]=t.red,f[d+1]=t.green,f[d+2]=t.blue,a&&(f[d+3]=t.alpha)):(f.writeUInt16BE(t.red,d),f.writeUInt16BE(t.green,d+2),f.writeUInt16BE(t.blue,d+4),a&&f.writeUInt16BE(t.alpha,d+6));break;case r.COLORTYPE_ALPHA:case r.COLORTYPE_GRAYSCALE:{let e=(t.red+t.green+t.blue)/3;8===o.bitDepth?(f[d]=e,a&&(f[d+1]=t.alpha)):(f.writeUInt16BE(e,d),a&&f.writeUInt16BE(t.alpha,d+2));break}default:throw new Error("unrecognised color Type "+o.colorType)}c+=u,d+=h}return f}}).call(this,n(19).Buffer)},function(t,e,n){"use strict";(function(e){let r=n(242);function i(t,e,n,r,i){for(let o=0;o<n;o++)r[i+o]=t[e+o]}function o(t,e,n){let r=0,i=e+n;for(let n=e;n<i;n++)r+=Math.abs(t[n]);return r}function a(t,e,n,r,i,o){for(let a=0;a<n;a++){let n=a>=o?t[e+a-o]:0,s=t[e+a]-n;r[i+a]=s}}function s(t,e,n,r){let i=0;for(let o=0;o<n;o++){let n=o>=r?t[e+o-r]:0,a=t[e+o]-n;i+=Math.abs(a)}return i}function l(t,e,n,r,i){for(let o=0;o<n;o++){let a=e>0?t[e+o-n]:0,s=t[e+o]-a;r[i+o]=s}}function u(t,e,n){let r=0,i=e+n;for(let o=e;o<i;o++){let i=e>0?t[o-n]:0,a=t[o]-i;r+=Math.abs(a)}return r}function h(t,e,n,r,i,o){for(let a=0;a<n;a++){let s=a>=o?t[e+a-o]:0,l=e>0?t[e+a-n]:0,u=t[e+a]-(s+l>>1);r[i+a]=u}}function f(t,e,n,r){let i=0;for(let o=0;o<n;o++){let a=o>=r?t[e+o-r]:0,s=e>0?t[e+o-n]:0,l=t[e+o]-(a+s>>1);i+=Math.abs(l)}return i}function c(t,e,n,i,o,a){for(let s=0;s<n;s++){let l=s>=a?t[e+s-a]:0,u=e>0?t[e+s-n]:0,h=e>0&&s>=a?t[e+s-(n+a)]:0,f=t[e+s]-r(l,u,h);i[o+s]=f}}function d(t,e,n,i){let o=0;for(let a=0;a<n;a++){let s=a>=i?t[e+a-i]:0,l=e>0?t[e+a-n]:0,u=e>0&&a>=i?t[e+a-(n+i)]:0,h=t[e+a]-r(s,l,u);o+=Math.abs(h)}return o}let p={0:i,1:a,2:l,3:h,4:c},g={0:o,1:s,2:u,3:f,4:d};t.exports=function(t,n,r,i,o){let a;if("filterType"in i&&-1!==i.filterType){if("number"!==typeof i.filterType)throw new Error("unrecognised filter types");a=[i.filterType]}else a=[0,1,2,3,4];16===i.bitDepth&&(o*=2);let s=n*o,l=0,u=0,h=e.alloc((s+1)*r),f=a[0];for(let e=0;e<r;e++){if(a.length>1){let e=1/0;for(let n=0;n<a.length;n++){let r=g[a[n]](t,u,s,o);r<e&&(f=a[n],e=r)}}h[l]=f,l++,p[f](t,u,s,h,l,o),l+=s,u+=s}return h}}).call(this,n(19).Buffer)},function(t,e,n){"use strict";let r=n(376),i=n(379);e.read=function(t,e){return r(t,e||{})},e.write=function(t,e){return i(t,e)}},function(t,e,n){"use strict";(function(e){let r=!0,i=n(49),o=n(377);i.deflateSync||(r=!1);let a=n(248),s=n(378),l=n(243),u=n(245),h=n(246);t.exports=function(t,n){if(!r)throw new Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let f,c,d;function p(t){f=t}function g(t){c=t}function _(t){c.transColor=t}function y(t){c.palette=t}function b(){c.alpha=!0}function w(t){d=t}let m=[];function v(t){m.push(t)}let E=new a(t),k=new l(n,{read:E.read.bind(E),error:p,metadata:g,gamma:w,palette:y,transColor:_,inflateData:v,simpleTransparency:b});if(k.start(),E.process(),f)throw f;let x,A=e.concat(m);if(m.length=0,c.interlace)x=i.inflateSync(A);else{let t=1+(c.width*c.bpp*c.depth+7>>3),e=t*c.height;x=o(A,{chunkSize:e,maxLength:e})}if(A=null,!x||!x.length)throw new Error("bad png - invalid inflate data response");let T=s.process(x,c);A=null;let O=u.dataToBitMap(T,c);T=null;let S=h(O,c);return c.data=S,c.gamma=d||0,c}}).call(this,n(19).Buffer)},function(t,e,n){"use strict";(function(r,i){let o=n(72).ok,a=n(49),s=n(30),l=n(19).kMaxLength;function u(t){if(!(this instanceof u))return new u(t);t&&t.chunkSize<a.Z_MIN_CHUNK&&(t.chunkSize=a.Z_MIN_CHUNK),a.Inflate.call(this,t),this._offset=void 0===this._offset?this._outOffset:this._offset,this._buffer=this._buffer||this._outBuffer,t&&null!=t.maxLength&&(this._maxLength=t.maxLength)}function h(t){return new u(t)}function f(t,e){e&&r.nextTick(e),t._handle&&(t._handle.close(),t._handle=null)}function c(t,e){if("string"===typeof e&&(e=i.from(e)),!(e instanceof i))throw new TypeError("Not a string or buffer");let n=t._finishFlushFlag;return null==n&&(n=a.Z_FINISH),t._processChunk(e,n)}function d(t,e){return c(new u(e),t)}u.prototype._processChunk=function(t,e,n){if("function"===typeof n)return a.Inflate._processChunk.call(this,t,e,n);let r,s,u=this,h=t&&t.length,c=this._chunkSize-this._offset,d=this._maxLength,p=0,g=[],_=0;function y(t,e){if(u._hadError)return;let n=c-e;if(o(n>=0,"have should not go down"),n>0){let t=u._buffer.slice(u._offset,u._offset+n);if(u._offset+=n,t.length>d&&(t=t.slice(0,d)),g.push(t),_+=t.length,d-=t.length,0===d)return!1}return(0===e||u._offset>=u._chunkSize)&&(c=u._chunkSize,u._offset=0,u._buffer=i.allocUnsafe(u._chunkSize)),0===e&&(p+=h-t,h=t,!0)}this.on("error",(function(t){r=t})),o(this._handle,"zlib binding closed");do{s=this._handle.writeSync(e,t,p,h,this._buffer,this._offset,c),s=s||this._writeState}while(!this._hadError&&y(s[0],s[1]));if(this._hadError)throw r;if(_>=l)throw f(this),new RangeError("Cannot create final Buffer. It would be larger than 0x"+l.toString(16)+" bytes");let b=i.concat(g,_);return f(this),b},s.inherits(u,a.Inflate),t.exports=e=d,e.Inflate=u,e.createInflate=h,e.inflateSync=d}).call(this,n(26),n(19).Buffer)},function(t,e,n){"use strict";(function(t){let r=n(248),i=n(240);e.process=function(e,n){let o=[],a=new r(e),s=new i(n,{read:a.read.bind(a),write:function(t){o.push(t)},complete:function(){}});return s.start(),a.process(),t.concat(o)}}).call(this,n(19).Buffer)},function(t,e,n){"use strict";(function(e){let r=!0,i=n(49);i.deflateSync||(r=!1);let o=n(50),a=n(247);t.exports=function(t,n){if(!r)throw new Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");let s=n||{},l=new a(s),u=[];u.push(e.from(o.PNG_SIGNATURE)),u.push(l.packIHDR(t.width,t.height)),t.gamma&&u.push(l.packGAMA(t.gamma));let h=l.filterData(t.data,t.width,t.height),f=i.deflateSync(h,l.getDeflateOptions());if(h=null,!f||!f.length)throw new Error("bad png - invalid compressed data response");return u.push(l.packIDAT(f)),u.push(l.packIEND()),e.concat(u)}}).call(this,n(19).Buffer)},function(t,e,n){const r=n(57),i={WW:" ",WB:"\u2584",BB:"\u2588",BW:"\u2580"},o={BB:" ",BW:"\u2584",WW:"\u2588",WB:"\u2580"};function a(t,e,n){return t&&e?n.BB:t&&!e?n.BW:!t&&e?n.WB:n.WW}e.render=function(t,e,n){const s=r.getOptions(e);let l=i;"#ffffff"!==s.color.dark.hex&&"#000000"!==s.color.light.hex||(l=o);const u=t.modules.size,h=t.modules.data;let f="",c=Array(u+2*s.margin+1).join(l.WW);c=Array(s.margin/2+1).join(c+"\n");const d=Array(s.margin+1).join(l.WW);f+=c;for(let t=0;t<u;t+=2){f+=d;for(let e=0;e<u;e++){const n=h[t*u+e],r=h[(t+1)*u+e];f+=a(n,r,l)}f+=d+"\n"}return f+=c.slice(0,-1),"function"===typeof n&&n(null,f),f},e.renderToFile=function(t,r,i,o){"undefined"===typeof o&&(o=i,i=void 0);const a=n(69),s=e.render(r,i);a.writeFile(t,s,o)}},function(t,e,n){const r=n(382),i=n(383);e.render=function(t,e,n){return e&&e.small?i.render(t,e,n):r.render(t,e,n)}},function(t,e){e.render=function(t,e,n){const r=t.modules.size,i=t.modules.data,o="\x1b[40m  \x1b[0m",a="\x1b[47m  \x1b[0m";let s="";const l=Array(r+3).join(a),u=Array(2).join(a);s+=l+"\n";for(let t=0;t<r;++t){s+=a;for(let e=0;e<r;e++)s+=i[t*r+e]?o:a;s+=u+"\n"}return s+=l+"\n","function"===typeof n&&n(null,s),s}},function(t,e){const n="\x1b[47m",r="\x1b[40m",i="\x1b[37m",o="\x1b[30m",a="\x1b[0m",s=n+o,l=r+i,u=function(t,e,n){return{"00":a+" "+t,"01":a+e+"\u2584"+t,"02":a+n+"\u2584"+t,10:a+e+"\u2580"+t,11:" ",12:"\u2584",20:a+n+"\u2580"+t,21:"\u2580",22:"\u2588"}},h=function(t,e,n,r){const i=e+1;if(n>=i||r>=i||r<-1||n<-1)return"0";if(n>=e||r>=e||r<0||n<0)return"1";const o=r*e+n;return t[o]?"2":"1"},f=function(t,e,n,r){return h(t,e,n,r)+h(t,e,n,r+1)};e.render=function(t,e,n){const r=t.modules.size,h=t.modules.data,c=!(!e||!e.inverse),d=e&&e.inverse?l:s,p=c?o:i,g=c?i:o,_=u(d,p,g),y=a+"\n"+d;let b=d;for(let t=-1;t<r+1;t+=2){for(let e=-1;e<r;e++)b+=_[f(h,r,e,t)];b+=_[f(h,r,r,t)]+y}return b+=a,"function"===typeof n&&n(null,b),b}},function(t,e,n){const r=n(249);e.render=r.render,e.renderToFile=function(t,r,i,o){"undefined"===typeof o&&(o=i,i=void 0);const a=n(69),s=e.render(r,i),l='<?xml version="1.0" encoding="utf-8"?><!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">'+s;a.writeFile(t,l,o)}},function(t,e,n){const r=n(221),i=n(222),o=n(386),a=n(249);function s(t,e,n,o,a){const s=[].slice.call(arguments,1),l=s.length,u="function"===typeof s[l-1];if(!u&&!r())throw new Error("Callback required as last argument");if(!u){if(l<1)throw new Error("Too few arguments provided");return 1===l?(n=e,e=o=void 0):2!==l||e.getContext||(o=n,n=e,e=void 0),new Promise((function(r,a){try{const a=i.create(n,o);r(t(a,e,o))}catch(t){a(t)}}))}if(l<2)throw new Error("Too few arguments provided");2===l?(a=n,n=e,e=o=void 0):3===l&&(e.getContext&&"undefined"===typeof a?(a=o,o=void 0):(a=o,o=n,n=e,e=void 0));try{const r=i.create(n,o);a(null,t(r,e,o))}catch(t){a(t)}}e.create=i.create,e.toCanvas=s.bind(null,o.render),e.toDataURL=s.bind(null,o.renderToDataURL),e.toString=s.bind(null,(function(t,e,n){return a.render(t,n)}))},function(t,e,n){(function(t){const r=n(57);function i(t,e,n){t.clearRect(0,0,e.width,e.height),e.style||(e.style={}),e.height=n,e.width=n,e.style.height=n+"px",e.style.width=n+"px"}function o(){try{return t.createElement("canvas")}catch(t){throw new Error("You need to specify a canvas element")}}e.render=function(t,e,n){let a=n,s=e;"undefined"!==typeof a||e&&e.getContext||(a=e,e=void 0),e||(s=o()),a=r.getOptions(a);const l=r.getImageWidth(t.modules.size,a),u=s.getContext("2d"),h=u.createImageData(l,l);return r.qrToImageData(h.data,t,a),i(u,s,l),u.putImageData(h,0,0),s},e.renderToDataURL=function(t,n,r){let i=r;"undefined"!==typeof i||n&&n.getContext||(i=n,n=void 0),i||(i={});const o=e.render(t,n,i),a=i.type||"image/png",s=i.rendererOpts||{};return o.toDataURL(a,s.quality)}}).call(this,n(9)["document"])},function(t,e,n){},,,,,,,,,,,,,,,function(t,e,n){"use strict";n.r(e);var r=n(9),i=n(18),o=n(5),a=n(8),s=n(7),l=n(2),u=n(0),h=n(10),f=n(3),c=n.n(f),d=n(31),p=n.n(d),g=n(6),_=n(261),y=n.n(_),b=n(1),w=function(t){var e=t.txt,n=t.size,r=Object(l["useState"])(""),i=Object(s["a"])(r,2),h=i[0],f=i[1],c=Boolean(n)?n:530;return Object(l["useEffect"])(Object(a["a"])(Object(o["a"])().mark((function t(){var n;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:n={errorCorrectionLevel:"H",type:"image/jpeg",quality:1,margin:1,width:c,heigth:c},console.log("reload"),y.a.toDataURL(JSON.stringify(e),n).then((function(t){f(t)})).catch((function(t){}));case 3:case"end":return t.stop()}}),t)}))),[e]),Object(b["jsxs"])(u["View"],{style:{textAlign:"center",position:"relative"},children:[Object(b["jsx"])(u["Image"],{src:"".concat(g["a"].picUrl,"/bind/qrlogo.png"),mode:"widthFix",style:{width:"65rpx",height:"65rpx",position:"absolute",top:"42%",left:"46%"}}),Object(b["jsx"])(u["Image"],{src:h,mode:"widthFix",style:{width:"".concat(c,"rpx"),height:"".concat(c,"r600px")}})]})},m=w,v=n(22),E=n(47),k=n(4),x=n(20),A=n(262),T=n.n(A);n(387);function O(t){var e=g["a"].getQuery(),n=Object(l["useState"])(null),r=Object(s["a"])(n,2),f=r[0],d=r[1],_=Object(l["useState"])([]),y=Object(s["a"])(_,2),w=y[0],A=y[1],O=Object(l["useState"])(!1),S=Object(s["a"])(O,2),R=S[0],I=S[1],P=Object(l["useState"])(!0),L=Object(s["a"])(P,2),C=L[0],j=L[1],B=Object(l["useState"])(null),N=Object(s["a"])(B,2),M=N[0],U=N[1],D=Object(l["useState"])(!1),F=Object(s["a"])(D,2),z=F[0],Z=F[1],Y=Object(l["useState"])(!1),H=Object(s["a"])(Y,2),W=H[0],q=H[1],G=Object(l["useState"])(!1),V=Object(s["a"])(G,2),K=V[0],$=V[1],J=Object(x["c"])((function(t){return t.login})),X=function(t){return{txt:t,end:Object(k["a"])(Object(k["a"])().add(10,"m")).unix()}},Q=[{image:"".concat(g["a"].picUrl,"/bind/qr_1.png"),value:"\u5165\u56ed\u8bb0\u5f55"},{image:"".concat(g["a"].picUrl,"/bind/qr_2.png"),value:"\u7acb\u5373\u9884\u7ea6"},{image:"".concat(g["a"].picUrl,"/bind/qr_3.png"),value:"\u5b9e\u540d\u4fe1\u606f"},{image:"".concat(g["a"].picUrl,"/bind/qr_4.png"),value:"\u8054\u7cfb\u5ba2\u670d"}];Object(l["useEffect"])(Object(a["a"])(Object(o["a"])().mark((function t(){var n,r,i,a;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(c.a.showLoading({mask:!0,title:"\u8bfb\u53d6\u4e2d"}),J){t.next=4;break}return j(!1),t.abrupt("return",!1);case 4:return t.next=6,g["a"].getCardList();case 6:if(n=t.sent,c.a.hideLoading(),0!==n.enable_card_num){t.next=11;break}return c.a.redirectTo({url:"/pages/tips/index?type=101"}),t.abrupt("return",!1);case 11:r=g["a"].cardCheck(n.card_list,"now"),i=g["a"].cardCheck(n.card_list,"all"),A(i),a=e.data.code,!1===Boolean(a)&&(a=r.length>0?r[0].good_id:i[0].good_id),d(a),$(!0);case 18:case"end":return t.stop()}}),t)}))),[J]),Object(l["useEffect"])(Object(a["a"])(Object(o["a"])().mark((function t(){var e;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(null===f){t.next=8;break}return c.a.showLoading({mask:!0,title:"\u8bfb\u53d6\u4e2d"}),t.next=4,g["a"].Api.cardInfo({code:f});case 4:e=t.sent,200===e.code?(j(!1),U(e.data),Z(X(e.data.good_key))):(d(null),j(!1)),c.a.hideLoading(),setInterval((function(){Z(X(e.data.good_key))}),T()("2m"));case 8:case"end":return t.stop()}}),t)}))),[f]);var tt=function(t){switch(t){case 0:c.a.navigateTo({url:"/pages/my/use"});break;case 1:c.a.navigateTo({url:"/pages/sreach/book"});break;case 2:$(!0);break;case 3:c.a.makePhoneCall({phoneNumber:g["a"].phone});break}};return Object(b["jsxs"])(u["View"],{className:"index",children:[M?Object(b["jsxs"])(u["View"],{children:[Object(b["jsx"])(p.a,{title:!0,row:20,loading:C,children:null===f?Object(b["jsxs"])(u["View"],{style:{textAlign:"center",marginTop:"60px",color:"#333",fontSize:"16px"},children:[Object(b["jsx"])(u["View"],{children:Object(b["jsx"])(h["f"],{value:"calendar",size:"30",color:"#848181"})}),Object(b["jsx"])(u["View"],{style:{marginTop:"10px",color:"#848181"},children:"\u65e0\u6548\u5361"})]}):Object(b["jsxs"])(u["View"],{style:{marginTop:"".concat(g["a"].reTopH(10),"rpx"),padding:"0 20px"},children:[Object(b["jsxs"])(u["View"],{className:"at-row",children:[Object(b["jsx"])(u["View"],{className:"at-col at-col-1",children:Object(b["jsx"])(u["Image"],{src:M.user_img,mode:"aspectFill",style:{width:"70rpx",height:"70rpx",borderRadius:"70rpx",marginRight:"20rpx"}})}),Object(b["jsxs"])(u["View"],Object(i["a"])(Object(i["a"])(Object(i["a"])(Object(i["a"])({className:"at-col at-col-9"},"className","user_good_id"),"style",{marginLeft:"10rpx"}),"onClick",(function(){return I(!0)})),"children",[" No.",f," ",Object(b["jsx"])(u["Text"],{className:"cardBtn",children:"\u6362\u5361"})]))]}),Object(b["jsxs"])(u["View"],{className:"qr",children:[Object(b["jsx"])(u["View"],{className:"title",children:M.card_name}),Object(b["jsx"])(u["View"],{style:{fontSize:"14px",fontWeight:"bold",paddingBottom:"10px",color:"#06a506",textAlign:"center"},children:M.enableData}),Object(b["jsxs"])(u["View"],{children:[Object(b["jsx"])(p.a,{title:!0,row:11,loading:!Boolean(z),children:Object(b["jsx"])(m,{txt:z})}),Object(b["jsxs"])(u["View"],{className:"at-row reload",onClick:function(){Z(X(M.good_key))},children:[Object(b["jsx"])(u["View"],{className:"at-col at-col-6",style:{textAlign:"right"},children:Object(b["jsx"])(u["Image"],{src:"".concat(g["a"].picUrl,"/bind/reload.png"),mode:"widthFix",style:{width:"20px",height:"20px"}})}),Object(b["jsx"])(u["View"],{className:"at-col at-col-6 txt",children:Object(b["jsx"])(u["Text"],{children:"\u5237\u65b0"})})]}),Object(b["jsx"])(u["View"],{className:"note",children:Object(b["jsx"])(h["o"],{icon:"volume-plus",children:"\u8bf7\u5de5\u4f5c\u4eba\u5458\u6838\u9a8c\u4e8c\u7ef4\u7801\u548c\u5b9e\u540d\u4fe1\u606f\u540e\u5165\u56ed"})}),Object(b["jsx"])(u["View"],{style:{borderTop:"1px solid #EDEDED",width:"85%",margin:"10px auto"}}),Object(b["jsx"])(h["e"],{hasBorder:!0,data:Q,columnNum:4,onClick:function(t,e){tt(e)}})]})]})]})}),Object(b["jsx"])(h["c"],{isOpened:W,onClose:function(){q(!1)},children:Object(b["jsx"])(u["Image"],{style:{width:"100%",borderRadius:"15rpx"},mode:"widthFix",src:"".concat(g["a"].ip,"/go.jpg")})}),Object(b["jsx"])(h["d"],{isOpened:K,title:"\u8bf7\u666f\u533a\u5de5\u4f5c\u4eba\u5458\u6838\u9a8c\u5b9e\u540d\u4fe1\u606f\u540e\u5165\u56ed",onClose:function(){$(!1)},children:Object(b["jsx"])(u["View"],{style:{paddding:"25rpx"},children:Object(b["jsxs"])(u["View"],{className:"at-row",style:{width:"90%",margin:"20px auto"},children:[Object(b["jsx"])(u["View"],{className:"at-col at-col-4",children:Object(b["jsx"])(u["Image"],{src:M.user_img,mode:"aspectFit",className:"user_img_show"})}),Object(b["jsxs"])(u["View"],{className:"at-col at-col-9",style:{paddingLeft:"15rpx",fontSize:"30rpx",color:"#b3b1b1"},children:[Object(b["jsxs"])(u["View"],{style:{marginBottom:"20rpx"},children:["\u59d3",Object(b["jsx"])(u["View"],{style:{width:"30rpx",display:"inline-block"}}),"\u540d : ",Object(b["jsx"])(u["Text"],{style:{color:"#673ab7"},children:M.real_name})]}),Object(b["jsxs"])(u["View"],{style:{marginBottom:"20rpx"},children:["\u7535",Object(b["jsx"])(u["View"],{style:{width:"30rpx",display:"inline-block"}}),"\u8bdd : ",Object(b["jsx"])(u["Text"],{style:{color:"#673ab7"},children:M.user_tel})]}),Object(b["jsxs"])(u["View"],{style:{marginBottom:"20rpx"},children:["\u7c7b",Object(b["jsx"])(u["View"],{style:{width:"30rpx",display:"inline-block"}}),"\u578b : ",Object(b["jsx"])(u["Text"],{style:{color:"#673ab7"},children:M.id_card_type_str})]}),Object(b["jsxs"])(u["View"],{children:["\u8bc1\u4ef6\u53f7 : ",Object(b["jsx"])(u["Text"],{style:{color:"#673ab7"},children:M.id_card_no})]})]})]})})})]}):null,Object(b["jsx"])(v["a"],{now:2}),Object(b["jsx"])(h["d"],{isOpened:R,title:"\u8bf7\u60a8\u9009\u62e9\u5e74\u7968",onClose:function(){I(!1)},children:Object(b["jsx"])(u["View"],{style:{backgroundColor:"#fff",width:"93%",margin:"0 auto 20px auto"},children:w.map((function(t){return Object(b["jsx"])(E["a"],{data:t,oper:function(t){d(t),I(!1)}})}))})})]})}var S=O,R={navigationBarTitleText:"",navigationStyle:"custom",transparentTitle:"always",titlePenetrate:"YES"};Page(Object(r["createPageConfig"])(S,"pages/qr/index",{root:{cn:[]}},R||{}))}]),[[402,0,1,2,3]]]);