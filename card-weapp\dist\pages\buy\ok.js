(my["webpackJsonp"]=my["webpackJsonp"]||[]).push([[21],{426:function(e,t,c){"use strict";c.r(t);var n=c(9),a=c(5),i=c(8),r=c(7),s=c(2),o=c(0),d=c(3),l=c.n(d),j=c(32),b=c(6),p=(c(254),c(1));function x(e){var t=Object(s["useState"])([]),c=Object(r["a"])(t,2),n=c[0],d=c[1];return Object(s["useEffect"])(Object(i["a"])(Object(a["a"])().mark((function e(){var t;return Object(a["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return l.a.showLoading({mask:!0,title:"\u8bfb\u53d6\u4e2d"}),e.next=3,b["a"].Api.myOrders();case 3:t=e.sent,l.a.hideLoading(),200===t.code&&d(t.data[t.data.length-1]);case 6:case"end":return e.stop()}}),e)}))),[]),Object(p["jsxs"])(o["View"],{className:"index",children:[Object(p["jsx"])(j["a"],{}),Object(p["jsxs"])(o["View"],{style:{textAlign:"center",marginTop:"60px",color:"#333",fontSize:"18px"},children:[Object(p["jsx"])(o["View"],{children:Object(p["jsx"])(o["Image"],{src:"".concat(b["a"].picUrl,"/bind/ok.png"),mode:"widthFix",style:{width:"20%"}})}),Object(p["jsx"])(o["View"],{style:{marginTop:"10px",color:"#848181"},children:"\u8d2d\u4e70\u6210\u529f"}),Object(p["jsx"])(o["View"],{className:"okbtn",onClick:function(){l.a.redirectTo({url:"/pages/bind/index?key="+n.good_key})},children:"\u7acb\u5373\u7ed1\u5b9a\u6fc0\u6d3b"}),Object(p["jsx"])(o["View"],{className:"checkbtn",onClick:function(){l.a.redirectTo({url:"/pages/my/order"})},children:"\u67e5\u770b\u6fc0\u6d3b\u7801\uff0c\u6682\u4e0d\u7ed1\u5b9a"})]})]})}var u=x,g={navigationBarTitleText:"\u5e74\u7968\u8d2d\u4e70\u6210\u529f"};Page(Object(n["createPageConfig"])(u,"pages/buy/ok",{root:{cn:[]}},g||{}))}},[[426,0,1,2,3]]]);