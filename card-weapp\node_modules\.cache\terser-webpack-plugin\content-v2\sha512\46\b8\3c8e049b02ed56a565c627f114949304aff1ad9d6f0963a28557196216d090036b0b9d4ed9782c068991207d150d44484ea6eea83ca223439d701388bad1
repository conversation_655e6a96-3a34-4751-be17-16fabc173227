{"code": "(my[\"webpackJsonp\"]=my[\"webpackJsonp\"]||[]).push([[45],{391:function(e,a,t){},407:function(e,a,t){\"use strict\";t.r(a);var n=t(9),c=t(5),i=t(8),s=t(2),p=t(3),r=t.n(p),o=t(0),d=t(6),g=(t(391),t(1));function u(e){var a=r.a.useRouter(),t=a.params.id;return Object(s[\"useEffect\"])(Object(i[\"a\"])(Object(c[\"a\"])().mark((function e(){var a;return Object(c[\"a\"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return r.a.showLoading({title:\"\\u53d1\\u8d77\\u4e2d\"}),e.next=3,d[\"a\"].Api.pay({code:t,openid:d[\"a\"].getData(\"userInfo\").code,appid:\"wx99dcb5f0415eb002\"});case 3:a=e.sent,r.a.hideLoading(),console.log(a.code),0===a.code?r.a.requestPayment({timeStamp:a.data.timeStamp,nonceStr:a.data.nonceStr,package:a.data.package,signType:a.data.signType,paySign:a.data.sign,success:function(){r.a.redirectTo({url:\"/pages/tips/index?type=pay100\"})},fail:function(){r.a.redirectTo({url:\"/pages/tips/index?type=pay105\"})}}):r.a.redirectTo({url:\"/pages/tips/index?type=pay106\"});case 7:case\"end\":return e.stop()}}),e)}))),[]),Object(g[\"jsxs\"])(o[\"View\"],{style:\"text-align: center;padding-top: 100px;\",children:[Object(g[\"jsx\"])(o[\"Image\"],{src:\"https://test.qqyhmmwg.com/res/wbg/w.png\",className:\"pay_img\"}),Object(g[\"jsx\"])(o[\"View\"],{className:\"pay_memo\",children:\"\\u652f\\u4ed8\\u4e2d\\u8bf7\\u7a0d\\u7b49\"}),Object(g[\"jsx\"])(o[\"View\"],{className:\"pay_txt\",children:\"\\u652f\\u4ed8\\u8df3\\u8f6c\\u4e2d, \\u652f\\u4ed8\\u5b8c\\u6210, \\u9875\\u9762\\u81ea\\u52a8\\u8df3\\u8f6c\"})]})}var m=u,y={navigationBarTitleText:\"\\u8bf7\\u652f\\u4ed8\"};Page(Object(n[\"createPageConfig\"])(m,\"pages/pay/index\",{root:{cn:[]}},y||{}))}},[[407,0,1,2,3]]]);", "extractedComments": []}