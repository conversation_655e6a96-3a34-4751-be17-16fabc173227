(my["webpackJsonp"]=my["webpackJsonp"]||[]).push([[33],{418:function(e,a,t){"use strict";t.r(a);var c=t(9),n=t(5),i=t(8),s=t(7),r=t(2),o=t(3),l=t.n(o),j=t(20),b=t(0),u=t(10),x=t(65),m=t(22),d=t(6),h=(t(51),t(1));function g(e){var a=Object(r["useState"])(!1),t=Object(s["a"])(a,2),c=t[0],o=t[1],g=Object(j["c"])((function(e){return e.login})),p=Object(r["useState"])(!1),O=Object(s["a"])(p,2),f=O[0],w=O[1];return Object(r["useEffect"])(Object(i["a"])(Object(n["a"])().mark((function e(){var a;return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a=d["a"].getDataSafe("userInfo"),null!==a&&null!==a.token&&w(!0);case 2:case"end":return e.stop()}}),e)}))),[]),Object(h["jsxs"])(b["View"],{children:[Object(h["jsx"])(b["View"],{style:{height:"".concat(d["a"].reTopH(6),"rpx"),backgroundColor:"#fff"}}),f?Object(h["jsxs"])(b["View"],{className:"at-row my_avatr",children:[Object(h["jsx"])(b["View"],{className:"at-col at-col-3",children:Object(h["jsx"])(b["Image"],{src:d["a"].getDataSafe("userInfo").avatar?d["a"].getDataSafe("userInfo").avatar:"".concat(d["a"].picUrl,"/index/head.png"),mode:"widthFix",className:"myhead"})}),Object(h["jsxs"])(b["View"],{className:"at-col at-col-9",children:[Object(h["jsx"])(b["View"],{className:"nickname",children:d["a"].getDataSafe("userInfo").nickname?d["a"].getDataSafe("userInfo").nickname:"\u5df2\u767b\u5f55"}),Object(h["jsx"])(b["View"],{className:"nickinfo",children:"\u6b22\u8fce\u4f7f\u7528\u5317\u4eac\u98ce\u666f\u540d\u80dc\u5e74\u7968"})]})]}):Object(h["jsxs"])(b["View"],{className:"at-row my_avatr",onClick:function(){d["a"].getUserInfo((function(e){return w(e)}))},children:[Object(h["jsx"])(b["View"],{className:"at-col at-col-3",children:Object(h["jsx"])(b["Image"],{src:"".concat(d["a"].picUrl,"/index/head.png"),mode:"widthFix",className:"myhead"})}),Object(h["jsxs"])(b["View"],{className:"at-col at-col-9",children:[Object(h["jsx"])(b["View"],{className:"nickname",children:"\u8bf7\u767b\u5f55"}),Object(h["jsx"])(b["View"],{className:"nickinfo",children:"\u767b\u5f55\u67e5\u770b\u66f4\u591a\u5185\u5bb9"})]})]}),Object(h["jsxs"])(u["i"],{children:[Object(h["jsx"])(u["j"],{title:"\u6211\u7684\u5361\u5305",arrow:"right",thumb:"".concat(d["a"].picUrl,"/index/card.png"),onClick:function(){l.a.navigateTo({url:"/pages/my/card"})}}),Object(h["jsx"])(u["j"],{title:"\u9884\u7ea6\u8bb0\u5f55",arrow:"right",thumb:"".concat(d["a"].picUrl,"/index/book.png"),onClick:function(){l.a.navigateTo({url:"/pages/my/book"})}}),Object(h["jsx"])(u["j"],{title:"\u5165\u56ed\u8bb0\u5f55",arrow:"right",thumb:"".concat(d["a"].picUrl,"/index/plan.png"),onClick:function(){l.a.navigateTo({url:"/pages/my/use"})}}),Object(h["jsx"])(u["j"],{title:"\u6211\u7684\u8ba2\u5355",arrow:"right",thumb:"".concat(d["a"].picUrl,"/index/card.png"),onClick:function(){l.a.navigateTo({url:"/pages/my/order"})}})]}),Object(h["jsx"])(b["View"],{style:{height:"8px"}}),Object(h["jsxs"])(u["i"],{children:[Object(h["jsx"])(u["j"],{title:"\u6211\u7684\u6536\u85cf",arrow:"right",thumb:"".concat(d["a"].picUrl,"/index/love.png"),onClick:function(){l.a.navigateTo({url:"/pages/my/love"})}}),Object(h["jsx"])(u["j"],{title:"\u8054\u7cfb\u5ba2\u670d",arrow:"right",thumb:"".concat(d["a"].picUrl,"/index/help.png"),onClick:function(){l.a.makePhoneCall({phoneNumber:"4006091798"})}})]}),Object(h["jsxs"])(b["View"],{className:"cardList",children:[Object(h["jsx"])(b["Text"],{className:"lastuse",children:"\u6700\u8fd1\u4f7f\u7528"}),g?Object(h["jsx"])(x["a"],{callback:function(){return w(!0)},max:3}):null]}),Object(h["jsx"])(u["c"],{isOpened:c,onClose:function(){o(!1)},children:Object(h["jsx"])(b["Image"],{style:{width:"100%",borderRadius:"15rpx"},mode:"widthFix",src:"".concat(d["a"].ip,"/go.jpg")})}),Object(h["jsx"])(m["a"],{now:3})]})}var p=g,O={navigationBarTitleText:"",navigationStyle:"custom",transparentTitle:"always",titlePenetrate:"YES"};Page(Object(c["createPageConfig"])(p,"pages/my/index",{root:{cn:[]}},O||{}))}},[[418,0,1,2,3]]]);