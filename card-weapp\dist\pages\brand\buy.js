(my["webpackJsonp"]=my["webpackJsonp"]||[]).push([[15],{400:function(e,t,a){"use strict";a.r(t);var c=a(9),s=a(5),i=a(8),n=a(7),l=a(2),r=a(3),o=a.n(r),d=a(20),j=a(0),b=a(10),m=a(31),x=a.n(m),h=a(14),O=a(4),p=a(1),_=function(e){var t=Object(l["useState"])(["--","--","--"]),a=Object(n["a"])(t,2),c=a[0],s=a[1],i=Object(l["useState"])("--"),r=Object(n["a"])(i,2),o=r[0],d=r[1],m=Object(l["useState"])("m"),x=Object(n["a"])(m,2),h=x[0],_=x[1];return Object(l["useEffect"])((function(){var t=e.start,a=e.end,c=Object(O["a"])(),i=(Object(O["a"])(a).diff(c),Object(O["a"])(t).diff(c)),n=i>0?"will":"buy";_(n);"buy"===n?(Object(O["a"])(a).diff(c,"day"),O["a"].duration(Object(O["a"])(a).diff(c),"milliseconds")):(Object(O["a"])(t).diff(c,"day"),O["a"].duration(Object(O["a"])(t).diff(c),"milliseconds"));d("buy"===n?"\u7ed3\u675f\u4e8e":"\u5f00\u59cb\u4e8e"),s("buy"===n?Object(O["a"])(a).format("MM-DD-HH").split("-"):Object(O["a"])(t).format("MM-DD-HH").split("-")),_("m")}),[]),Object(p["jsx"])(j["View"],{children:Object(p["jsxs"])(j["View"],{className:"at-row",children:[Object(p["jsx"])(j["View"],{className:"at-col at-col-1",children:Object(p["jsx"])(b["f"],{value:"bell",size:"16",color:"#fa6f14"})}),Object(p["jsx"])(j["View"],{className:"at-col at-col-4",style:{textAlign:"center"},children:Object(p["jsx"])(j["Text"],{style:{fontSize:"24rpx"},children:o})}),"s"===h?Object(p["jsxs"])(j["View"],{className:"at-col at-col-7",children:[Object(p["jsx"])(j["View"],{className:"time_item time_month",children:c[0]}),Object(p["jsx"])(j["Text"],{style:{fontSize:"20rpx"},children:"\u65f6"}),Object(p["jsx"])(j["View"],{className:"time_item time_month",children:c[1]}),Object(p["jsx"])(j["Text"],{style:{fontSize:"20rpx"},children:"\u5206"}),Object(p["jsx"])(j["View"],{className:"time_item time_month",children:c[2]}),Object(p["jsx"])(j["Text"],{style:{fontSize:"20rpx"},children:"\u79d2"})]}):Object(p["jsxs"])(j["View"],{className:"at-col at-col-7",children:[Object(p["jsx"])(j["View"],{className:"time_item time_month",children:c[0]}),Object(p["jsx"])(j["Text"],{style:{fontSize:"20rpx"},children:"\u6708"}),Object(p["jsx"])(j["View"],{className:"time_item time_month",children:c[1]}),Object(p["jsx"])(j["Text"],{style:{fontSize:"20rpx"},children:"\u65e5"}),Object(p["jsx"])(j["View"],{className:"time_item time_month",children:c[2]}),Object(p["jsx"])(j["Text"],{style:{fontSize:"20rpx"},children:"\u65f6"})]})]})})};_.options={addGlobalClass:!0},_.defaultProps={start:"2020-12-12",end:"2020-12-12"};var u=_,w=a(6),g=function(e){var t=e.allT,a=e.openmm,c=e.weixinadinfo,r=e.coupon,d=Object(l["useState"])(w["a"].showNum),m=Object(n["a"])(d,2),x=m[0],O=m[1],_=Object(l["useState"])(!1),g=Object(n["a"])(_,2),f=g[0],N=g[1],V=Object(l["useState"])({tips:[]}),y=Object(n["a"])(V,2),T=y[0],v=y[1],S=Object(l["useState"])([]),k=Object(n["a"])(S,2),I=k[0],z=k[1],C=Object(l["useState"])(r),A=Object(n["a"])(C,2),P=(A[0],A[1]);(function(){try{Boolean(c)&&c.toString().split(".")[0]}catch(e){return 0}})();Object(l["useEffect"])((function(){P(r)}),[r]);var D=["\u6307\u5b9a\u65e5\u671f","\u8d85\u957f\u6709\u6548"];return Object(p["jsxs"])(j["View"],{children:[Object(p["jsxs"])(j["View"],{children:[Object(p["jsx"])(j["View"],{className:"brand_tt",style:{padding:0},children:t.map((function(e,t){return e.good.id&&t<x?Object(p["jsxs"])(j["View"],{children:[Object(p["jsxs"])(j["View"],{className:"at-row brand_tt_per",onClick:Object(i["a"])(Object(s["a"])().mark((function t(){var c,i;return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(0!==parseInt(e.good.place_origin,10)){t.next=6;break}v(Object(h["a"])(Object(h["a"])({},e.good),{},{name:e.name,itemname:null})),a(!1),N(!0),t.next=17;break;case 6:if(c=I.filter((function(t){return t.goods_id===e.good.id})),!(c.length>0)){t.next=11;break}z([]),t.next=17;break;case 11:return o.a.showLoading({title:"\u8bfb\u53d6\u4e2d",mask:!0}),t.next=14,w["a"].Api.getBrandDatePrice({id:e.good.id});case 14:i=t.sent,z(i.data.rules),o.a.hideLoading();case 17:case"end":return t.stop()}}),t)}))),children:[Object(p["jsxs"])(j["View"],{className:"at-col at-col-8",children:[1===parseInt(e.good.place_origin,10)?Object(p["jsx"])(j["View"],{style:{height:"10rpx"}}):null,Object(p["jsx"])(j["View"],{className:"at-col--wrap brand_tt_name",children:Object(p["jsx"])(j["Text"],{className:"brand_tt_name",children:e.name})}),Object(p["jsxs"])(j["View"],{className:"brand_tt_tags",children:[Object(p["jsx"])(j["Text"],{className:"brand_tt_tips",style:{color:"#28bb65"},children:w["a"].getlimt(e.good.limittype,e.good.limitnum)}),Object(p["jsx"])(j["Text"],{className:"brand_tt_shu",children:"|"}),Object(p["jsx"])(j["Text"],{className:"brand_tt_tips",style:{color:"#e23b14"},children:["\u968f\u65f6\u9000","\u6709\u6548\u53ef\u9000","\u6761\u4ef6\u9000"][parseInt(e.good.if_refund)]}),Object(p["jsxs"])(j["Text"],{style:-1!==w["a"].showUseDate(e.good.is_book,e.good.open_time,e.good.close_time)?{display:"inline-block"}:{display:"none"},children:[Object(p["jsx"])(j["Text"],{className:"brand_tt_shu",children:"|"}),Object(p["jsx"])(j["Text"],{className:"brand_tt_tips",style:{color:"#ff9800"},children:D[w["a"].showUseDate(e.good.is_book,e.good.open_time,e.good.close_time)]})]}),1===parseInt(e.good.place_origin,10)?null:Object(p["jsxs"])(j["Text"],{children:[Object(p["jsx"])(j["Text"],{className:"brand_tt_shu",children:"|"}),Object(p["jsx"])(j["Text"],{className:"brand_tt_tips",style:{color:"#d02114",fontWeight:"bold"},children:"\u8d2d\u4e70\u987b\u77e5 \u5fc5\u8bfb"})]}),"1"===e.good.buy_time&&0===parseInt(e.good.place_origin,10)?Object(p["jsx"])(j["View"],{style:{width:"60%",margin:"10rpx 0"},children:Object(p["jsx"])(u,{end:e.good.end_time,start:e.good.start_time})}):null]})]}),0===parseInt(e.good.place_origin,10)?Object(p["jsxs"])(j["View"],{className:"at-col at-col-4",style:{textAlign:"right"},children:[Object(p["jsxs"])(j["View"],{className:"brand_buy_price",children:[Object(p["jsx"])(j["Text"],{className:"price_t",children:"\xa5"})," ",w["a"].getPrice(e.good.share_price),Object(p["jsxs"])(j["Text"],{className:"brand_buy_showp",children:["\xa5",w["a"].getPrice(e.good.show_price)]})]}),"1"===e.good.buy_time?Object(p["jsx"])(j["View"],{className:"brand_buy_btn_time ",children:"\u62a2\u8d2d"}):Object(p["jsx"])(j["View"],{className:"brand_buy_btn",children:"\u8d2d\u4e70"})]}):Object(p["jsxs"])(j["View"],{className:"at-col at-col-4",style:{textAlign:"right"},children:[Object(p["jsxs"])(j["View"],{className:"brand_buy_price",children:[Object(p["jsx"])(j["Text"],{className:"price_t",children:"\xa5"})," ",w["a"].getPrice(e.good.share_price),Object(p["jsx"])(j["Text"],{className:"brand_buy_showqi",children:"\u8d77"})]}),Object(p["jsx"])(j["View"],{style:{color:"#fff",fontSize:"26rpx",background:"#03A9F4",width:"62%",textAlign:"center",marginLeft:"43%",borderRadius:"25rpx",padding:"8rpx 0"},children:"\u7acb\u5373\u9884\u5b9a"})]})]},e.id),I.map((function(t){return t.goods_id==e.good.id?Object(p["jsxs"])(j["View"],{className:"at-row brand_tt_per",style:{background:"#fbfbfb"},onClick:function(){v(Object(h["a"])(Object(h["a"])({},e.good),{},{rulescode:t.title_code,show_price:t.show_price,buy_price:t.buy_price,share_price:t.share_price,name:e.name,itemname:t.title})),a(!1),N(!0)},children:[Object(p["jsxs"])(j["View"],{className:"at-col at-col-8",children:[Object(p["jsx"])(j["View"],{className:"at-col--wrap brand_tt_name",children:Object(p["jsx"])(j["Text"],{className:"brand_tt_name",style:{fontSize:"26rpx",color:"#636161"},children:t.title})}),Object(p["jsx"])(j["View"],{className:"brand_tt_tags",children:Object(p["jsxs"])(j["View"],{style:{color:"red"},children:[Object(p["jsxs"])(j["Text"],{children:[Object(p["jsx"])(j["Text"],{className:"brand_tt_tips",style:{color:"#6fb2f9"},children:"\u8d2d\u4e70\u987b\u77e5"}),Object(p["jsx"])(j["Text"],{className:"brand_tt_shu",children:"|"})]}),w["a"].dateInfo(t.stime,t.etime,t.enable_week)]})})]}),Object(p["jsxs"])(j["View"],{className:"at-col at-col-4",style:{textAlign:"right"},children:[Object(p["jsxs"])(j["View"],{className:"brand_buy_price",children:[Object(p["jsx"])(j["Text"],{className:"price_t",children:"\xa5"})," ",w["a"].getPrice(t.share_price),Object(p["jsxs"])(j["Text"],{className:"brand_buy_showp",children:["\xa5",w["a"].getPrice(t.show_price)]})]}),Object(p["jsx"])(j["View"],{className:"brand_buy_btn",children:"\u8d2d\u4e70"})]})]},t.code):null}))]}):null}))}),t.length>w["a"].showNum?Object(p["jsx"])(j["View"],{className:"brand_show_all",onClick:function(){x===w["a"].showNum?O(99):O(w["a"].showNum)},children:x>w["a"].showNum?"\u6536\u8d77":"\u67e5\u770b\u5168\u90e8".concat(t.length,"\u7c7b\u4ea7\u54c1")}):null]}),f?Object(p["jsx"])(b["d"],{isOpened:f,onClose:function(){N(!1),a(!0)},title:"\u8d2d\u4e70\u987b\u77e5",children:Object(p["jsxs"])(j["View"],{className:"brand_tips",children:[Object(p["jsxs"])(j["View"],{className:"at-row",children:[Object(p["jsx"])(j["View"],{className:"at-col at-col-3",children:Object(p["jsx"])(j["Image"],{src:T.home_recommended_images,className:"brand_good_img",mode:"aspectFill"})}),Object(p["jsxs"])(j["View"],{className:"at-col at-col-9",style:{paddingLeft:"15rpx"},children:[Object(p["jsx"])(j["View"],{className:"at-col--wrap brand_tt_name",children:Object(p["jsx"])(j["Text"],{className:"brand_tt_name",children:w["a"].getSamll(T.name,15)})}),null!==T.itemname?Object(p["jsx"])(j["View"],{children:T.itemname}):null,Object(p["jsxs"])(j["View"],{className:"brand_tt_tags",style:null!==T.itemname?{padding:0}:{},children:[Object(p["jsx"])(j["Text"],{className:"brand_tt_tips",style:{color:"#28bb65"},children:w["a"].getlimt(T.limittype,T.limitnum)}),Object(p["jsx"])(j["Text"],{className:"brand_tt_shu",children:"|"}),Object(p["jsx"])(j["Text"],{className:"brand_tt_tips",style:{color:"#e23b14"},children:["\u968f\u65f6\u9000","\u6709\u6548\u53ef\u9000","\u6761\u4ef6\u9000"][parseInt(T.if_refund)]}),Object(p["jsxs"])(j["Text"],{style:-1!==w["a"].showUseDate(T.is_book,T.open_time,T.close_time)?{display:"inline-block"}:{display:"none"},children:[Object(p["jsx"])(j["Text"],{className:"brand_tt_shu",children:"|"}),Object(p["jsx"])(j["Text"],{className:"brand_tt_tips",style:{color:"#ff9800"},children:D[w["a"].showUseDate(T.is_book,T.open_time,T.close_time)]})]}),"1"===T.buy_time?Object(p["jsx"])(j["View"],{style:{width:"60%",margin:"10rpx 0"},children:Object(p["jsx"])(u,{end:T.end_time,start:T.start_time})}):null]})]})]}),0===T.tips.length?Object(p["jsx"])(j["View"],{children:T.content_web}):Object(p["jsxs"])(j["View"],{style:{paddingBottom:"50rpx"},children:[" ",T.tips.map((function(e){return Object(p["jsxs"])(j["View"],{children:[Object(p["jsx"])(j["View"],{style:{margin:"15rpx 0 5rpx 0",fontWeight:"bold"},children:e.title}),e.memo.includes('<span class="none"></span>')?Object(p["jsx"])(j["RichText"],{nodes:w["a"].removeCss(e.memo),style:{color:"#585656"}}):Object(p["jsx"])(j["Text"],{style:{color:"#585656"},children:e.memo}),Object(p["jsx"])(j["View"],{style:{height:"30rpx"}})]},T.title)}))]})]})}):null,f?Object(p["jsx"])(j["View"],{className:"brand_bar",children:Object(p["jsx"])(j["View"],{className:"at-row",children:Object(p["jsx"])(j["View"],{className:"at-col at-col-8",children:Object(p["jsxs"])(j["View"],{className:"brand_buy_price",style:{fontSize:"40rpx"},children:[Object(p["jsx"])(j["Text"],{className:"price_t",children:"\xa5"})," ",w["a"].getPrice(T.share_price),Object(p["jsxs"])(j["Text"],{className:"brand_buy_showp",children:["\xa5",w["a"].getPrice(T.show_price)]})]})})})}):null]})};g.defaultProps={allT:[],coupon:[]};var f=g;a(251);function N(e){var t=w["a"].getQuery(),a=Object(d["c"])((function(e){return e.login})),c=Object(l["useState"])(!0),m=Object(n["a"])(c,2),h=m[0],O=m[1],_=Object(l["useState"])(null),u=Object(n["a"])(_,2),g=u[0],N=u[1],V=Object(l["useState"])([]),y=Object(n["a"])(V,2),T=y[0],v=y[1],S=Object(l["useState"])({num:0,good_id:"--",list:[],name:"--"}),k=Object(n["a"])(S,2),I=k[0],z=k[1],C=t.data.v;return Object(l["useEffect"])(Object(i["a"])(Object(s["a"])().mark((function e(){var t,c,i,n;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!a||!Boolean(C)){e.next=15;break}return e.next=3,w["a"].Api.brandInfoZh(C);case 3:return t=e.sent,e.next=6,w["a"].Api.brandGoodZh(C);case 6:if(c=e.sent,200!==t.code){e.next=14;break}return e.next=10,w["a"].getCardList({list:t.data.brand.templet_list_enable});case 10:i=e.sent,i.enable_card_num>0&&(n=i.card_list.filter((function(e){return!0===e.enable})),z({num:n.length,good_id:n[0].good_id,list:n})),N(t.data),O(!1);case 14:0===c.code&&v(c.data.ticket);case 15:case"end":return e.stop()}}),e)}))),[a]),Object(r["useShareAppMessage"])((function(){return{title:g.brand.name,path:"/pages/brand/buy?v=".concat(C),imageUrl:"".concat(g.brand.logo,"?x-oss-process=image/resize,m_fill,h_400,w_400")}})),Object(p["jsx"])(j["View"],{children:null===g&&Boolean(C)&&0===T.length?Object(p["jsx"])(x.a,{title:!0,row:30,loading:h}):Object(p["jsxs"])(j["View"],{children:[Object(p["jsxs"])(j["View"],{className:"topNav",style:{position:"absolute"},children:[Object(p["jsx"])(j["View"],{style:{height:"".concat(w["a"].reTopH(6),"rpx")}}),Object(p["jsxs"])(j["View"],{className:"at-row",children:[Object(p["jsx"])(j["View"],{className:"at-col at-col-1 left",onClick:function(){o.a.navigateBack({delta:1,fail:function(){o.a.navigateTo({url:"/pages/index/index"})}})},children:Object(p["jsx"])(j["Image"],{src:"".concat(w["a"].picUrl,"/memu/back.png"),mode:"widthFix",className:"topIcon_b"})}),Object(p["jsx"])(j["View"],{className:"at-col at-col-9"})]})]}),Object(p["jsx"])(j["View"],{className:"brand_top_pic",children:Object(p["jsx"])(j["Swiper"],{className:"brand_swiper",indicatorColor:"#999",indicatorActiveColor:"#fff",circular:!0,indicatorDots:!0,autoplay:!0,children:g.brand.images.map((function(e){return Object(p["jsx"])(j["SwiperItem"],{children:Object(p["jsx"])(j["Image"],{src:e.path,mode:"heightfix",className:"images"})})}))})}),Object(p["jsxs"])(j["View"],{className:"brand_content",children:[Object(p["jsxs"])(j["View"],{className:"at-row",children:[Object(p["jsxs"])(j["View"],{className:"at-col at-col-9",children:[Object(p["jsx"])(j["View"],{className:"title",children:g.brand.name}),Object(p["jsx"])(j["View"],{className:"memo",style:{padding:0},children:g.brand.tagsname.map((function(e,t){return t<=3?Object(p["jsx"])(j["View"],{className:"tips_three",style:0===t?{marginLeft:"0"}:{},children:e.tagname}):null}))})]}),Object(p["jsx"])(j["View"],{className:"at-col at-col-3",children:Object(p["jsxs"])(j["View"],{style:{marginTop:"44px",color:"#777575",fontSize:"14px",textAlign:"right"},children:["\u5df2\u552e: ",g.brand.ordernum+g.brand.ordernum_show]})})]}),Object(p["jsxs"])(j["View"],{className:"at-row list_show",onClick:function(){o.a.makePhoneCall({phoneNumber:g.brand.tel})},children:[Object(p["jsx"])(j["View"],{className:"at-col at-col-1",children:Object(p["jsx"])(j["Image"],{src:"".concat(w["a"].picUrl,"/info/time.png"),mode:"widthFix",className:"icon"})}),Object(p["jsxs"])(j["View"],{className:"at-col at-col-10 text",children:["\u8425\u4e1a\u65f6\u95f4 ",null===g.brandTime?"\u5168\u5929":g.brandTime[0]]}),Object(p["jsx"])(j["View"],{className:"at-col at-col-1",children:Object(p["jsx"])(b["f"],{value:"phone",size:"18",color:"#afafaf"})})]}),Object(p["jsxs"])(j["View"],{className:"at-row list_show",onClick:function(){var e=w["a"].bdMap_to_txMap(g.brand.latitude,g.brand.longitude);o.a.openLocation({latitude:e.lat,longitude:e.lng,scale:15})},children:[Object(p["jsx"])(j["View"],{className:"at-col at-col-1",children:Object(p["jsx"])(j["Image"],{src:"".concat(w["a"].picUrl,"/info/map.png"),mode:"widthFix",className:"icon"})}),Object(p["jsx"])(j["View"],{className:"at-col at-col-10 text",children:"".concat(g.brand.province).concat(g.brand.city).concat(g.brand.address)}),Object(p["jsx"])(j["View"],{className:"at-col at-col-1",children:Object(p["jsx"])(b["f"],{value:"chevron-right",size:"18",color:"#afafaf"})})]}),Object(p["jsxs"])(j["View"],{className:"at-row list_show",children:[Object(p["jsx"])(j["View"],{className:"at-col at-col-1",children:Object(p["jsx"])(j["Image"],{src:"".concat(w["a"].picUrl,"/info/shop.png"),mode:"widthFix",className:"icon"})}),Object(p["jsx"])(j["View"],{className:"at-col at-col-10 text",style:{fontWeight:"bold"},children:"\u4ea7\u54c1\u9884\u8ba2"}),Object(p["jsx"])(j["View"],{className:"at-col at-col-1",children:Object(p["jsx"])(b["f"],{value:"chevron-down",size:"18",color:"#afafaf"})}),Object(p["jsx"])(j["View"],{})]}),T.length>0?Object(p["jsx"])(f,{allT:T,vid:0,rank:1,openmm:function(){},weixinadinfo:!1,coupon:[],card:I.num}):null]}),Object(p["jsxs"])(j["View"],{children:[Object(p["jsxs"])(j["View"],{className:"brand_title",children:[Object(p["jsx"])(j["View"],{className:"t_icon"}),Object(p["jsx"])(j["View"],{className:"t_name",children:"\u7279\u8272\u4ecb\u7ecd"})]}),Object(p["jsx"])(j["View"],{className:"brand_html",children:Object(p["jsx"])(j["RichText"],{nodes:w["a"].removeCss(g.brand.memo)})})]}),Object(p["jsx"])(j["View"],{style:{height:"80px"}})]})})}var V=N,y={navigationBarTitleText:"\u98ce\u666f\u540d\u80dc\u5e74\u7968",navigationStyle:"custom",enableShareAppMessage:!0,transparentTitle:"always",titlePenetrate:"YES"};V.enableShareAppMessage=!0;Page(Object(c["createPageConfig"])(V,"pages/brand/buy",{root:{cn:[]}},y||{}))}},[[400,0,1,2,3]]]);