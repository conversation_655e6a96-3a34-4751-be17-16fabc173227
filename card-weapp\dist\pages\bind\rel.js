(my["webpackJsonp"]=my["webpackJsonp"]||[]).push([[11],{411:function(e,t,a){"use strict";a.r(t);var n=a(9),c=a(5),i=a(8),r=a(7),o=a(2),s=a(0),j=a(3),b=a.n(j),l=a(10),u=a(6),p=a(32),d=(a(74),a(1));function O(e){var t=u["a"].getQuery(),a=Boolean(t.data.key)?t.data.key:"",n=Object(o["useState"])(0),j=Object(r["a"])(n,2),O=j[0],x=j[1];Object(o["useEffect"])(Object(i["a"])(Object(c["a"])().mark((function e(){return Object(c["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:case"end":return e.stop()}}),e)}))),[]);var f=function(e){x(e)};return Object(d["jsxs"])(s["View"],{children:[Object(d["jsx"])(p["a"],{}),Object(d["jsx"])(l["o"],{icon:"volume-plus",children:"\u4e3a\u65b9\u4fbf\u4f7f\u7528\u60a8\u53ef\u4e3a\u60a8\u7684\u5bb6\u4eba\u5f00\u901a\u5361\u7247\uff0c\u5c24\u5176\u662f\u8001\u4eba\u4e0e\u5b69\u5b50\u5466\uff5e\u795d\u60a8\u6e38\u73a9\u6109\u5feb\uff01"}),Object(d["jsx"])(s["View"],{style:{padding:"30px 20px"},children:Object(d["jsx"])(l["p"],{options:u["a"].relation,value:O,onClick:f.bind(this)})}),Object(d["jsx"])(s["View"],{className:"jbtn",onClick:function(){b.a.navigateTo({url:"/pages/bind/add?rel=".concat(O,"&key=").concat(a)})},children:"\u4e0b\u4e00\u6b65"})]})}var x=O,f={navigationBarTitleText:"\u4e3a\u8c01\u5f00\u901a"};Page(Object(n["createPageConfig"])(x,"pages/bind/rel",{root:{cn:[]}},f||{}))}},[[411,0,1,2,3]]]);