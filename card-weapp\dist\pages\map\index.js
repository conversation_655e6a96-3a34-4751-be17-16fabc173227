(my["webpackJsonp"]=my["webpackJsonp"]||[]).push([[27],{388:function(e,n,t){},405:function(e,n,t){"use strict";t.r(n);var a=t(9),c=t(5),r=t(8),o=t(7),i=t(2),s=t(3),u=t.n(s),l=t(20),p=t(0),d=t(22),v=t(6),b=(t(388),t(1)),f=function(){var e=Object(l["c"])((function(e){return e.login})),n=Object(i["useState"])([]),t=Object(o["a"])(n,2),a=t[0],s=t[1];Object(i["useEffect"])(Object(r["a"])(Object(c["a"])().mark((function n(){var t;return Object(c["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(!e){n.next=5;break}return n.next=3,v["a"].Api.brandMap();case 3:t=n.sent,s(t.data||[]);case 5:case"end":return n.stop()}}),n)}))),[e]);var f=function(e){var n=null;a.forEach((function(t){t.id===e&&(n=t.code)})),null!==n&&u.a.navigateTo({url:"/pages/brand/index?v=".concat(n)})};return Object(b["jsxs"])(p["Map"],{setting:{},scale:8,"show-compass":!0,"enable-overlooking":!0,markers:a,latitude:39.925218,longitude:116.404425,style:{height:"100vh",width:"100vw"},onCalloutTap:function(e){console.warn("v -------- v",e),f(e.markerId)},onMarkerTap:function(e){console.warn("v -------- v",e.markerId),f(e.markerId)},children:[Object(b["jsx"])(p["CoverView"],{slot:"callout",children:a.map((function(e){return Object(b["jsx"])(p["CoverView"],{"marker-id":e.code,height:10,iconPath:"".concat(v["a"].picUrl,"/memu/love.png")},e.code)}))}),Object(b["jsx"])(d["a"],{now:1})]})},j=f,g={navigationBarTitleText:"\u666f\u533a\u5730\u56fe"};Page(Object(a["createPageConfig"])(j,"pages/map/index",{root:{cn:[]}},g||{}))}},[[405,0,1,2,3]]]);