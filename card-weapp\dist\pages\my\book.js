(my["webpackJsonp"]=my["webpackJsonp"]||[]).push([[29],{401:function(e,t,a){"use strict";a.r(t);var c=a(9),n=a(5),i=a(15),s=a(36);function r(e,t){var a="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!a){if(Array.isArray(e)||(a=Object(s["a"])(e))||t&&e&&"number"===typeof e.length){a&&(e=a);var c=0,n=function(){};return{s:n,n:function(){return c>=e.length?{done:!0}:{done:!1,value:e[c++]}},e:function(e){throw e},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,r=!0,o=!1;return{s:function(){a=a.call(e)},n:function(){var e=a.next();return r=e.done,e},e:function(e){o=!0,i=e},f:function(){try{r||null==a["return"]||a["return"]()}finally{if(o)throw i}}}}var o=a(8),l=a(7),d=a(2),j=a(3),b=a.n(j),x=a(0),u=a(6),h=a(4),p=(a(252),a(1)),O=function(e){var t=e.data,a=Object(d["useState"])(!0),c=Object(l["a"])(a,2),i=c[0],s=c[1],r=t.enable?{}:{color:"#ccc"},j=["\u5468\u65e5","\u5468\u4e00","\u5468\u4e8c","\u5468\u4e09","\u5468\u56db","\u5468\u4e94","\u5468\u516d"];return Object(p["jsx"])(d["Fragment"],{children:i?Object(p["jsxs"])(x["View"],{className:"bookItem",children:[Object(p["jsxs"])(x["View"],{className:"at-row cardNum",children:[Object(p["jsx"])(x["View"],{className:"at-col at-col-6",style:r,children:t.card_name}),Object(p["jsx"])(x["View"],{className:"at-col at-col-5",style:{textAlign:"right"},children:Object(p["jsxs"])(x["Text"],{style:r,children:["No.",t.user_card_code]})})]}),Object(p["jsxs"])(x["View"],{children:[Object(p["jsxs"])(x["View"],{className:"at-row",style:{padding:"0 14px",width:"90%",marginTop:"14px"},children:[Object(p["jsxs"])(x["View"],{className:"at-col at-col-3",style:{textAlign:"center"},children:[Object(p["jsx"])(x["View"],{className:"week",style:r,children:j[Object(h["a"])(1e3*t.book_time_unix).weekday()]}),Object(p["jsx"])(x["View"],{className:"day",style:r,children:t.book_time_show})]}),Object(p["jsxs"])(x["View"],{className:"at-col at-col-2",style:{margin:"0 15px"},children:[Object(p["jsx"])(x["View"],{className:"linetext",style:r,children:"\u6e38\u73a9"}),Object(p["jsx"])(x["Image"],{src:"".concat(u["a"].picUrl,"/bind/").concat(t.enable?"right":"right_cc",".png"),className:"line",mode:"widthFix"})]}),Object(p["jsxs"])(x["View"],{className:"at-col at-col-8 brand",children:[Object(p["jsx"])(x["View"],{style:r,children:t.brand_name}),Object(p["jsx"])(x["View"],{className:"address",children:Object(p["jsx"])(x["Text"],{style:r,children:u["a"].getSamll("".concat(t.province).concat(t.city).concat(t.address),10)})})]})]}),Object(p["jsxs"])(x["View"],{className:"at-row",style:{padding:"0 14px",width:"90%",marginBottom:"14px",marginTop:"6px",color:"#8e8c8c"},children:[Object(p["jsx"])(x["View"],{className:"at-col at-col-3",style:{textAlign:"center"},children:Object(p["jsxs"])(x["Text"],{style:r,children:["\u9884\u7ea6\u4eba:  ",t.real_name]})}),Object(p["jsx"])(x["View"],{className:"at-col at-col-3",style:{textAlign:"left"}}),Object(p["jsx"])(x["View"],{className:"at-col at-col-6",style:{textAlign:"left",paddingLeft:"5px"},children:Object(p["jsx"])(x["Text"],{style:r,children:t.add_time})})]}),t.enable?Object(p["jsxs"])(x["View"],{className:"at-row oper",children:[Object(p["jsx"])(x["View"],{className:"at-col at-col-4",style:{textAlign:"center",borderRight:"1px solid #e2e2e2"},onClick:function(){b.a.navigateTo({url:"/pages/qr/index?code=".concat(t.user_card_code)})},children:"\u626b\u7801\u5165\u56ed"}),Object(p["jsx"])(x["View"],{className:"at-col at-col-4",style:{textAlign:"center",borderRight:"1px solid #e2e2e2"},onClick:function(){b.a.openLocation({latitude:parseFloat(t.latitude),longitude:parseFloat(t.longitude),scale:15})},children:"\u4f4d\u7f6e\u5bfc\u822a"}),Object(p["jsx"])(x["View"],{className:"at-col at-col-4",style:{textAlign:"center"},onClick:function(){b.a.showModal({title:"\u8bf7\u786e\u8ba4",content:"\u53d6\u6d88\u6b64\u9884\u7ea6\uff1f",success:function(){var e=Object(o["a"])(Object(n["a"])().mark((function e(a){var c;return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!a.confirm){e.next=5;break}return e.next=3,u["a"].Api.removeBook({id:t.id});case 3:c=e.sent,!0===c.data&&(b.a.showToast({title:"\u9884\u7ea6\u5df2\u53d6\u6d88",icon:"none",duration:2e3}),s(!1));case 5:case"end":return e.stop()}}),e)})));function a(t){return e.apply(this,arguments)}return a}()})},children:"\u53d6\u6d88\u9884\u7ea6"})]}):Object(p["jsx"])(x["View"],{children:Object(p["jsx"])(x["View"],{style:{height:"10px"}})})]})]}):null})},m=O,w=a(10);a(51);function g(e){var t=u["a"].getQuery(),a=Object(d["useState"])([]),c=Object(l["a"])(a,2),s=c[0],h=c[1],O=Object(d["useState"])(),g=Object(l["a"])(O,2),f=g[0],y=g[1],V=Object(d["useState"])(1),v=Object(l["a"])(V,2),N=v[0],k=v[1],T=Object(d["useState"])(-1),_=Object(l["a"])(T,2),A=_[0],S=_[1],L=Object(d["useState"])([]),B=Object(l["a"])(L,2),C=B[0],z=(B[1],[{title:"\u666f\u533a\u9884\u7ea6"},{title:"\u9884\u7ea6\u8bb0\u5f55"}]);return Object(d["useEffect"])(Object(o["a"])(Object(n["a"])().mark((function e(){var a,c,o,l,d;return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return a=Boolean(t.data.code)?t.data.code:"ALL",b.a.showLoading({mask:!0,title:"\u8bfb\u53d6\u4e2d"}),e.next=4,u["a"].Api.myBookList({code:a,page:{current:N,pageSize:20}});case 4:if(c=e.sent,b.a.hideLoading(),200===c.code){o=r(c.data.data);try{for(o.s();!(l=o.n()).done;)d=l.value,!1===C.includes(d.book_time_tab)?(C.push(d.book_time_tab),d.tabTop=!0):d.tabTop=!1}catch(e){o.e(e)}finally{o.f()}h([].concat(Object(i["a"])(s),Object(i["a"])(c.data.data))),y(c.data.page),S(c.data.page.total)}case 7:case"end":return e.stop()}}),e)}))),[N]),Object(j["useReachBottom"])((function(){var e=f.current+1;e<=f.totalPage?k(e):b.a.showToast({title:"\u6682\u65e0\u66f4\u591a\u5185\u5bb9",icon:"none",duration:2e3})})),Object(p["jsxs"])(x["View"],{className:"index",children:[Object(p["jsx"])(w["s"],{current:1,tabList:z,onClick:function(e){0===e&&b.a.navigateTo({url:"/pages/sreach/book"})}}),0===A?Object(p["jsxs"])(x["View"],{style:{textAlign:"center",marginTop:"60px",color:"#333",fontSize:"16px"},children:[Object(p["jsx"])(x["View"],{children:Object(p["jsx"])(w["f"],{value:"calendar",size:"30",color:"#848181"})}),Object(p["jsx"])(x["View"],{style:{marginTop:"10px",color:"#848181"},children:"\u6682\u65e0\u9884\u7ea6\u8bb0\u5f55"})]}):Object(p["jsx"])(x["View"],{children:s.map((function(e){return e.tabTop?Object(p["jsxs"])(x["View"],{children:[Object(p["jsx"])(x["View"],{style:{backgroundColor:"#ffd65b",fontSize:"14px",color:"#af6c09",padding:"4px 7px",width:"80px",textAlign:"center",margin:"8px",borderRadius:"20px",marginTop:"13px"},children:e.book_time_tab}),Object(p["jsx"])(m,{data:e})]}):Object(p["jsx"])(m,{data:e})}))}),Object(p["jsx"])(x["View"],{style:{height:"30px"}})]})}var f=g,y={navigationBarTitleText:"\u9884\u7ea6\u8bb0\u5f55"};Page(Object(c["createPageConfig"])(f,"pages/my/book",{root:{cn:[]}},y||{}))}},[[401,0,1,2,3]]]);