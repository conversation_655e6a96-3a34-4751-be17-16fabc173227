(my["webpackJsonp"]=my["webpackJsonp"]||[]).push([[53],{413:function(e,t,a){"use strict";a.r(t);var c=a(9),n=a(15),i=a(14),s=a(5),r=a(8),o=a(7),b=a(2),j=a(0),u=a(20),O=a(3),l=a.n(O),d=a(22),f=a(37),p=a(35),g=a(28),w=a(6),h=(a(41),a(1)),x=function(e){var t=w["a"].getQuery(),a=Object(u["c"])((function(e){return e.login})),c=Object(b["useState"])(null),x=Object(o["a"])(c,2),k=x[0],m=x[1],v=Object(b["useState"])(!1),S=Object(o["a"])(v,2),y=S[0],_=S[1],B=Object(b["useState"])(1),L=Object(o["a"])(B,2),P=L[0],T=L[1],A=Object(b["useState"])({}),C=Object(o["a"])(A,2),E=C[0],J=C[1],V=Object(b["useState"])([]),z=Object(o["a"])(V,2),N=z[0],Q=z[1],R=Object(b["useState"])(t.data.v),q=Object(o["a"])(R,2),D=q[0],F=q[1],G=Object(b["useState"])(!1),H=Object(o["a"])(G,2),I=H[0],K=H[1];return Object(b["useEffect"])(Object(r["a"])(Object(s["a"])().mark((function e(){var t;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!a){e.next=5;break}return e.next=3,w["a"].Api.brandCity({line_id:w["a"].line_code});case 3:t=e.sent,200===t.code&&_(t.data);case 5:case"end":return e.stop()}}),e)}))),[a]),Object(b["useEffect"])(Object(r["a"])(Object(s["a"])().mark((function e(){var t,c;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(K(!1),l.a.showLoading({mask:!0,title:"\u8bf7\u7a0d\u7b49"}),!a||!y){e.next=11;break}return t=Object(i["a"])({line_id:w["a"].line_code,order:"km",point:!0,page:{current:P,pageSize:20}},E),Boolean(D)&&(t.keywords=D),e.next=7,w["a"].Api.brandList(t);case 7:c=e.sent,200===c.code&&(m(c.data.page),Q([].concat(Object(n["a"])(N),Object(n["a"])(c.data.list)))),K(!0),l.a.hideLoading();case 11:case"end":return e.stop()}}),e)}))),[a,P,E,D,y]),Object(O["useReachBottom"])((function(){var e=k.current+1;e<=k.totalPage?T(e):l.a.showToast({title:"\u6682\u65e0\u66f4\u591a\u5185\u5bb9",icon:"none",duration:2e3})})),Object(h["jsxs"])(j["View"],{children:[y?Object(h["jsx"])(p["a"],{config:y,callBack:function(e){Q([]),J(e),T(1)},setkey:function(e){Q([]),F(e),T(1)},init:D}):null,Object(h["jsx"])(j["View"],{children:N.length>0?N.map((function(e){return Object(h["jsx"])(g["a"],{className:"list",data:e,cname:"images_l"})})):Object(h["jsx"])(f["a"],{loaded:I})}),Object(h["jsx"])(d["a"],{now:2})]})},k=x,m={navigationBarTitleText:"\u7cbe\u9009\u666f\u533a"};Page(Object(c["createPageConfig"])(k,"pages/sreach/list",{root:{cn:[]}},m||{}))}},[[413,0,1,2,3]]]);