(my["webpackJsonp"]=my["webpackJsonp"]||[]).push([[39],{419:function(e,t,a){"use strict";a.r(t);var c=a(9),n=a(5),r=a(15),s=a(8),i=a(7),o=a(2),j=a(3),b=a.n(j),l=a(0),d=a(6),O=a(4),u=a(10),p=(a(51),a(1));function x(e){var t=Object(o["useState"])([]),a=Object(i["a"])(t,2),c=a[0],x=a[1],m=Object(o["useState"])(),g=Object(i["a"])(m,2),h=g[0],w=g[1],f=Object(o["useState"])(1),y=Object(i["a"])(f,2),_=y[0],v=y[1],S=Object(o["useState"])(-1),T=Object(i["a"])(S,2),V=T[0],k=T[1];return Object(o["useEffect"])(Object(s["a"])(Object(n["a"])().mark((function e(){var t;return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return b.a.showLoading({mask:!0,title:"\u8bfb\u53d6\u4e2d"}),e.next=3,d["a"].Api.myUseList({code:"ALL",page:{current:_,pageSize:20}});case 3:t=e.sent,b.a.hideLoading(),200===t.code&&(x([].concat(Object(r["a"])(c),Object(r["a"])(t.data.data))),w(t.data.page),k(t.data.page.total));case 6:case"end":return e.stop()}}),e)}))),[_]),Object(j["useReachBottom"])((function(){var e=h.current+1;e<=h.totalPage?v(e):b.a.showToast({title:"\u6682\u65e0\u66f4\u591a\u5185\u5bb9",icon:"none",duration:2e3})})),Object(p["jsxs"])(l["View"],{className:"index",children:[0===V?Object(p["jsxs"])(l["View"],{style:{textAlign:"center",marginTop:"60px",color:"#333",fontSize:"16px"},children:[Object(p["jsx"])(l["View"],{children:Object(p["jsx"])(u["f"],{value:"calendar",size:"30",color:"#848181"})}),Object(p["jsx"])(l["View"],{style:{marginTop:"10px",color:"#848181"},children:"\u6682\u65e0\u5165\u56ed\u8bb0\u5f55"})]}):Object(p["jsx"])(l["View"],{style:{marginTop:"20px"},children:c.map((function(e){return Object(p["jsxs"])(l["View"],{style:{marginBottom:"10px"},children:[" ",Object(p["jsx"])(u["b"],{note:"".concat(e.real_name," ").concat(e.card_name," No.").concat(e.user_card_code),extra:e.check_way_str,title:"".concat(Object(O["a"])(1e3*e.add_time_unix).format("YYYY\u5e74MM\u6708DD\u65e5 HH:MM:ss")),children:Object(p["jsx"])(l["Text"],{style:{fontSize:"30px"},children:e.brand_name})})]})}))}),Object(p["jsx"])(l["View"],{style:{height:"30px"}})]})}var m=x,g={navigationBarTitleText:"\u5165\u56ed\u8bb0\u5f55"};Page(Object(c["createPageConfig"])(m,"pages/my/use",{root:{cn:[]}},g||{}))}},[[419,0,1,2,3]]]);