(my["webpackJsonp"]=my["webpackJsonp"]||[]).push([[31],{420:function(e,a,c){"use strict";c.r(a);var t=c(9),n=(c(2),c(0)),s=c(65),i=c(1);function r(e){return Object(i["jsx"])(n["View"],{className:"index",children:Object(i["jsx"])(n["View"],{className:"cardList",children:Object(i["jsx"])(s["a"],{})})})}var o=r,j={navigationBarTitleText:"\u6211\u7684\u5361\u5305"};Page(Object(t["createPageConfig"])(o,"pages/my/card",{root:{cn:[]}},j||{}))}},[[420,0,1,2,3]]]);