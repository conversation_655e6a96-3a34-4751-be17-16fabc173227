(my["webpackJsonp"]=my["webpackJsonp"]||[]).push([[41],{422:function(e,t,n){"use strict";n.r(t);var c=n(9),a=n(5),i=n(8),r=n(7),s=n(2),o=n(0),l=n(10),j=n(3),u=n.n(j),d=n(6),b=(n(253),n(1)),p=function(){var e=Object(s["useState"])([]),t=Object(r["a"])(e,2),n=t[0],c=t[1];return Object(s["useEffect"])(Object(i["a"])(Object(a["a"])().mark((function e(){var t;return Object(a["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return u.a.showLoading({mask:!0,title:"\u8bfb\u53d6\u4e2d"}),e.next=3,d["a"].Api.noteList();case 3:t=e.sent,200===t.code&&c(0===t.data.length?null:t.data),u.a.hideLoading();case 6:case"end":return e.stop()}}),e)}))),[]),Object(b["jsx"])(o["View"],{children:null===n?Object(b["jsx"])(o["View"],{className:"index",children:Object(b["jsxs"])(o["View"],{style:{textAlign:"center",marginTop:"60px",color:"#333",fontSize:"16px"},children:[Object(b["jsx"])(o["View"],{children:Object(b["jsx"])(l["f"],{value:"calendar",size:"30",color:"#848181"})}),Object(b["jsx"])(o["View"],{style:{marginTop:"10px",color:"#848181"},children:"\u6682\u65e0\u901a\u77e5"})]})}):Object(b["jsx"])(l["i"],{children:null!==n&&n.map((function(e){return Object(b["jsx"])(l["j"],{title:e.title,note:e.add_time,arrow:"right",onClick:function(){return u.a.navigateTo({url:"/pages/news/info?v=".concat(e.code)})}})}))})})},x=p,O={navigationBarTitleText:"\u6700\u65b0\u901a\u77e5"};Page(Object(c["createPageConfig"])(x,"pages/news/index",{root:{cn:[]}},O||{}))}},[[422,0,1,2,3]]]);