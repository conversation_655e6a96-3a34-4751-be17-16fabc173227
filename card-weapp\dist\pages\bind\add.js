(my["webpackJsonp"]=my["webpackJsonp"]||[]).push([[7],{393:function(e,t,n){},403:function(e,t,n){"use strict";n.r(t);var c=n(9),a=n(5),r=n(8),i=n(7),s=n(2),l=n(3),o=n.n(l),d=n(0),u=n(10),j=(n(393),n(1)),b=function(e){var t=e.open,n=e.title,c=e.ok,a=e.close;return Object(j["jsxs"])(u["k"],{isOpened:t,children:[Object(j["jsx"])(u["n"],{children:n}),Object(j["jsx"])(u["m"],{children:e.children}),Object(j["jsxs"])(u["l"],{children:[Boolean(a)?Object(j["jsx"])(d["But<PERSON>"],{onClick:function(){return a.fun()},children:a.name}):null,Object(j["jsx"])(d["Button"],{onClick:function(){return c.fun()},children:c.name})]})]})},h=b,p=n(32),x=n(6);n(74);function O(e){var t=x["a"].getQuery(),n=Boolean(t.data.rel)?parseInt(t.data.rel,10):0,c=Boolean(t.data.key)?t.data.key:null;console.warn("good_key -------------",c,n);var l=0===n?"":x["a"].relation[n].label,u=Object(s["useState"])(null),b=Object(i["a"])(u,2),O=(b[0],b[1],Object(s["useState"])(!1)),_=Object(i["a"])(O,2),m=_[0],k=_[1],f=Object(s["useState"])(!1),g=Object(i["a"])(f,2),y=g[0],w=g[1],v=Object(s["useState"])(!1),V=Object(i["a"])(v,2),B=V[0],S=V[1],T=Object(s["useState"])(0),C=Object(i["a"])(T,2),N=C[0],I=(C[1],Object(s["useState"])("")),z=Object(i["a"])(I,2),P=(z[0],z[1],Object(s["useState"])(!1)),D=Object(i["a"])(P,2),J=(D[0],D[1],Object(s["useState"])({user_img:"",user_img_check:"\u8bf7\u4e0a\u4f20\u6301\u5361\u4eba\u7167\u7247",real_name:null,real_name_check:"\u8bf7\u586b\u5199\u59d3\u540d",id_card_no:"fake",id_card_no_check:"\u8bf7\u586b\u5199\u8bc1\u4ef6\u53f7\u7801",good_key:c,good_key_check:"\u8bf7\u586b\u5199\u5361\u7247\u6fc0\u6d3b\u7801"})),A=Object(i["a"])(J,2),E=A[0],F=A[1],L=(Object(s["useRef"])(null),function(e,t,n,c){var a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:"null";if(e.detail.value.length>0){var r=x["a"].checkData(e.detail.value,t,n,a);!0!==r.check?(o.a.showToast({title:r.check,icon:"none",mask:!0,duration:2e3}),E["".concat(c,"_check")]=r.check,E[c]=null):E[c]=r.data,F(E)}}),Q=function(){var e=[];Object.keys(E).forEach((function(t){t.includes("_check")||null===E[t]&&e.push(E["".concat(t,"_check")])})),e.length>0?k(!0):S(!0)},R=function(){var e=Object(r["a"])(Object(a["a"])().mark((function e(){var t,c,r;return Object(a["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(S(!1),!y){e.next=3;break}return e.abrupt("return",!1);case 3:return w(!0),o.a.showLoading({mask:!0,title:"\u63d0\u4ea4\u4e2d"}),t=0===N?x["a"].checkData(E.id_card_no,17,"\u8eab\u4efd\u8bc1","card"):{year:1984,sex:1},c={good_key:E.good_key.toUpperCase(),info:{id_card_no:E.id_card_no,id_card_type:N,real_name:E.real_name,user_tel:E.user_tel,user_img:E.user_img,user_year:t.year,user_gender:t.sex,user_rel:n}},e.next=9,x["a"].Api.bindCard(c);case 9:r=e.sent,200===r.code?o.a.redirectTo({url:"/pages/tips/index?type=100"}):w(!1);case 11:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}();return Object(j["jsxs"])(d["View"],{children:[Object(j["jsx"])(p["a"],{}),Object(j["jsxs"])(d["View"],{className:"addPage",children:[Object(j["jsxs"])(d["View"],{class:"item",style:{marginTop:"18px"},children:[Object(j["jsxs"])(d["Text"],{style:{width:"80px"},children:[l,"\u59d3\u540d"]}),Object(j["jsx"])(d["Input"],{type:"text",placeholder:"\u8bf7\u8f93\u5165\u59d3\u540d",className:"input",maxlength:10,onBlur:function(e){L(e,2,"\u59d3\u540d","real_name")}})]}),Object(j["jsxs"])(d["View"],{class:"item",style:{marginTop:"18px"},children:[Object(j["jsx"])(d["Text"],{style:{width:"80px"},children:"\u5361\u6fc0\u6d3b\u7801"}),Object(j["jsx"])(d["Input"],{type:"text",placeholder:"\u8bf7\u8f93\u5165\u5361\u6fc0\u6d3b\u7801",className:"input",maxlength:15,value:c,onBlur:function(e){L(e,8,"\u5361\u6fc0\u6d3b\u7801","good_key")}})]}),Object(j["jsxs"])(d["View"],{className:"card",children:[Object(j["jsx"])(d["Image"],{src:"".concat(x["a"].ip,"/card.jpg"),mode:"widthFix",style:{width:"70%",marginBottom:"12px"}}),Object(j["jsx"])(d["View"],{style:{color:"#3b883e",fontSize:"25rpx"},children:"\u5b9e\u4f53\u5361\u53f3\u4e0b\u89d2\u4e3a\u6fc0\u6d3b\u7801\uff0c\u7535\u5b50\u7968\u8bf7\u5728\u5c0f\u7a0b\u5e8f\u6211\u7684\u8ba2\u5355\u4e2d\u67e5\u770b"})]})]}),Object(j["jsx"])(d["View"],{className:"jbtn",onClick:function(){Q()},children:"\u63d0 \u4ea4"}),Object(j["jsx"])(h,{open:m,title:"\u63d0\u793a",ok:{name:"\u91cd\u65b0\u586b\u5199",fun:function(){k(!1)}},children:Object(j["jsx"])(d["View"],{style:{fontSize:"16px"},children:Object.keys(E).map((function(e){if(null===E[e])return Object(j["jsxs"])(d["View"],{style:{padding:"4px"},children:[Object(j["jsx"])(d["Text"],{style:{color:"red"},children:"X"})," ",E["".concat(e,"_check")]]})}))})}),Object(j["jsx"])(h,{open:B,title:"\u786e\u8ba4\u4fe1\u606f",ok:{name:"\u7acb\u5373\u63d0\u4ea4",fun:function(){R()}},close:{name:"\u91cd\u65b0\u586b\u5199",fun:function(){S(!1)}},children:Object(j["jsxs"])(d["View"],{style:{fontSize:"14px",padding:"0 5px"},children:[Object(j["jsxs"])(d["View"],{style:{paddingBottom:"5px"},children:["\u771f\u5b9e\u59d3\u540d: ",E.real_name]}),Object(j["jsxs"])(d["View"],{style:{paddingBottom:"5px"},children:["\u5361\u6fc0\u6d3b\u7801: ",E.good_key]})]})})]})}var _=O,m={navigationBarTitleText:"\u7528\u5361\u4fe1\u606f\u63d0\u4ea4"};Page(Object(c["createPageConfig"])(_,"pages/bind/add",{root:{cn:[]}},m||{}))}},[[403,0,1,2,3]]]);