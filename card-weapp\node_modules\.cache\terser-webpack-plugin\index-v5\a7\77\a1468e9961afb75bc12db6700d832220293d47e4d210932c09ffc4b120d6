
5093b4589757bbb83ad76a3fe99198026a271df5	{"key":"{\"terser\":\"4.8.1\",\"terser-webpack-plugin\":\"3.1.0\",\"terser-webpack-plugin-options\":{\"test\":new RegExp(\"\\\\.m?js(\\\\?.*)?$\", \"i\"),\"warningsFilter\":() => true,\"extractComments\":true,\"sourceMap\":false,\"cache\":true,\"cacheKeys\":defaultCacheKeys => defaultCacheKeys,\"parallel\":true,\"include\":undefined,\"exclude\":undefined,\"minify\":undefined,\"terserOptions\":{\"parse\":{\"ecma\":8},\"compress\":{\"ecma\":5,\"warnings\":false,\"arrows\":false,\"collapse_vars\":false,\"comparisons\":false,\"computed_props\":false,\"hoist_funs\":false,\"hoist_props\":false,\"hoist_vars\":false,\"inline\":false,\"loops\":false,\"negate_iife\":false,\"properties\":false,\"reduce_funcs\":false,\"reduce_vars\":false,\"switches\":false,\"toplevel\":false,\"typeofs\":false,\"booleans\":true,\"if_return\":true,\"sequences\":true,\"unused\":true,\"conditionals\":true,\"dead_code\":true,\"evaluate\":true},\"output\":{\"ecma\":5,\"comments\":false,\"ascii_only\":true}}},\"nodeVersion\":\"v16.20.0\",\"name\":\"pages\\u002Fpay\\u002Findex.js\",\"contentHash\":\"8cc45d980fcad6e1e307\"}","integrity":"sha512-yi2mkzxssvDa8mX1mVewotG0SO/0EvDwWdjpeZ79uscPV+qG/vYV62pZM9ZOyLDiSgyy6MHr04oS/UC63klG6g==","time":1754920734569,"size":1743}