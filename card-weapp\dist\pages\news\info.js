(my["webpackJsonp"]=my["webpackJsonp"]||[]).push([[43],{423:function(e,t,a){"use strict";a.r(t);var c=a(9),n=a(5),i=a(8),s=a(7),l=a(2),r=a(0),o=a(3),j=a.n(o),d=a(6),b=(a(253),a(1)),h=function(){var e=d["a"].getQuery(),t=Object(l["useState"])(null),a=Object(s["a"])(t,2),c=a[0],o=a[1];return Object(l["useEffect"])(Object(i["a"])(Object(n["a"])().mark((function t(){var a;return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return j.a.showLoading({mask:!0,title:"\u8bfb\u53d6\u4e2d"}),t.next=3,d["a"].Api.noteInfo({code:e.data.v});case 3:a=t.sent,200===a.code&&(o(a.data),j.a.setNavigationBarTitle({title:a.data.title})),j.a.hideLoading();case 6:case"end":return t.stop()}}),t)}))),[]),Object(b["jsx"])(r["View"],{children:null!==c?Object(b["jsxs"])(r["View"],{className:"at-article",children:[Object(b["jsx"])(r["View"],{className:"at-article__h1",style:{fontSize:"22px",lineHeight:"28px"},children:c.title}),Object(b["jsx"])(r["View"],{className:"at-article__info",style:{color:"#968e8e",marginTop:"8px"},children:c.add_time}),Object(b["jsx"])(r["View"],{className:"at-article__content",children:Object(b["jsx"])(r["View"],{className:"brand_html",children:Object(b["jsx"])(r["RichText"],{className:"htmlformat",style:{lineHeight:"30px"},nodes:d["a"].removeCss(c.content)})})})]}):null})},m=h,u={navigationBarTitleText:"loading"};Page(Object(c["createPageConfig"])(m,"pages/news/info",{root:{cn:[]}},u||{}))}},[[423,0,1,2,3]]]);