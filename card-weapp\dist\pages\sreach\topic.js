(my["webpackJsonp"]=my["webpackJsonp"]||[]).push([[57],{415:function(t,e,a){"use strict";a.r(e);var c=a(9),n=a(5),i=a(8),o=a(7),s=a(2),r=a(0),j=a(20),u=a(3),b=a.n(u),l=a(22),O=a(37),d=(a(35),a(28)),p=a(6),g=(a(41),a(1)),m=function(t){var e=p["a"].getQuery(),a=Object(j["c"])((function(t){return t.login})),c=Object(s["useState"])(null),m=Object(o["a"])(c,2),h=m[0],w=(m[1],Object(s["useState"])(1)),x=Object(o["a"])(w,2),f=x[0],v=x[1],k=Object(s["useState"])([]),S=Object(o["a"])(k,2),y=S[0],B=S[1],T=Object(s["useState"])(!1),P=Object(o["a"])(T,2),V=P[0],I=P[1],J=Object(s["useState"])(null),L=Object(o["a"])(J,2),N=L[0],A=L[1];return Object(s["useEffect"])(Object(i["a"])(Object(n["a"])().mark((function t(){var c,i;return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(I(!1),b.a.showLoading({mask:!0,title:"\u8bf7\u7a0d\u7b49"}),!a||!e.data.v){t.next=10;break}return c={point:!0,code:e.data.v},t.next=6,p["a"].Api.topicInfo(c);case 6:i=t.sent,200===i.code&&(B(i.data.list),A(i.data.topic),b.a.setNavigationBarTitle({title:"".concat(i.data.topic.name)})),I(!0),b.a.hideLoading();case 10:case"end":return t.stop()}}),t)}))),[a,f]),Object(u["useReachBottom"])((function(){var t=h.current+1;t<=h.totalPage?v(t):b.a.showToast({title:"\u6682\u65e0\u66f4\u591a\u5185\u5bb9",icon:"none",duration:2e3})})),Object(g["jsxs"])(r["View"],{children:[null!==N?Object(g["jsx"])(r["View"],{children:Object(g["jsx"])(r["Image"],{src:N.image,mode:"widthFix",style:{width:"100%",marginBottom:"20px"}})}):null,Object(g["jsx"])(r["View"],{children:y.length>0?y.map((function(t){return Object(g["jsx"])(d["a"],{className:"list",data:t,cname:"images_l"})})):Object(g["jsx"])(O["a"],{loaded:V})}),Object(g["jsx"])(l["a"],{now:3})]})},h=m,w={navigationBarTitleText:"\u666f\u533a\u4e13\u9898"};Page(Object(c["createPageConfig"])(h,"pages/sreach/topic",{root:{cn:[]}},w||{}))}},[[415,0,1,2,3]]]);