(my["webpackJsonp"]=my["webpackJsonp"]||[]).push([[35],{421:function(e,t,a){"use strict";a.r(t);var c=a(9),n=a(18),i=a(5),s=a(15),r=a(8),o=a(7),j=a(2),b=a(3),l=a.n(b),O=a(0),p=a(6),u=a(28),d=a(10),g=(a(51),a(1));function x(e){var t=Object(j["useState"])([]),a=Object(o["a"])(t,2),c=a[0],x=a[1],m=Object(j["useState"])(),h=Object(o["a"])(m,2),w=h[0],f=h[1],v=Object(j["useState"])(1),y=Object(o["a"])(v,2),S=y[0],T=y[1],V=Object(j["useState"])(-1),k=Object(o["a"])(V,2),z=k[0],P=k[1];return Object(j["useEffect"])(Object(r["a"])(Object(i["a"])().mark((function e(){var t;return Object(i["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return l.a.showLoading({mask:!0,title:"\u8bfb\u53d6\u4e2d"}),e.next=3,p["a"].Api.myCollect({point:!0,page:{current:S,pageSize:20}});case 3:t=e.sent,l.a.hideLoading(),200===t.code&&(x([].concat(Object(s["a"])(c),Object(s["a"])(t.data.data))),f(t.data.page),P(t.data.page.total));case 6:case"end":return e.stop()}}),e)}))),[S]),Object(b["useReachBottom"])((function(){var e=w.current+1;e<=w.totalPage?T(e):l.a.showToast({title:"\u6682\u65e0\u66f4\u591a\u5185\u5bb9",icon:"none",duration:2e3})})),Object(g["jsxs"])(O["View"],{className:"index",children:[0===z?Object(g["jsxs"])(O["View"],{style:{textAlign:"center",marginTop:"60px",color:"#333",fontSize:"16px"},children:[Object(g["jsx"])(O["View"],{children:Object(g["jsx"])(d["f"],{value:"calendar",size:"30",color:"#848181"})}),Object(g["jsx"])(O["View"],{style:{marginTop:"10px",color:"#848181"},children:"\u6682\u65e0\u6536\u85cf\u8bb0\u5f55"})]}):Object(g["jsx"])(O["View"],{style:{marginTop:"20px"},children:c.map((function(e){return Object(g["jsx"])(u["a"],Object(n["a"])(Object(n["a"])({data:e,className:"list"},"data",e),"cname","images_l"))}))}),Object(g["jsx"])(O["View"],{style:{height:"30px"}})]})}var m=x,h={navigationBarTitleText:"\u6211\u7684\u6536\u85cf"};Page(Object(c["createPageConfig"])(m,"pages/my/love",{root:{cn:[]}},h||{}))}},[[421,0,1,2,3]]]);