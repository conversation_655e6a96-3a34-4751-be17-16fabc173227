(my["webpackJsonp"]=my["webpackJsonp"]||[]).push([[55],{416:function(e,t,a){"use strict";a.r(t);var n=a(9),c=a(15),r=a(14),i=a(5),o=a(8),s=a(7),u=a(2),b=a(0),j=a(20),l=a(3),O=a.n(l),d=a(22),g=a(37),f=a(35),p=a(64),m=a(6),w=(a(41),a(1)),h=function(e){var t=m["a"].getQuery(),a=Object(j["c"])((function(e){return e.login})),n=m["a"].getDataSafe("mypoint"),h=Object(u["useState"])(null),x=Object(s["a"])(h,2),v=x[0],y=x[1],k=Object(u["useState"])(!1),S=Object(s["a"])(k,2),B=S[0],V=S[1],_=Object(u["useState"])(1),P=Object(s["a"])(_,2),L=P[0],T=P[1],A=Object(u["useState"])({order:"km",ordertype:"asc",range:{latitude:n.api.latitude,longitude:n.api.longitude,val:5e3}}),D=Object(s["a"])(A,2),E=D[0],J=D[1],z=Object(u["useState"])([]),C=Object(s["a"])(z,2),I=C[0],N=C[1],Q=Object(u["useState"])(t.data.v),R=Object(s["a"])(Q,2),q=R[0],F=R[1],G=Object(u["useState"])(!1),H=Object(s["a"])(G,2),K=H[0],M=H[1];Object(u["useEffect"])(Object(o["a"])(Object(i["a"])().mark((function e(){var t;return Object(i["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!a){e.next=5;break}return e.next=3,m["a"].Api.brandBuy({latitude:n.api.latitude,longitude:n.api.longitude});case 3:t=e.sent,200===t.code&&V(t.data);case 5:case"end":return e.stop()}}),e)}))),[a]),Object(u["useEffect"])(Object(o["a"])(Object(i["a"])().mark((function e(){var t,n,o;return Object(i["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(M(!1),console.log(E),t=m["a"].getDataSafe("userInfo"),O.a.showLoading({mask:!0,title:"\u8bf7\u7a0d\u7b49"}),!a||!B){e.next=13;break}return n=Object(r["a"])({admin:"buyer",showlist:1,from:"fe",page:L-1,enter_user_id:0,agent_id:1,pageSize:20,currentPage:L,buyer:t.mem_id},E),Boolean(q)&&(n.keywords=q),e.next=9,m["a"].Api.brandBuyList(n);case 9:o=e.sent,0===o.code&&(y(o.pagination),N([].concat(Object(c["a"])(I),Object(c["a"])(o.list)))),M(!0),O.a.hideLoading();case 13:case"end":return e.stop()}}),e)}))),[a,L,E,q,B]),Object(l["useReachBottom"])((function(){var e=v.current+1;e<=v.totalPage?T(e):O.a.showToast({title:"\u6682\u65e0\u66f4\u591a\u5185\u5bb9",icon:"none",duration:2e3})}));var U=function(e){var t={};Boolean(e.orderVal)&&(t.orderVal=e.orderVal[0]),Boolean(e.range)&&(t.range=e.range[0]),Boolean(e.order)&&(t=Object(r["a"])(Object(r["a"])({},t),e.order)),J(Object(r["a"])(Object(r["a"])({},E),t))};return Object(w["jsxs"])(b["View"],{children:[B?Object(w["jsx"])(f["a"],{config:B,callBack:function(e){N([]),U(e),T(1)},setkey:function(e){N([]),F(e),T(1)},init:q}):null,Object(w["jsx"])(b["View"],{children:I.length>0?I.map((function(e){return Object(w["jsx"])(p["a"],{className:"list",data:e,cname:"images_l"})})):Object(w["jsx"])(g["a"],{loaded:K})}),Object(w["jsx"])(d["a"],{now:10})]})},x=h,v={navigationBarTitleText:"\u5e74\u7968\u798f\u5229"};Page(Object(n["createPageConfig"])(x,"pages/sreach/listbuy",{root:{cn:[]}},v||{}))}},[[416,0,1,2,3]]]);