(my["webpackJsonp"]=my["webpackJsonp"]||[]).push([[17],{389:function(e,t,a){},399:function(e,t,a){"use strict";a.r(t);var c=a(9),n=a(5),i=a(8),s=a(7),o=a(2),l=a.n(o),r=a(3),d=a.n(r),j=a(20),b=a(0),u=a(10),m=(a(389),a(1)),h=function(e){var t=[31,28,31,30,31,30,31,31,30,31,30,31],a=["\u65e5","\u4e00","\u4e8c","\u4e09","\u56db","\u4e94","\u516d"],c=e.start,n=e.end,i=e.list,r=e.book,j=[1,2,3,4,5,6],h=Object(o["useState"])(0),x=Object(s["a"])(h,2),O=x[0],w=x[1],p=Object(o["useState"])(0),f=Object(s["a"])(p,2),g=f[0],_=f[1],N=Object(o["useState"])(0),V=Object(s["a"])(N,2),v=(V[0],V[1]),k=Object(o["useState"])(0),y=Object(s["a"])(k,2),F=(y[0],y[1]),C=new Date,T=Object(o["useState"])(!1),S=Object(s["a"])(T,2),D=S[0],I=S[1];Object(o["useEffect"])((function(){if(Boolean(c)&&Boolean(n)){var e=new Date(c),t=new Date(n);w(e.getFullYear()),_(e.getMonth()),v(t.getFullYear()),F(t.getMonth())}else w(C.getFullYear()),_(C.getMonth()),v(C.getFullYear()),F(C.getMonth()+3)}),[c,n]);var Y=function(e){return e.getMonth()},M=function(e){return e.getFullYear()},A=function(e,a){var c=a+C.getFullYear();return(c%100!=0&&c%4==0||c%400==0)&&(t=[31,29,31,30,31,30,31,31,30,31,30,31]),t[e]},U=function(e,t){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,c=new Date;return c.setFullYear(e),c.setMonth(t,a),c},B=function(e,t){var a=U(e,t);return a.getDay()},E=function(e){var t=parseInt(e,10);return t<10?"0".concat(t):"".concat(t)},L=function(t,a,s,o){var j=7*t+a-s+1,u=new Date;if(j<=0||j>o)return Object(m["jsx"])(b["View"],{className:"day-c",children:"\xa0"},a);var h="".concat(u.getFullYear(),"-").concat(E(u.getMonth()+1),"-").concat(E(u.getDate())),x="".concat(O,"-").concat(E(g+1),"-").concat(E(j)),w=function(){var e=function(){if(0===i.length)return!0;var e=[];return i.forEach((function(t){t.val===x&&!0===t.enable&&e.push(t)})),Boolean(e.length>0)}();if(new Date(x)>=new Date(c)&&new Date(x)<=new Date(n)&&e)return!0}();return Object(m["jsx"])(l.a.Fragment,{children:w?Object(m["jsxs"])(b["View"],{className:"day-c",onClick:function(){r?e.onChange(x):d.a.showToast({title:"\u65e0\u9700\u9884\u7ea6\u76f4\u63a5\u5165\u56ed",icon:"none",duration:1e3})},children:[Object(m["jsx"])(b["View"],{className:"day",style:h===x?{width:"25px",height:"25px",borderRadius:"25px",backgroundColor:"#f12828",color:"#fff",margin:"0 auto",fontSize:"14px",lineHeight:"25px"}:{},children:j}),Object(m["jsx"])(b["View"],{className:"desc",style:{color:r?"#26942a":"#2196f3"},children:r?"\u53ef\u7ea6":"\u5f00\u653e"})]},a):Object(m["jsxs"])(b["View"],{className:"day-c",children:[Object(m["jsx"])(b["View"],{className:"day",style:h===x?{width:"25px",height:"25px",borderRadius:"25px",backgroundColor:"#e2d7d7",color:"#fff",margin:"0 auto",fontSize:"14px",lineHeight:"25px"}:{color:"#c7c3c3"},children:j}),Object(m["jsx"])(b["View"],{className:"desc",style:{color:"#fff"},children:"\u8fc7\u671f"})]},a)})},P=function(e){var t=Y(e),a=M(e);w(a),_(t),I(!1)},z=function(e){if(!D){I(!0);var t=g+e,a=function(){return 1===e?11===g?0:t:0===g?11:t}(),i=function(){return 11===g&&1===e?O+1:0===g&&-1===e?O-1:O}(),s=new Date("".concat(i),"".concat(a+1)),o=new Date("".concat(new Date(c).getFullYear()),"".concat(new Date(c).getMonth()+1)),l=new Date("".concat(new Date(n).getFullYear()),"".concat(new Date(n).getMonth()+1));if(s>=o&&s<=l){var r=U(i,a);P(r)}else I(!1)}},q=function(e){var t=e+1;return t<10?"0".concat(t):"".concat(t)},R=B(O,g),H=0;return Object(m["jsx"])(l.a.Fragment,{children:Object(m["jsxs"])(b["View"],{className:"loins-calendar",onTouchEnd:function(e){H>e.changedTouches[0]["clientX"]+30&&z(1),H<e.changedTouches[0]["clientX"]-30&&z(-1)},onTouchStart:function(e){H=e.changedTouches[0]["clientX"]},children:[Object(m["jsxs"])(b["View"],{className:"loins-calendar-tabbar",children:[Object(m["jsx"])(b["View"],{children:Object(m["jsx"])(u["f"],{value:"chevron-left",size:"25",color:"#297AF8",onClick:function(){z(-1)}})}),Object(m["jsxs"])(b["View"],{className:"loins-calendar-title",children:[O," \u5e74 ",q(g),"\u6708"]}),Object(m["jsx"])(b["View"],{children:Object(m["jsx"])(u["f"],{value:"chevron-right",size:"25",color:"#297AF8",onClick:function(){z(1)}})})]}),a.map((function(e,t){return Object(m["jsx"])(b["View"],{className:"title-c",children:e},t)})),j.map((function(e,t){return Object(m["jsx"])(b["View"],{className:"day-content",children:a.map((function(e,a){return L(t,a,R,A(g,O))}))},t)}))]})})},x=h,O=a(47),w=a(31),p=a.n(w),f=a(6),g=function(e){var t=e.love;return Object(m["jsxs"])(b["View"],{className:"topNav",style:{position:"absolute"},children:[Object(m["jsx"])(b["View"],{style:{height:"".concat(f["a"].reTopH(6),"rpx")}}),Object(m["jsxs"])(b["View"],{className:"at-row",children:[Object(m["jsx"])(b["View"],{className:"at-col at-col-1 left",onClick:function(){d.a.navigateBack({delta:1,fail:function(){d.a.navigateTo({url:"/pages/index/index"})}})},children:Object(m["jsx"])(b["Image"],{src:"".concat(f["a"].picUrl,"/memu/back.png"),mode:"widthFix",className:"topIcon_b"})}),Object(m["jsx"])(b["View"],{className:"at-col at-col-1",onClick:function(){e.oper()},children:t?Object(m["jsx"])(b["Image"],{src:"".concat(f["a"].picUrl,"/memu/love_s.png"),mode:"widthFix",className:"topIcon_b"}):Object(m["jsx"])(b["Image"],{src:"".concat(f["a"].picUrl,"/memu/love.png"),mode:"widthFix",className:"topIcon_b"})}),Object(m["jsx"])(b["View"],{className:"at-col at-col-9"})]})]})},_=g,N=a(4);a(251);function V(e){var t=f["a"].getQuery(),a=Object(j["c"])((function(e){return e.login})),c=Object(o["useState"])(!0),l=Object(s["a"])(c,2),h=l[0],w=l[1],g=Object(o["useState"])(null),V=Object(s["a"])(g,2),v=V[0],k=V[1],y=Object(o["useState"])(!0),F=Object(s["a"])(y,2),C=F[0],T=F[1],S=Object(o["useState"])(!1),D=Object(s["a"])(S,2),I=D[0],Y=D[1],M=Object(o["useState"])(!1),A=Object(s["a"])(M,2),U=A[0],B=A[1],E=Object(o["useState"])(!1),L=Object(s["a"])(E,2),P=L[0],z=L[1],q=Object(o["useState"])({num:0,good_id:"--",list:[],name:"--"}),R=Object(s["a"])(q,2),H=R[0],X=R[1],J=Object(o["useState"])({start:"2022-01-01",end:"2022-02-01",list:[]}),W=Object(s["a"])(J,2),G=W[0],Q=W[1],K=Object(o["useState"])(""),Z=Object(s["a"])(K,2),$=Z[0],ee=Z[1],te=t.data.v;Object(o["useEffect"])(Object(i["a"])(Object(n["a"])().mark((function e(){var t,c,i;return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!a||!Boolean(te)){e.next=14;break}return e.next=3,f["a"].Api.brandInfo({code:te});case 3:if(t=e.sent,200!==t.code){e.next=14;break}return e.next=7,f["a"].getCardList({list:t.data.brand.templet_list_enable});case 7:c=e.sent,c.enable_card_num>0&&(i=c.card_list.filter((function(e){return!0===e.enable})),X({num:i.length,good_id:i[0].good_id,list:i})),console.warn("--------- data.data --------",t.data),k(t.data),Y(t.data.brand.collect),w(!1),ae(t.data.brand.area);case 14:case"end":return e.stop()}}),e)}))),[a]);var ae=function(e){var t="";t=e.length>2?"https://wis.qq.com/weather/common?source=pc&weather_type=observe|air&province=".concat(e[0],"&city=").concat(e[1],"&county=").concat(e[2]):"https://wis.qq.com/weather/common?source=pc&weather_type=observe|air&province=".concat(e[0],"&city=").concat(e[0],"&county=").concat(e[1]),d.a.request({url:t,method:"get"}).then((function(e){var t=e.data.data,a="".concat(t.observe.weather," / ").concat(t.observe.degree,"\xb0 / ").concat(t.observe.wind_direction_name," ").concat(t.observe.wind_power," \u7ea7");ee(a)}))};Object(r["useShareAppMessage"])((function(){return{title:v.brand.brand_name,path:"/pages/brand/index?v=".concat(te),imageUrl:"".concat(v.brand.image[0].thumbUrl,"?x-oss-process=image/resize,m_fill,h_400,w_400")}}));var ce=function(){var e=Object(i["a"])(Object(n["a"])().mark((function e(){var t;return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(!(H.num>0)){e.next=9;break}return d.a.showLoading({mask:!0,title:"\u8bfb\u53d6\u4e2d"}),e.next=4,f["a"].Api.brandBookList({brand_code:te,good_id:H.good_id,today:v.book.today_time});case 4:t=e.sent,d.a.hideLoading(),200===t.code?t.data.list.length>0&&(Q({start:t.data.list[0].val,end:t.data.list[t.data.list.length-1].val,list:t.data.list}),B(!0)):d.a.showToast({title:t.message,icon:"none",duration:2e3}),e.next=10;break;case 9:d.a.navigateTo({url:"/pages/bind/index"});case 10:case"end":return e.stop()}}),e)})));return function(){return e.apply(this,arguments)}}(),ne=function(e){return e.includes("|")?e.split("|"):[e]},ie=function(e){var t=[];return e.forEach((function(e){t.push(e.replace(":00",""))})),t.join("~")};return Object(m["jsx"])(b["View"],{children:null===v&&Boolean(te)?Object(m["jsx"])(p.a,{title:!0,row:30,loading:h}):Object(m["jsxs"])(b["View"],{children:[Object(m["jsx"])(_,{love:I,oper:Object(i["a"])(Object(n["a"])().mark((function e(){return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,f["a"].Api.brandCollect({brand_code:te,action:!1===I?"ADD":"DEL"});case 2:Y(!I);case 3:case"end":return e.stop()}}),e)})))}),Object(m["jsx"])(b["View"],{className:"brand_top_pic",children:Object(m["jsxs"])(b["Swiper"],{className:"brand_swiper",indicatorColor:"#999",indicatorActiveColor:"#fff",circular:!0,indicatorDots:!0,autoplay:C,children:[null!==v.brand.video&&v.brand.video.map((function(e){return Object(m["jsx"])(b["SwiperItem"],{children:Object(m["jsx"])(b["Video"],{src:e.url,controls:!0,autoplay:!1,showProgress:!0,poster:"",initialTime:"0",enablePlayGesture:!0,autoPauseIfOpenNative:!0,muted:!1,className:"images",objectFit:"contain",onPlay:function(){T(!1)},onEnded:function(){T(!0)}})})})),v.brand.static_list.map((function(e){return Object(m["jsx"])(b["SwiperItem"],{children:Object(m["jsx"])(b["Image"],{src:e.url,mode:"heightfix",className:"images"})})}))]})}),"none"!==v.brand.brand_note&&v.brand.brand_note.length>4?Object(m["jsx"])(u["o"],{children:v.brand.brand_note}):null,Object(m["jsxs"])(b["View"],{className:"brand_content",children:[Object(m["jsxs"])(b["View"],{className:"at-row",style:{marginBottom:"20px"},children:[Object(m["jsxs"])(b["View"],{className:"at-col at-col-10",children:[Object(m["jsx"])(b["View"],{className:"title",children:v.brand.brand_name}),Object(m["jsx"])(b["View"],{className:"memo",children:v.brand.intro})]}),Object(m["jsx"])(b["View"],{className:"at-col at-col-2",children:v.limit.is_open_config.is_open?Object(m["jsx"])(b["View"],{className:"open",children:Object(m["jsx"])(b["View"],{style:{marginTop:"5px"},children:ne(v.limit.is_open_config.is_open_tips).map((function(e){return Object(m["jsx"])(b["View"],{className:"num",children:e})}))})}):Object(m["jsx"])(b["View"],{className:"open close",children:Object(m["jsx"])(b["View"],{style:{marginTop:"5px"},children:ne(v.limit.is_open_config.is_open_tips).map((function(e){return Object(m["jsx"])(b["View"],{className:"num",children:e})}))})})})]}),Object(m["jsxs"])(b["View"],{className:"at-row list_show",onClick:function(){d.a.makePhoneCall({phoneNumber:v.brand.tel})},children:[Object(m["jsx"])(b["View"],{className:"at-col at-col-1",children:Object(m["jsx"])(b["Image"],{src:"".concat(f["a"].picUrl,"/info/time.png"),mode:"widthFix",className:"icon"})}),Object(m["jsxs"])(b["View"],{className:"at-col at-col-10 text",children:["\u8425\u4e1a\u65f6\u95f4 ",ie(v.brand.jijie.time)]}),Object(m["jsx"])(b["View"],{className:"at-col at-col-1",children:Object(m["jsx"])(u["f"],{value:"phone",size:"18",color:"#afafaf"})})]}),Object(m["jsxs"])(b["View"],{className:"at-row list_show",onClick:function(){d.a.openLocation({latitude:parseFloat(v.brand.latitude),longitude:parseFloat(v.brand.longitude),name:v.brand.brand_name,address:v.brand.address,scale:12})},children:[Object(m["jsx"])(b["View"],{className:"at-col at-col-1",children:Object(m["jsx"])(b["Image"],{src:"".concat(f["a"].picUrl,"/info/map.png"),mode:"widthFix",className:"icon"})}),Object(m["jsx"])(b["View"],{className:"at-col at-col-10 text",children:"".concat(v.brand.area.join("")).concat(v.brand.address)}),Object(m["jsx"])(b["View"],{className:"at-col at-col-1",children:Object(m["jsx"])(u["f"],{value:"chevron-right",size:"18",color:"#afafaf"})})]}),Object(m["jsxs"])(b["View"],{className:"at-row list_show",children:[Object(m["jsx"])(b["View"],{className:"at-col at-col-1",children:Object(m["jsx"])(b["Image"],{src:"".concat(f["a"].picUrl,"/info/rank.png"),mode:"widthFix",className:"icon"})}),Object(m["jsxs"])(b["View"],{className:"at-col at-col-10 text",children:[f["a"].brandRank(v.brand.rank)," ",f["a"].brandSpace(v.brand.in_out)]}),Object(m["jsx"])(b["View"],{className:"at-col at-col-1"})]}),Object(m["jsxs"])(b["View"],{className:"at-row list_show",children:[Object(m["jsx"])(b["View"],{className:"at-col at-col-1",children:Object(m["jsx"])(b["Image"],{src:"".concat(f["a"].picUrl,"/info/bookt.png"),mode:"widthFix",className:"icon"})}),Object(m["jsxs"])(b["View"],{className:"at-col at-col-10 text",children:["\u5165\u56ed\u65b9\u5f0f ",Object(m["jsx"])(b["Text"],{style:{color:v.limit.is_book?"#FF9800":"#4CAF50",fontWeight:600},children:v.limit.is_book?"\u9700\u63d0\u524d\u9884\u7ea6":"\u65e0\u9700\u9884\u7ea6"}),v.limit.today_num>-1?Object(m["jsxs"])(b["Text"],{style:{color:"red"},children:[" ",v.limit.today_num_str]}):null]})]}),Object(m["jsxs"])(b["View"],{className:"at-row list_show",children:[Object(m["jsx"])(b["View"],{className:"at-col at-col-1",children:Object(m["jsx"])(b["Image"],{src:"".concat(f["a"].picUrl,"/info/date.png"),mode:"widthFix",className:"icon"})}),Object(m["jsxs"])(b["View"],{className:"at-col at-col-10 text",children:["\u5b9e\u65f6\u5929\u6c14 ",Object(m["jsx"])(b["Text"],{children:$})]})]}),Object(m["jsxs"])(b["View"],{className:"at-row list_show",children:[Object(m["jsx"])(b["View"],{className:"at-col at-col-1",children:Object(m["jsx"])(b["Image"],{src:"".concat(f["a"].picUrl,"/info/date.png"),mode:"widthFix",className:"icon"})}),Object(m["jsxs"])(b["View"],{className:"at-col at-col-10 text",children:[Object(m["jsxs"])(b["View"],{children:["\u5f00\u653e\u65f6\u95f4 ","".concat(Object(N["a"])(1e3*v.limit.enable_start_time_unix).format("YYYY\u5e74M\u6708D\u65e5")," \u81f3 ").concat(Object(N["a"])(1e3*v.limit.enable_end_time_unix).format("YYYY\u5e74M\u6708D\u65e5"))]}),Object(m["jsxs"])(b["View"],{style:{paddingTop:"4px",color:"#4CAF50",fontWeight:400},children:[v.limit.enable_week_str.join(" , "),v.limit.is_book?"\u53ef\u7ea6":"\u5f00\u653e"]})]}),Object(m["jsx"])(b["View"],{className:"at-col at-col-1"})]})]}),null!==v.brand.rule_list&&v.brand.rule_list.map((function(e){return Object(m["jsxs"])(b["View"],{children:[Object(m["jsxs"])(b["View"],{className:"brand_title",children:[Object(m["jsx"])(b["View"],{className:"t_icon"}),Object(m["jsx"])(b["View"],{className:"t_name",children:e.val.title})]}),Object(m["jsx"])(b["View"],{className:"brand_html",children:Object(m["jsx"])(b["RichText"],{className:"htmlformat",style:{lineHeight:"30px"},nodes:f["a"].removeCss(e.val.memo)})})]})})),Object(m["jsxs"])(b["View"],{children:[Object(m["jsxs"])(b["View"],{className:"brand_title",children:[Object(m["jsx"])(b["View"],{className:"t_icon"}),Object(m["jsx"])(b["View"],{className:"t_name",children:"\u7279\u8272\u4ecb\u7ecd"})]}),Object(m["jsx"])(b["View"],{className:"brand_html",children:Object(m["jsx"])(b["RichText"],{nodes:f["a"].removeCss(v.brand.content)})})]}),Object(m["jsx"])(b["View"],{style:{height:"80px"}}),Object(m["jsxs"])(b["View"],{className:"at-row brand_memu",children:[Object(m["jsx"])(b["View"],{className:"at-col at-col-4 card_status",children:Object(m["jsxs"])(b["View"],{className:"at-row",children:[Object(m["jsx"])(b["View"],{className:"at-col at-col-2",children:Object(m["jsx"])(b["Image"],{mode:"widthFix",src:"".concat(f["a"].picUrl,"/index/card.png"),className:"c_icon"})}),Object(m["jsx"])(b["View"],{className:"at-col at-col-9",onClick:function(){H.num>0&&z(!0)},children:0===H.num?Object(m["jsx"])(b["Text"],{className:"nocard",children:"\u6682\u65e0\u53ef\u7528\u5361"}):Object(m["jsxs"])(b["Text"],{className:"hadcard",children:["NO.",H.good_id," ",Object(m["jsx"])(b["Text"],{className:"ok",children:"\u6362\u5361"})," "]})})]})}),Object(m["jsx"])(b["View"],{className:"at-col at-col-4",children:v.limit.is_open?Object(m["jsx"])(b["View"],{className:"book",style:v.limit.is_book?{backgroundColor:"#FF5722"}:{},onClick:function(){z(!0)},children:0===H.num?"\u6fc0\u6d3b\u5e74\u7968":function(){return v.limit.is_book?"\u9884\u7ea6\u5165\u56ed":"\u5f00\u653e\u65e5\u5386"}()}):Object(m["jsx"])(b["View"],{className:"book_close",children:"\u6682\u505c\u5165\u56ed"})}),Object(m["jsx"])(b["View"],{className:"at-col at-col-4",children:Object(m["jsx"])(b["View"],{className:"book",style:{backgroundColor:"#FF5722"},onClick:function(){d.a.navigateTo({url:"/pages/buy/index"})},children:"\u8d2d\u4e70\u5e74\u7968"})})]}),Object(m["jsx"])(u["d"],{isOpened:U,title:v.limit.is_book?"\u8bf7\u60a8\u9009\u62e9\u9884\u7ea6\u65e5\u671f":"\u666f\u533a\u5f00\u653e\u65e5\u5386",onClose:function(){B(!1)},children:Object(m["jsx"])(b["View"],{style:{backgroundColor:"#fff"},children:Object(m["jsx"])(x,{start:G.start,end:G.end,list:G.list,book:v.limit.is_book,onChange:function(e){d.a.showModal({title:"No.".concat(H.good_id,"\u9884\u7ea6\u786e\u8ba4"),content:"\u9884\u7ea6 ".concat(e," \u5165\u56ed\uff1f"),success:function(){var t=Object(i["a"])(Object(n["a"])().mark((function t(a){var c,i;return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!a.confirm){t.next=9;break}return B(!1),d.a.showLoading({mask:!0,title:"\u63d0\u4ea4\u4e2d"}),c={brand_code:te,good_id:H.good_id,day:e},t.next=6,f["a"].Api.addBrandBook(c);case 6:i=t.sent,d.a.hideLoading(),d.a.showToast({title:i.message,icon:"none",duration:2e3});case 9:case"end":return t.stop()}}),t)})));function a(e){return t.apply(this,arguments)}return a}()})}})})}),Object(m["jsx"])(u["d"],{isOpened:P,title:"\u8bf7\u60a8\u9009\u62e9\u9700\u9884\u7ea6\u5361",onClose:function(){z(!1)},children:Object(m["jsx"])(b["View"],{style:{backgroundColor:"#fff",width:"93%",margin:"0 auto 20px auto"},children:H.list.map((function(e){return Object(m["jsx"])(O["a"],{data:e,oper:function(e,t){X({num:H.num,good_id:e,list:H.list,name:t}),z(!1),ce()}})}))})})]})})}var v=V,k={navigationBarTitleText:"",navigationStyle:"custom",enableShareAppMessage:!0,transparentTitle:"always",titlePenetrate:"YES"};v.enableShareAppMessage=!0;Page(Object(c["createPageConfig"])(v,"pages/brand/index",{root:{cn:[]}},k||{}))}},[[399,0,1,2,3]]]);