(my["webpackJsonp"]=my["webpackJsonp"]||[]).push([[59],{414:function(e,t,a){"use strict";a.r(t);var c=a(9),n=a(5),i=a(15),o=a(8),s=a(7),r=a(2),j=a(0),b=a(20),u=a(3),l=a.n(u),O=a(22),d=a(37),p=(a(35),a(28)),g=a(6),h=(a(41),a(1)),f=function(e){var t=g["a"].getQuery(),a=Object(b["c"])((function(e){return e.login})),c=Object(r["useState"])(null),f=Object(s["a"])(c,2),w=f[0],x=f[1],m=Object(r["useState"])(1),y=Object(s["a"])(m,2),S=y[0],v=y[1],k=Object(r["useState"])([]),T=Object(s["a"])(k,2),_=T[0],B=T[1],L=Object(r["useState"])(!1),P=Object(s["a"])(L,2),V=P[0],z=P[1],J=Object(r["useState"])(""),N=Object(s["a"])(J,2),A=N[0],C=N[1];return Object(r["useEffect"])(Object(o["a"])(Object(n["a"])().mark((function e(){var c,o;return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(z(!1),l.a.showLoading({mask:!0,title:"\u8bf7\u7a0d\u7b49"}),!a){e.next=10;break}return c={line_id:g["a"].line_code,order:"km",point:!0,action:"type",type_list:[t.data.v],page:{current:S,pageSize:20}},e.next=6,g["a"].Api.brandList(c);case 6:o=e.sent,200===o.code&&(x(o.data.page),B([].concat(Object(i["a"])(_),Object(i["a"])(o.data.list))),C(o.data.typeShow.title),l.a.setNavigationBarTitle({title:"".concat(o.data.typeShow.title,"\u76f8\u5173")})),z(!0),l.a.hideLoading();case 10:case"end":return e.stop()}}),e)}))),[a,S]),Object(u["useReachBottom"])((function(){var e=w.current+1;e<=w.totalPage?v(e):l.a.showToast({title:"\u6682\u65e0\u66f4\u591a\u5185\u5bb9",icon:"none",duration:2e3})})),Object(h["jsxs"])(j["View"],{children:[Object(h["jsx"])(j["View"],{style:{fontSize:"36px",padding:"20px",fontWeight:"bold"},children:A}),Object(h["jsx"])(j["View"],{children:_.length>0?_.map((function(e){return Object(h["jsx"])(p["a"],{className:"list",data:e,cname:"images_l"})})):Object(h["jsx"])(d["a"],{loaded:V})}),Object(h["jsx"])(O["a"],{now:3})]})},w=f,x={navigationBarTitleText:"\u7cbe\u9009\u666f\u533a"};Page(Object(c["createPageConfig"])(w,"pages/sreach/type",{root:{cn:[]}},x||{}))}},[[414,0,1,2,3]]]);