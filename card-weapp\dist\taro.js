/*! For license information please see taro.js.LICENSE.txt */
(my["webpackJsonp"]=my["webpackJsonp"]||[]).push([[1],{0:function(e,t,n){"use strict";n.r(t),n.d(t,"Ad",(function(){return L})),n.d(t,"Audio",(function(){return A})),n.d(t,"Block",(function(){return D})),n.d(t,"Button",(function(){return u})),n.d(t,"Camera",(function(){return x})),n.d(t,"Canvas",(function(){return F})),n.d(t,"Checkbox",(function(){return s})),n.d(t,"CheckboxGroup",(function(){return l})),n.d(t,"ContactButton",(function(){return z})),n.d(t,"CoverImage",(function(){return O})),n.d(t,"CoverView",(function(){return k})),n.d(t,"CustomWrapper",(function(){return U})),n.d(t,"Form",(function(){return d})),n.d(t,"Icon",(function(){return o})),n.d(t,"Image",(function(){return I})),n.d(t,"Input",(function(){return f})),n.d(t,"Label",(function(){return h})),n.d(t,"LifeFollow",(function(){return W})),n.d(t,"Lifestyle",(function(){return H})),n.d(t,"LivePlayer",(function(){return N})),n.d(t,"Lottie",(function(){return $})),n.d(t,"Map",(function(){return M})),n.d(t,"MovableArea",(function(){return S})),n.d(t,"MovableView",(function(){return T})),n.d(t,"Navigator",(function(){return P})),n.d(t,"Picker",(function(){return p})),n.d(t,"PickerView",(function(){return v})),n.d(t,"PickerViewColumn",(function(){return m})),n.d(t,"Progress",(function(){return i})),n.d(t,"Radio",(function(){return g})),n.d(t,"RadioGroup",(function(){return b})),n.d(t,"RichText",(function(){return a})),n.d(t,"ScrollView",(function(){return E})),n.d(t,"Slider",(function(){return y})),n.d(t,"Slot",(function(){return q})),n.d(t,"Swiper",(function(){return C})),n.d(t,"SwiperItem",(function(){return _})),n.d(t,"Switch",(function(){return w})),n.d(t,"Text",(function(){return c})),n.d(t,"Textarea",(function(){return j})),n.d(t,"Video",(function(){return B})),n.d(t,"View",(function(){return r})),n.d(t,"WebView",(function(){return R}));var r="view",o="icon",i="progress",a="rich-text",c="text",u="button",s="checkbox",l="checkbox-group",d="form",f="input",h="label",p="picker",v="picker-view",m="picker-view-column",g="radio",b="radio-group",y="slider",w="switch",O="cover-image",j="textarea",k="cover-view",S="movable-area",T="movable-view",E="scroll-view",C="swiper",_="swiper-item",P="navigator",A="audio",x="camera",I="image",N="live-player",B="video",F="canvas",L="ad",R="web-view",D="block",M="map",q="slot",U="custom-wrapper",$="lottie",H="lifestyle",W="life-follow",z="contact-button"},11:function(e,t,n){"use strict";n.d(t,"a",(function(){return re})),n.d(t,"b",(function(){return ne})),n.d(t,"c",(function(){return ue})),n.d(t,"d",(function(){return te})),n.d(t,"e",(function(){return ie})),n.d(t,"f",(function(){return le})),n.d(t,"g",(function(){return ee})),n.d(t,"h",(function(){return d})),n.d(t,"i",(function(){return u})),n.d(t,"j",(function(){return s})),n.d(t,"k",(function(){return l})),n.d(t,"l",(function(){return c})),n.d(t,"m",(function(){return i})),n.d(t,"n",(function(){return a})),n.d(t,"o",(function(){return pe})),n.d(t,"p",(function(){return ve})),n.d(t,"q",(function(){return oe})),n.d(t,"r",(function(){return Oe})),n.d(t,"s",(function(){return p})),n.d(t,"t",(function(){return ce})),n.d(t,"u",(function(){return ae}));var r=n(15),o=n(13);function i(e){return"string"===typeof e}function a(e){return"undefined"===typeof e}function c(e){return null!==e&&"object"===Object(o["a"])(e)}function u(e){return!0===e||!1===e}function s(e){return"function"===typeof e}function l(e){return"number"===typeof e}var d=Array.isArray,f=("i.".concat("st"),"i.".concat("cl"),{bindTouchStart:"",bindTouchMove:"",bindTouchEnd:"",bindTouchCancel:"",bindLongTap:""}),h={bindAnimationStart:"",bindAnimationIteration:"",bindAnimationEnd:"",bindTransitionEnd:""};function p(e){return"'".concat(e,"'")}var v=Object.assign(Object.assign({"hover-class":p("none"),"hover-stop-propagation":"false","hover-start-time":"50","hover-stay-time":"400",animation:""},f),h),m={type:"",size:"23",color:""},g=Object.assign({longitude:"",latitude:"",scale:"16",markers:"[]",covers:"",polyline:"[]",circles:"[]",controls:"[]","include-points":"[]","show-location":"","layer-style":"1",bindMarkerTap:"",bindControlTap:"",bindCalloutTap:"",bindUpdated:""},f),b={percent:"","stroke-width":"6",color:p("#09BB07"),activeColor:p("#09BB07"),backgroundColor:p("#EBEBEB"),active:"false","active-mode":p("backwards"),"show-info":"false"},y={nodes:"[]"},w={selectable:"false",space:"",decode:"false"},O=Object.assign({size:p("default"),type:"",plain:"false",disabled:"",loading:"false","form-type":"","open-type":"","hover-class":p("button-hover"),"hover-stop-propagation":"false","hover-start-time":"20","hover-stay-time":"70",name:""},f),j={value:"",disabled:"",checked:"false",color:p("#09BB07"),name:""},k={bindChange:"",name:""},S={"report-submit":"false",bindSubmit:"",bindReset:"",name:""},T={value:"",type:p(""),password:"false",placeholder:"","placeholder-style":"","placeholder-class":p("input-placeholder"),disabled:"",maxlength:"140","cursor-spacing":"0",focus:"false","confirm-type":p("done"),"confirm-hold":"false",cursor:"i.value.length","selection-start":"-1","selection-end":"-1",bindInput:"",bindFocus:"",bindBlur:"",bindConfirm:"",name:""},E={for:"",name:""},C={mode:p("selector"),disabled:"",range:"","range-key":"",value:"",start:"",end:"",fields:p("day"),"custom-item":"",name:"",bindCancel:"",bindChange:"",bindColumnChange:""},_={value:"","indicator-style":"","indicator-class":"","mask-style":"","mask-class":"",bindChange:"",name:""},P={name:""},A={value:"",checked:"false",disabled:"",color:p("#09BB07"),name:""},x={bindChange:"",name:""},I={min:"0",max:"100",step:"1",disabled:"",value:"0",activeColor:p("#1aad19"),backgroundColor:p("#e9e9e9"),"block-size":"28","block-color":p("#ffffff"),"show-value":"false",bindChange:"",bindChanging:"",name:""},N={checked:"false",disabled:"",type:p("switch"),color:p("#04BE02"),bindChange:"",name:""},B={value:"",placeholder:"","placeholder-style":"","placeholder-class":p("textarea-placeholder"),disabled:"",maxlength:"140","auto-focus":"false",focus:"false","auto-height":"false",fixed:"false","cursor-spacing":"0",cursor:"-1","selection-start":"-1","selection-end":"-1",bindFocus:"",bindBlur:"",bindLineChange:"",bindInput:"",bindConfirm:"",name:""},F={src:"",bindLoad:"eh",bindError:"eh"},L=Object.assign({"scroll-top":"false"},f),R={"scale-area":"false"},D=Object.assign(Object.assign({direction:"none",inertia:"false","out-of-bounds":"false",x:"",y:"",damping:"20",friction:"2",disabled:"",scale:"false","scale-min":"0.5","scale-max":"10","scale-value":"1",animation:"true",bindChange:"",bindScale:"",bindHTouchMove:"",bindVTouchMove:"",width:p("10px"),height:p("10px")},f),h),M=Object.assign(Object.assign({"scroll-x":"false","scroll-y":"false","upper-threshold":"50","lower-threshold":"50","scroll-top":"","scroll-left":"","scroll-into-view":"","scroll-with-animation":"false","enable-back-to-top":"false",bindScrollToUpper:"",bindScrollToLower:"",bindScroll:""},f),h),q=Object.assign({"indicator-dots":"false","indicator-color":p("rgba(0, 0, 0, .3)"),"indicator-active-color":p("#000000"),autoplay:"false",current:"0",interval:"5000",duration:"500",circular:"false",vertical:"false","previous-margin":"'0px'","next-margin":"'0px'","display-multiple-items":"1",bindChange:"",bindTransition:"",bindAnimationFinish:""},f),U={"item-id":""},$={url:"","open-type":p("navigate"),delta:"1","hover-class":p("navigator-hover"),"hover-stop-propagation":"false","hover-start-time":"50","hover-stay-time":"600",bindSuccess:"",bindFail:"",bindComplete:""},H={id:"",src:"",loop:"false",controls:"false",poster:"",name:"",author:"",bindError:"",bindPlay:"",bindPause:"",bindTimeUpdate:"",bindEnded:""},W={"device-position":p("back"),flash:p("auto"),bindStop:"",bindError:""},z=Object.assign({src:"",mode:p("scaleToFill"),"lazy-load":"false",bindError:"",bindLoad:""},f),V={src:"",autoplay:"false",muted:"false",orientation:p("vertical"),"object-fit":p("contain"),"background-mute":"false","min-cache":"1","max-cache":"3",animation:"",bindStateChange:"",bindFullScreenChange:"",bindNetStatus:""},G={src:"",duration:"",controls:"true","danmu-list":"","danmu-btn":"","enable-danmu":"",autoplay:"false",loop:"false",muted:"false","initial-time":"0","page-gesture":"false",direction:"","show-progress":"true","show-fullscreen-btn":"true","show-play-btn":"true","show-center-play-btn":"true","enable-progress-gesture":"true","object-fit":p("contain"),poster:"","show-mute-btn":"false",animation:"",bindPlay:"",bindPause:"",bindEnded:"",bindTimeUpdate:"",bindFullScreenChange:"",bindWaiting:"",bindError:""},K=Object.assign({"canvas-id":"","disable-scroll":"false",bindError:""},f),Q={"unit-id":"","ad-intervals":"",bindLoad:"",bindError:"",bindClose:""},Y={src:"",bindMessage:"",bindLoad:"",bindError:""},J={},X={name:""},Z={name:""},ee={View:v,Icon:m,Progress:b,RichText:y,Text:w,Button:O,Checkbox:j,CheckboxGroup:k,Form:S,Input:T,Label:E,Picker:C,PickerView:_,PickerViewColumn:P,Radio:A,RadioGroup:x,Slider:I,Switch:N,CoverImage:F,Textarea:B,CoverView:L,MovableArea:R,MovableView:D,ScrollView:M,Swiper:q,SwiperItem:U,Navigator:$,Audio:H,Camera:W,Image:z,LivePlayer:V,Video:G,Canvas:K,Ad:Q,WebView:Y,Block:J,Map:g,Slot:Z,SlotView:X},te=new Set(["input","checkbox","picker","picker-view","radio","slider","switch","textarea"]),ne=(new Set(["input","textarea"]),new Set(["progress","icon","rich-text","input","textarea","slider","switch","audio","ad","official-account","open-data","navigation-bar"]),new Map([["view",-1],["catch-view",-1],["cover-view",-1],["static-view",-1],["pure-view",-1],["block",-1],["text",-1],["static-text",6],["slot",8],["slot-view",8],["label",6],["form",4],["scroll-view",4],["swiper",4],["swiper-item",4]]),{}),re=[],oe=function(){},ie=Object.create(null);function ae(e){return e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}function ce(e){for(var t="",n=!1,r=0;r<e.length;r++)"-"!==e[r]?(t+=n?e[r].toUpperCase():e[r],n=!1):n=!0;return t}function ue(e){return e.charAt(0).toUpperCase()+e.slice(1)}Object.prototype.hasOwnProperty;var se="\u5982\u6709\u7591\u95ee\uff0c\u8bf7\u63d0\u4ea4 issue \u81f3\uff1ahttps://github.com/nervjs/taro/issues";function le(e,t){if(!e)throw new Error(t+"\n"+se)}var de=1,fe=(new Date).getTime().toString();function he(){return fe+de++}function pe(e){Object.keys(e).forEach((function(t){t in ee?Object.assign(ee[t],e[t]):ee[t]=e[t]}))}function ve(e){Object.keys(e).forEach((function(t){var n=e[t],r=ie[t];r?d(r)?ie[t]=r.push(n):ie[t]=[r,n]:ie[t]=n}))}function me(e){return function(){console.warn("\u5c0f\u7a0b\u5e8f\u6682\u4e0d\u652f\u6301 ".concat(e))}}function ge(e,t){var n="__key_",r=["navigateTo","redirectTo","reLaunch","switchTab"];if(r.indexOf(e)>-1){var o=t.url=t.url||"",i=o.indexOf("?")>-1,a=he();t.url+=(i?"&":"?")+"".concat(n,"=").concat(a)}}var be=new Set(["addPhoneContact","authorize","canvasGetImageData","canvasPutImageData","canvasToTempFilePath","checkSession","chooseAddress","chooseImage","chooseInvoiceTitle","chooseLocation","chooseVideo","clearStorage","closeBLEConnection","closeBluetoothAdapter","closeSocket","compressImage","connectSocket","createBLEConnection","downloadFile","exitMiniProgram","getAvailableAudioSources","getBLEDeviceCharacteristics","getBLEDeviceServices","getBatteryInfo","getBeacons","getBluetoothAdapterState","getBluetoothDevices","getClipboardData","getConnectedBluetoothDevices","getConnectedWifi","getExtConfig","getFileInfo","getImageInfo","getLocation","getNetworkType","getSavedFileInfo","getSavedFileList","getScreenBrightness","getSetting","getStorage","getStorageInfo","getSystemInfo","getUserInfo","getWifiList","hideHomeButton","hideShareMenu","hideTabBar","hideTabBarRedDot","loadFontFace","login","makePhoneCall","navigateBack","navigateBackMiniProgram","navigateTo","navigateToBookshelf","navigateToMiniProgram","notifyBLECharacteristicValueChange","hideKeyboard","hideLoading","hideNavigationBarLoading","hideToast","openBluetoothAdapter","openDocument","openLocation","openSetting","pageScrollTo","previewImage","queryBookshelf","reLaunch","readBLECharacteristicValue","redirectTo","removeSavedFile","removeStorage","removeTabBarBadge","requestSubscribeMessage","saveFile","saveImageToPhotosAlbum","saveVideoToPhotosAlbum","scanCode","sendSocketMessage","setBackgroundColor","setBackgroundTextStyle","setClipboardData","setEnableDebug","setInnerAudioOption","setKeepScreenOn","setNavigationBarColor","setNavigationBarTitle","setScreenBrightness","setStorage","setTabBarBadge","setTabBarItem","setTabBarStyle","showActionSheet","showFavoriteGuide","showLoading","showModal","showShareMenu","showTabBar","showTabBarRedDot","showToast","startBeaconDiscovery","startBluetoothDevicesDiscovery","startDeviceMotionListening","startPullDownRefresh","stopBeaconDiscovery","stopBluetoothDevicesDiscovery","stopCompass","startCompass","startAccelerometer","stopAccelerometer","showNavigationBarLoading","stopDeviceMotionListening","stopPullDownRefresh","switchTab","uploadFile","vibrateLong","vibrateShort","writeBLECharacteristicValue"]);function ye(e){return function(){if("function"!==typeof e.getSystemInfoSync)return console.error("\u4e0d\u652f\u6301 API canIUseWebp"),!1;var t=e.getSystemInfoSync(),n=t.platform,r=n.toLowerCase();return"android"===r||"devtools"===r}}function we(e){return function(t){t=t||{},"string"===typeof t&&(t={url:t});var n,r=t.success,o=t.fail,i=t.complete,a=new Promise((function(a,c){t.success=function(e){r&&r(e),a(e)},t.fail=function(e){o&&o(e),c(e)},t.complete=function(e){i&&i(e)},n=e.request(t)}));return a.abort=function(e){return e&&e(),n&&n.abort(),a},a}}function Oe(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=n.needPromiseApis||[],i=new Set([].concat(Object(r["a"])(o),Object(r["a"])(be))),a=["getEnv","interceptors","Current","getCurrentInstance","options","nextTick","eventCenter","Events","preload","webpackJsonp"],c=new Set(n.isOnlyPromisify?o:Object.keys(t).filter((function(e){return-1===a.indexOf(e)})));n.modifyApis&&n.modifyApis(c),c.forEach((function(r){if(i.has(r)){var o=r;e[o]=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},r=arguments.length,i=new Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];var c=o;if("string"===typeof e)return i.length?t[c].apply(t,[e].concat(i)):t[c](e);if(n.transformMeta){var u=n.transformMeta(c,e);if(c=u.key,e=u.options,!t.hasOwnProperty(c))return me(c)()}var s=null,l=Object.assign({},e);ge(c,e);var d=new Promise((function(r,o){l.success=function(t){var o,i;null===(o=n.modifyAsyncResult)||void 0===o||o.call(n,c,t),null===(i=e.success)||void 0===i||i.call(e,t),r("connectSocket"===c?Promise.resolve().then((function(){return s?Object.assign(s,t):t})):t)},l.fail=function(t){var n;null===(n=e.fail)||void 0===n||n.call(e,t),o(t)},l.complete=function(t){var n;null===(n=e.complete)||void 0===n||n.call(e,t)},s=i.length?t[c].apply(t,[l].concat(i)):t[c](l)}));return"uploadFile"!==c&&"downloadFile"!==c||(d.progress=function(e){return null===s||void 0===s||s.onProgressUpdate(e),d},d.abort=function(e){return null===e||void 0===e||e(),null===s||void 0===s||s.abort(),d}),d}}else{var a=r;if(n.transformMeta&&(a=n.transformMeta(r,{}).key),!t.hasOwnProperty(a))return void(e[r]=me(r));"function"===typeof t[r]?e[r]=function(){for(var e=arguments.length,o=new Array(e),i=0;i<e;i++)o[i]=arguments[i];return n.handleSyncApis?n.handleSyncApis(r,t,o):t[a].apply(t,o)}:e[r]=t[a]}})),!n.isOnlyPromisify&&je(e,t,n)}function je(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};e.canIUseWebp=ye(e),e.getCurrentPages=getCurrentPages||me("getCurrentPages"),e.getApp=getApp||me("getApp"),e.env=t.env||{};try{e.requirePlugin=requirePlugin||me("requirePlugin")}catch(t){e.requirePlugin=me("requirePlugin")}var r=n.request?n.request:we(t);function o(e){return r(e.requestParams)}var i=new e.Link(o);e.request=i.request.bind(i),e.addInterceptor=i.addInterceptor.bind(i),e.cleanInterceptors=i.cleanInterceptors.bind(i),e.miniGlobal=e.options.miniGlobal=t}},219:function(e,t,n){var r,o=n(255).default;r=n(311);var i=r&&r.default?r.default:r;"function"===typeof i&&i(o),e.exports=o,e.exports.default=e.exports},255:function(e,t,n){"use strict";n.r(t),n.d(t,"default",(function(){return T}));var r={};n.r(r),n.d(r,"logInterceptor",(function(){return p})),n.d(r,"timeoutInterceptor",(function(){return h}));var o=n(9),i={WEAPP:"WEAPP",SWAN:"SWAN",ALIPAY:"ALIPAY",TT:"TT",QQ:"QQ",JD:"JD",WEB:"WEB",RN:"RN",HARMONY:"HARMONY",QUICKAPP:"QUICKAPP",HARMONYHYBRID:"HARMONYHYBRID"};function a(){return i.ALIPAY}var c=n(16),u=n(17),s=n(429),l=function(){function e(t,n,r){Object(c["a"])(this,e),this.index=r||0,this.requestParams=t||{},this.interceptors=n||[]}return Object(u["a"])(e,[{key:"proceed",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(this.requestParams=e,this.index>=this.interceptors.length)throw new Error("chain \u53c2\u6570\u9519\u8bef, \u8bf7\u52ff\u76f4\u63a5\u4fee\u6539 request.chain");var t=this._getNextInterceptor(),n=this._getNextChain(),r=t(n),o=r.catch((function(e){return Promise.reject(e)}));return Object.keys(r).forEach((function(e){return Object(s["a"])(r[e])&&(o[e]=r[e])})),o}},{key:"_getNextInterceptor",value:function(){return this.interceptors[this.index]}},{key:"_getNextChain",value:function(){return new e(this.requestParams,this.interceptors,this.index+1)}}]),e}(),d=function(){function e(t){Object(c["a"])(this,e),this.taroInterceptor=t,this.chain=new l}return Object(u["a"])(e,[{key:"request",value:function(e){var t=this.chain,n=this.taroInterceptor;return t.interceptors=t.interceptors.filter((function(e){return e!==n})).concat(n),t.proceed(Object.assign({},e))}},{key:"addInterceptor",value:function(e){this.chain.interceptors.push(e)}},{key:"cleanInterceptors",value:function(){this.chain=new l}}]),e}();function f(e){return new d((function(t){return e(t.requestParams)}))}function h(e){var t,n=e.requestParams,r=new Promise((function(r,o){var i=setTimeout((function(){clearTimeout(i),o(new Error("\u7f51\u7edc\u94fe\u63a5\u8d85\u65f6,\u8bf7\u7a0d\u540e\u518d\u8bd5\uff01"))}),n&&n.timeout||6e4);t=e.proceed(n),t.then((function(e){i&&(clearTimeout(i),r(e))})).catch((function(e){i&&clearTimeout(i),o(e)}))}));return!Object(s["c"])(t)&&Object(s["a"])(t.abort)&&(r.abort=t.abort),r}function p(e){var t=e.requestParams,n=t.method,r=t.data,o=t.url;console.log("http ".concat(n||"GET"," --\x3e ").concat(o," data: "),r);var i=e.proceed(t),a=i.then((function(e){return console.log("http <-- ".concat(o," result:"),e),e}));return Object(s["a"])(i.abort)&&(a.abort=i.abort),a}var v=n(18);function m(e){return e}function g(e){return function(t,n){e.preloadData=Object(s["b"])(t)?t:Object(v["a"])({},t,n)}}var b=750,y={640:1.17,750:1,828:.905},w=20,O=5,j="rpx";function k(e){return function(t){var n=t.designWidth,r=void 0===n?b:n,o=t.deviceRatio,i=void 0===o?y:o,a=t.baseFontSize,c=void 0===a?w:a,u=t.targetUnit,s=void 0===u?j:u,l=t.unitPrecision,d=void 0===l?O:l;e.config=e.config||{},e.config.designWidth=r,e.config.deviceRatio=i,e.config.baseFontSize=c,e.config.targetUnit=s,e.config.unitPrecision=d}}function S(e){return function(t){var n=e.config||{},r=n.baseFontSize,o=n.deviceRatio||y,i=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;return Object(s["a"])(n.designWidth)?n.designWidth(e):n.designWidth||b}(t);if(!(i in o))throw new Error("deviceRatio \u914d\u7f6e\u4e2d\u4e0d\u5b58\u5728 ".concat(i," \u7684\u8bbe\u7f6e\uff01"));var a=n.targetUnit||j,c=n.unitPrecision||O,u=~~t,l=1/o[i];switch(a){case"rem":l*=2*r;break;case"px":l*=2;break}var d=u/l;return c>=0&&c<=100&&(d=Number(d.toFixed(c))),d+a}}var T={Behavior:m,getEnv:a,ENV_TYPE:i,Link:d,interceptors:r,Current:o["Current"],getCurrentInstance:o["getCurrentInstance"],options:o["options"],nextTick:o["nextTick"],eventCenter:o["eventCenter"],Events:o["Events"],getInitPxTransform:k,interceptorify:f};T.initPxTransform=k(T),T.preload=g(o["Current"]),T.pxTransform=S(T)},294:function(e,t,n){"use strict";var r=n(11),o=new Set(["addCardAuth","getOpenUserInfo","chooseAlipayContact","chooseCity","chooseContact","choosePhoneContact","datePicker","getAddress","getAuthCode","getPhoneNumber","getRunData","getRunScene","getServerTime","getTitleColor","rsa","paySignCenter","tradePay","isCollected","multiLevelSelect","onLocatedComplete","optionsSelect","prompt","regionPicker","setLocatedCity","showAuthGuide","textRiskIdentification","vibrate","watchShake","connectBLEDevice","disconnectBLEDevice","makeBluetoothPair","writeBLECharacteristicValue","readBLECharacteristicValue","notifyBLECharacteristicValueChange","getBLEDeviceServices","getBLEDeviceCharacteristics","openBluetoothAdapter","closeBluetoothAdapter","getBluetoothAdapterState","startBluetoothDevicesDiscovery","stopBluetoothDevicesDiscovery","getBluetoothDevices","getConnectedBluetoothDevices"]),i={showActionSheet:{options:{change:[{old:"itemList",new:"items"}]}},showToast:{options:{change:[{old:"title",new:"content"},{old:"icon",new:"type"}]}},showLoading:{options:{change:[{old:"title",new:"content"}]}},setNavigationBarTitle:{alias:"setNavigationBar"},setNavigationBarColor:{alias:"setNavigationBar"},saveImageToPhotosAlbum:{alias:"saveImage",options:{change:[{old:"filePath",new:"url"}]}},previewImage:{options:{set:[{key:"current",value:function(e){return e.urls.indexOf(e.current||e.urls[0])}}]}},getFileInfo:{options:{change:[{old:"filePath",new:"apFilePath"}]}},getSavedFileInfo:{options:{change:[{old:"filePath",new:"apFilePath"}]}},removeSavedFile:{options:{change:[{old:"filePath",new:"apFilePath"}]}},saveFile:{options:{change:[{old:"tempFilePath",new:"apFilePath"}]}},openLocation:{options:{set:[{key:"latitude",value:function(e){return String(e.latitude)}},{key:"longitude",value:function(e){return String(e.longitude)}}]}},uploadFile:{options:{change:[{old:"name",new:"fileName"}]}},getClipboardData:{alias:"getClipboard"},setClipboardData:{alias:"setClipboard",options:{change:[{old:"data",new:"text"}]}},makePhoneCall:{options:{change:[{old:"phoneNumber",new:"number"}]}},scanCode:{alias:"scan",options:{change:[{old:"onlyFromCamera",new:"hideAlbum"}],set:[{key:"type",value:function(e){return e.scanType&&e.scanType[0].slice(0,-4)||"qr"}}]}},setScreenBrightness:{options:{change:[{old:"value",new:"brightness"}]}},onBLEConnectionStateChange:{alias:"onBLEConnectionStateChanged"},offBLEConnectionStateChange:{alias:"offBLEConnectionStateChanged"},createBLEConnection:{alias:"connectBLEDevice"},closeBLEConnection:{alias:"disconnectBLEDevice"}},a=my.canIUse("request")?my.request:my.httpRequest;function c(e){e=e||{},"string"===typeof e&&(e={url:e});var t={"content-type":"application/json"};if(e.headers=t,e.header){for(var n in e.header){var r=n.toLocaleLowerCase();e.headers[r]=e.header[n]}delete e.header}var o,i=e.success,c=e.fail,u=e.complete,s=new Promise((function(t,n){e.success=function(e){e.statusCode=e.status,delete e.status,e.header=e.headers,delete e.headers,i&&i(e),t(e)},e.fail=function(e){c&&c(e),n(e)},e.complete=function(e){u&&u(e)},o=a(e)}));return s.abort=function(e){return e&&e(),o&&o.abort(),s},s}function u(e,t,n){if("getStorageSync"===e){var r=n[0];if(null!=r){var o=t[e]({key:r}),i=null;return o.hasOwnProperty("data")?i=o.data:o.hasOwnProperty("APDataStorage")&&(i=o.APDataStorage),null===i?"":i}return console.error("getStorageSync \u4f20\u5165\u53c2\u6570\u9519\u8bef")}if("setStorageSync"===e){var a=n[0],c=n[1];return null!=a?t[e]({key:a,data:c}):console.error("setStorageSync \u4f20\u5165\u53c2\u6570\u9519\u8bef")}if("removeStorageSync"===e){var u=n[0];return null!=u?t[e]({key:u}):console.error("removeStorageSync \u4f20\u5165\u53c2\u6570\u9519\u8bef")}if("createSelectorQuery"===e){var s=t[e]();return s.in=function(){return s},s}return t[e].apply(t,n)}function s(e,t){var n=e;return"showModal"===e?(t.cancelButtonText=t.cancelText||"\u53d6\u6d88",t.confirmButtonText=t.confirmText||"\u786e\u5b9a",n="confirm",!1===t.showCancel&&(t.buttonText=t.confirmText||"\u786e\u5b9a",n="alert")):Object.keys(i).forEach((function(r){var o=i[r];if(e===r&&(o.alias&&(n=o.alias),o.options)){var a=o.options.change,c=o.options.set;a&&a.forEach((function(e){t[e.new]=t[e.old]})),c&&c.forEach((function(e){t[e.key]="function"===typeof e.value?e.value(t):e.value}))}})),{key:n,options:t}}function l(e){Object.keys(i).map((function(t){e.add(t);var n=i[t].alias;n&&e.delete(n)})),e.add("showModal"),e.delete("confirm"),e.delete("alert")}function d(e,t){"saveFile"===e?t.savedFilePath=t.apFilePath:"downloadFile"===e?t.tempFilePath=t.apFilePath:"chooseImage"===e?t.tempFilePaths=t.apFilePaths:"getClipboard"===e?t.data=t.text:"scan"===e?t.result=t.code:"getScreenBrightness"===e?(t.value=t.brightness,delete t.brightness):"connectSocket"===e&&(t.onClose=function(e){my.onSocketClose(e)},t.onError=function(e){my.onSocketError(e)},t.onMessage=function(e){my.onSocketMessage(e)},t.onOpen=function(e){my.onSocketOpen(e)},t.send=function(e){my.sendSocketMessage(e)},t.close=function(){my.closeSocket()})}function f(e){Object(r["r"])(e,my,{needPromiseApis:o,handleSyncApis:u,transformMeta:s,modifyApis:l,modifyAsyncResult:d,request:c})}var h={View:{"disable-scroll":"false",hidden:"false",bindAppear:"",bindDisappear:"",bindFirstAppear:""},Text:{"number-of-lines":""},Map:{skew:"0",rotate:"0",polygons:"[]","include-padding":"","ground-overlays":"","tile-overlay":"","custom-map-style":"",setting:"{}",optimize:"",bindRegionChange:"",bindPanelTap:""},Button:{scope:"","public-id":"",bindGetAuthorize:"",bindError:""},Checkbox:{bindChange:""},Input:{"random-number":"false",controlled:"false",enableNative:"false"},Slider:{"track-size":"4","handle-size":"22","handle-color":Object(r["s"])("#ffffff")},Switch:{controlled:"false"},Textarea:{"show-count":"true",controlled:"false",enableNative:"false"},MovableView:{bindChangeEnd:""},ScrollView:{"scroll-animation-duration":"","trap-scroll":"false"},Swiper:{"active-class":"","changing-class":"",acceleration:"false","disable-programmatic-animation":"false","disable-touch":"false",bindAnimationEnd:""},Image:{"default-source":""},Canvas:{type:"",width:Object(r["s"])("300px"),height:Object(r["s"])("225px"),bindReady:""},Video:{"poster-size":Object(r["s"])("contain"),"mobilenet-hint-type":"1",enableNative:"false",bindLoading:"",bindUserAction:"",bindStop:"",bindRenderStart:""},Lottie:{autoplay:"false",path:"",speed:"1.0",repeatCount:"0",autoReverse:"false",assetsPath:"",placeholder:"",djangoId:"",md5:"",optimize:"false",bindDataReady:"",bindDataFailed:"",bindAnimationStart:"",bindAnimationEnd:"",bindAnimationRepeat:"",bindAnimationCancel:"",bindDataLoadReady:""},Lifestyle:{"public-id":"",memo:"",bindFollow:""},LifeFollow:{sceneId:"",checkFollow:"",bindCheckFollow:"",bindClose:""},ContactButton:{"tnt-inst-id":"",scene:"",size:"25",color:Object(r["s"])("#00A3FF"),icon:"","alipay-card-no":"","ext-info":""}},p=new Set(["touchStart","touchMove","touchEnd","touchCancel","tap","longTap"]),v={initNativeApi:f,getEventCenter:function(e){return my.taroEventCenter||(my.taroEventCenter=new e),my.taroEventCenter},modifyTaroEvent:function(e,t){"SWIPER"===t.tagName&&"animationend"===e.type&&(e.type="animationfinish")},isBubbleEvents:function(e){return p.has(e)}},m=navigator,g=m.userAgent;Object.defineProperty(navigator,"userAgent",{configurable:!0,enumerable:!0,get:function(){return g||navigator.swuserAgent||""}}),Object(r["p"])(v),Object(r["o"])(h)},295:function(e,t,n){"use strict";n.r(t),function(e,r){var o=n(34),i=n.n(o),a=n(258),c=n.n(a),u=n(77),s=n.n(u),l=n(78),d=n.n(l),f=n(75),h=n.n(f),p=n(9);"function"!==typeof Object.assign&&(Object.assign=function(e){if(null==e)throw new TypeError("Cannot convert undefined or null to object");for(var t=Object(e),n=1;n<arguments.length;n++){var r=arguments[n];if(null!=r)for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(t[o]=r[o])}return t}),"function"!==typeof Object.defineProperties&&(Object.defineProperties=function(e,t){function n(e){function t(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function n(e){return"function"===typeof e}if("object"!==i()(e)||null===e)throw new TypeError("bad desc");var r={};if(t(e,"enumerable")&&(r.enumerable=!!e.enumerable),t(e,"configurable")&&(r.configurable=!!e.configurable),t(e,"value")&&(r.value=e.value),t(e,"writable")&&(r.writable=!!e.writable),t(e,"get")){var o=e.get;if(!n(o)&&"undefined"!==typeof o)throw new TypeError("bad get");r.get=o}if(t(e,"set")){var a=e.set;if(!n(a)&&"undefined"!==typeof a)throw new TypeError("bad set");r.set=a}if(("get"in r||"set"in r)&&("value"in r||"writable"in r))throw new TypeError("identity-confused descriptor");return r}if("object"!==i()(e)||null===e)throw new TypeError("bad obj");t=Object(t);for(var r=Object.keys(t),o=[],a=0;a<r.length;a++)o.push([r[a],n(t[r[a]])]);for(var c=0;c<o.length;c++)Object.defineProperty(e,o[c][0],o[c][1]);return e});var v={WEAPP:"WEAPP",WEB:"WEB",RN:"RN",SWAN:"SWAN",ALIPAY:"ALIPAY",TT:"TT",QQ:"QQ",JD:"JD",KWAI:"KWAI"},m=null;function g(){return m||("undefined"!==typeof jd&&jd.getSystemInfo?(m=v.JD,v.JD):"undefined"!==typeof qq&&qq.getSystemInfo?(m=v.QQ,v.QQ):"undefined"!==typeof tt&&tt.getSystemInfo?(m=v.TT,v.TT):"undefined"!==typeof wx&&wx.getSystemInfo?(m=v.WEAPP,v.WEAPP):"undefined"!==typeof swan&&swan.getSystemInfo?(m=v.SWAN,v.SWAN):"undefined"!==typeof my&&my.getSystemInfo?(m=v.ALIPAY,v.ALIPAY):"undefined"!==typeof ks&&ks.getSystemInfo?(m=v.KWAI,v.KWAI):"undefined"!==typeof e&&e.__fbGenNativeModule?(m=v.RN,v.RN):"undefined"!==typeof r?(m=v.WEB,v.WEB):"Unknown environment")}var b=function(){function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;s()(this,e),this.index=r,this.requestParams=t,this.interceptors=n}return d()(e,[{key:"proceed",value:function(e){if(this.requestParams=e,this.index>=this.interceptors.length)throw new Error("chain \u53c2\u6570\u9519\u8bef, \u8bf7\u52ff\u76f4\u63a5\u4fee\u6539 request.chain");var t=this._getNextInterceptor(),n=this._getNextChain(),r=t(n),o=r.catch((function(e){return Promise.reject(e)}));return"function"===typeof r.abort&&(o.abort=r.abort),o}},{key:"_getNextInterceptor",value:function(){return this.interceptors[this.index]}},{key:"_getNextChain",value:function(){return new e(this.requestParams,this.interceptors,this.index+1)}}]),e}(),y=function(){function e(t){s()(this,e),this.taroInterceptor=t,this.chain=new b}return d()(e,[{key:"request",value:function(e){var t=this;return this.chain.interceptors=this.chain.interceptors.filter((function(e){return e!==t.taroInterceptor})),this.chain.interceptors.push(this.taroInterceptor),this.chain.proceed(c()({},e))}},{key:"addInterceptor",value:function(e){this.chain.interceptors.push(e)}},{key:"cleanInterceptors",value:function(){this.chain=new b}}]),e}();function w(e){var t,n=e.requestParams,r=new Promise((function(r,o){var i=setTimeout((function(){i=null,o(new Error("\u7f51\u7edc\u94fe\u63a5\u8d85\u65f6,\u8bf7\u7a0d\u540e\u518d\u8bd5\uff01"))}),n&&n.timeout||6e4);t=e.proceed(n),t.then((function(e){i&&(clearTimeout(i),r(e))})).catch((function(e){i&&clearTimeout(i),o(e)}))}));return void 0!==t&&"function"===typeof t.abort&&(r.abort=t.abort),r}function O(e){var t=e.requestParams,n=t.method,r=t.data,o=t.url;console.log("http ".concat(n||"GET"," --\x3e ").concat(o," data: "),r);var i=e.proceed(t),a=i.then((function(e){return console.log("http <-- ".concat(o," result:"),e),e}));return"function"===typeof i.abort&&(a.abort=i.abort),a}var j=Object.freeze({__proto__:null,timeoutInterceptor:w,logInterceptor:O});function k(e){return e}function S(e){return function(t,n){"object"===i()(t)?e.preloadData=t:void 0!==t&&void 0!==n&&(e.preloadData=h()({},t,n))}}function T(e){return function(t){var n=t.designWidth,r=void 0===n?750:n,o=t.deviceRatio,i=void 0===o?{640:1.17,750:1,828:.905}:o;e.config=e.config||{},e.config.designWidth=r,e.config.deviceRatio=i}}function E(e){return function(t){var n=e.config||{},r=n.designWidth,o=void 0===r?750:r,i=n.deviceRatio,a=void 0===i?{640:1.17,750:1,828:.905}:i;if(!(o in a))throw new Error("deviceRatio \u914d\u7f6e\u4e2d\u4e0d\u5b58\u5728 ".concat(o," \u7684\u8bbe\u7f6e\uff01"));return parseInt(t,10)*a[o]+"rpx"}}var C={Behavior:k,getEnv:g,ENV_TYPE:v,Link:y,interceptors:j,Current:p["Current"],getCurrentInstance:p["getCurrentInstance"],options:p["options"],nextTick:p["nextTick"],eventCenter:p["eventCenter"],Events:p["Events"],useDidShow:p["useDidShow"],useDidHide:p["useDidHide"],usePullDownRefresh:p["usePullDownRefresh"],useReachBottom:p["useReachBottom"],usePageScroll:p["usePageScroll"],useResize:p["useResize"],useShareAppMessage:p["useShareAppMessage"],useTabItemTap:p["useTabItemTap"],useTitleClick:p["useTitleClick"],useOptionMenuClick:p["useOptionMenuClick"],usePullIntercept:p["usePullIntercept"],useShareTimeline:p["useShareTimeline"],useAddToFavorites:p["useAddToFavorites"],useReady:p["useReady"],useRouter:p["useRouter"],getInitPxTransform:T};C.initPxTransform=T(C),C.preload=S(p["Current"]),C.pxTransform=E(C),t["default"]=C}.call(this,n(29),n(9)["window"])},3:function(e,t,n){var r=n(9),o=r.container,i=r.SERVICE_IDENTIFIER,a=n(295).default,c=o.get(i.Hooks);"function"===typeof c.initNativeApi&&c.initNativeApi(a),e.exports=a,e.exports.default=e.exports},311:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t["default"]=C;var r=a(n(255)),o=n(312),i=n(313);function a(e){return e&&e.__esModule?e:{default:e}}function c(e){return d(e)||l(e)||s(e)||u()}function u(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function s(e,t){if(e){if("string"===typeof e)return f(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?f(e,t):void 0}}function l(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}function d(e){if(Array.isArray(e))return f(e)}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var h=r["default"].noPromiseApis,p=r["default"].onAndSyncApis,v=r["default"].otherApis,m=r["default"].initPxTransform,g=r["default"].Link,b={showActionSheet:{options:{change:[{old:"itemList",new:"items"}]}},showToast:{options:{change:[{old:"title",new:"content"},{old:"icon",new:"type"}]}},showLoading:{options:{change:[{old:"title",new:"content"}]}},setNavigationBarTitle:{alias:"setNavigationBar"},setNavigationBarColor:{alias:"setNavigationBar"},saveImageToPhotosAlbum:{alias:"saveImage",options:{change:[{old:"filePath",new:"url"}]}},previewImage:{options:{set:[{key:"current",value:function(e){return e.urls.indexOf(e.current||e.urls[0])}}]}},getFileInfo:{options:{change:[{old:"filePath",new:"apFilePath"}]}},getSavedFileInfo:{options:{change:[{old:"filePath",new:"apFilePath"}]}},removeSavedFile:{options:{change:[{old:"filePath",new:"apFilePath"}]}},saveFile:{options:{change:[{old:"tempFilePath",new:"apFilePath"}]}},openLocation:{options:{set:[{key:"latitude",value:function(e){return String(e.latitude)}},{key:"longitude",value:function(e){return String(e.longitude)}}]}},uploadFile:{options:{change:[{old:"name",new:"fileName"}]}},getClipboardData:{alias:"getClipboard"},setClipboardData:{alias:"setClipboard",options:{change:[{old:"data",new:"text"}]}},makePhoneCall:{options:{change:[{old:"phoneNumber",new:"number"}]}},scanCode:{alias:"scan",options:{change:[{old:"onlyFromCamera",new:"hideAlbum"}],set:[{key:"type",value:function(e){return e.scanType&&e.scanType[0].slice(0,-4)||"qr"}}]}},setScreenBrightness:{options:{change:[{old:"value",new:"brightness"}]}}},y=my.canIUse("request")?my.request:my.httpRequest,w={MAX_REQUEST:5,queue:[],request:function(e){return this.push(e),this.run()},push:function(e){this.queue.push(e)},run:function(){var e=arguments,t=this;if(this.queue.length&&this.queue.length<=this.MAX_REQUEST){var n=this.queue.shift(),r=n.complete;return n.complete=function(){r&&r.apply(n,c(e)),t.run()},y(n)}}};function O(e){return k(e.requestParams)}var j=new g(O);function k(e){e=e||{},"string"===typeof e&&(e={url:e});var t={"content-type":"application/json"};if(e.headers=t,e.header){for(var n in e.header){var r=n.toLocaleLowerCase();e.headers[r]=e.header[n]}delete e.header}var o,i=e.success,a=e.fail,c=e.complete,u=new Promise((function(t,n){e.success=function(e){e.statusCode=e.status,delete e.status,e.header=e.headers,delete e.headers,i&&i(e),t(e)},e.fail=function(e){a&&a(e),n(e)},e.complete=function(e){c&&c(e)},o=w.request(e)}));return u.abort=function(e){return e&&e(),o&&o.abort(),u},u}function S(e){var t=Object.assign({},p,h,v),n="__preload_",r="$preloadComponent";Object.keys(t).forEach((function(t){p[t]||h[t]?e[t]=function(){if(t in my){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];if("getStorageSync"===t){var o=n[0];return null!=o?my[t]({key:o}).data||my[t]({key:o}).APDataStorage||"":console.log("getStorageSync \u4f20\u5165\u53c2\u6570\u9519\u8bef")}if("setStorageSync"===t){var i=n[0],a=n[1];return null!=i?my[t]({key:i,data:a}):console.log("setStorageSync \u4f20\u5165\u53c2\u6570\u9519\u8bef")}if("removeStorageSync"===t){var c=n[0];return null!=c?my[t]({key:c}):console.log("removeStorageSync \u4f20\u5165\u53c2\u6570\u9519\u8bef")}if("createSelectorQuery"===t){var u=my[t]();return u["in"]=function(){return u},u}var s=n.length,l=n.concat(),d=l[s-1];return d&&d.isTaroComponent&&d.$scope&&l.splice(s-1,1,d.$scope),my[t].apply(my,l)}console.warn("\u652f\u4ed8\u5b9d\u5c0f\u7a0b\u5e8f\u6682\u4e0d\u652f\u6301 ".concat(t))}:e[t]=function(e){for(var a=arguments.length,c=new Array(a>1?a-1:0),u=1;u<a;u++)c[u-1]=arguments[u];var s=E(t,e||{}),l=s.api;e=s.options;var d=null,f=Object.assign({},e);if(l in my){var h;if("string"===typeof e)return c.length?(h=my)[l].apply(h,[e].concat(c)):my[l](e);if("navigateTo"===t||"redirectTo"===t||"switchTab"===t){var p=f.url?f.url.replace(/^\//,""):"";p.indexOf("?")>-1&&(p=p.split("?")[0]);var v=(0,o.cacheDataGet)(p);if(v){var m=new v;if(m.componentWillPreload){var g=(0,i.getUniqueKey)(),b=f.url.indexOf("?"),y=b>-1,w=y?f.url.substring(b+1,f.url.length):"",O=(0,i.queryToJson)(w);f.url+=(y?"&":"?")+"".concat(n,"=").concat(g),(0,o.cacheDataSet)(g,m.componentWillPreload(O)),(0,o.cacheDataSet)(r,m)}}}var j=new Promise((function(t,n){var r;(["fail","success","complete"].forEach((function(r){f[r]=function(o){"success"===r&&("saveFile"===l?o.savedFilePath=o.apFilePath:"downloadFile"===l?o.tempFilePath=o.apFilePath:"chooseImage"===l?o.tempFilePaths=o.apFilePaths:"getClipboard"===l?o.data=o.text:"scan"===l?o.result=o.code:"getScreenBrightness"===l&&(o.value=o.brightness,delete o.brightness)),e[r]&&e[r](o),"success"===r?t(o):"fail"===r&&n(o)}})),c.length)?d=(r=my)[l].apply(r,[f].concat(c)):d=my[l](f)}));return"uploadFile"!==l&&"downloadFile"!==l||(j.progress=function(e){return d&&d.onProgressUpdate(e),j},j.abort=function(e){return e&&e(),d&&d.abort(),j}),j}console.warn("\u652f\u4ed8\u5b9d\u5c0f\u7a0b\u5e8f\u6682\u4e0d\u652f\u6301 ".concat(l))}}))}function T(e){var t=this.config||{},n=t.designWidth,r=void 0===n?750:n,o=t.deviceRatio,i=void 0===o?{640:1.17,750:1,828:.905}:o;if(!(r in i))throw new Error("deviceRatio \u914d\u7f6e\u4e2d\u4e0d\u5b58\u5728 ".concat(r," \u7684\u8bbe\u7f6e\uff01"));return parseInt(e,10)*i[r]+"rpx"}function E(e,t){var n=e;return"showModal"===e?(t.cancelButtonText=t.cancelText,t.confirmButtonText=t.confirmText||"\u786e\u5b9a",n="confirm",!1===t.showCancel&&(t.buttonText=t.confirmText||"\u786e\u5b9a",n="alert")):Object.keys(b).forEach((function(r){var o=b[r];if(e===r&&(o.alias&&(n=o.alias),o.options)){var i=o.options.change,a=o.options.set;i&&i.forEach((function(e){t[e["new"]]=t[e.old]})),a&&a.forEach((function(e){t[e.key]="function"===typeof e.value?e.value(t):e.value}))}})),{api:n,options:t}}function C(e){S(e),e.request=j.request.bind(j),e.addInterceptor=j.addInterceptor.bind(j),e.cleanInterceptors=j.cleanInterceptors.bind(j),e.getCurrentPages=getCurrentPages,e.getApp=getApp,e.initPxTransform=m.bind(e),e.pxTransform=T.bind(e)}},312:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.cacheDataSet=o,t.cacheDataGet=i,t.cacheDataHas=a;var r={};function o(e,t){r[e]=t}function i(e,t){var n=r[e];return t&&delete r[e],n}function a(e){return e in r}},313:function(e,t,n){"use strict";function r(e){for(var t,n,r,o=decodeURIComponent,i=e.split("&"),a={},c=0,u=i.length;c<u;++c)if(r=i[c],r.length){var s=r.indexOf("=");s<0?(t=o(r),n=""):(t=o(r.slice(0,s)),n=o(r.slice(s+1))),"string"===typeof a[t]&&(a[t]=[a[t]]),Array.isArray(a[t])?a[t].push(n):a[t]=n}return a}Object.defineProperty(t,"__esModule",{value:!0}),t.queryToJson=r,t.getUniqueKey=a;var o=1,i=(new Date).getTime().toString();function a(){return i+o++}},428:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(9);Component(r.createRecursiveComponentConfig("custom-wrapper"))},429:function(e,t,n){"use strict";(function(e){n.d(t,"a",(function(){return a})),n.d(t,"b",(function(){return i})),n.d(t,"c",(function(){return o}));var r=n(13);function o(e){return"undefined"===typeof e}function i(e){return null!==e&&"object"===Object(r["a"])(e)}function a(e){return"function"===typeof e}Array.isArray}).call(this,n(26))},58:function(e,t,n){"use strict";n.d(t,"b",(function(){return k}));var r=n(16),o=n(17),i=n(260),a=n.n(i),c=n(76),u=n(9),s=n(11);function l(e){return"o"===e[0]&&"n"===e[1]}var d=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord/i;function f(e,t,n){var r;for(r in t)r in n||v(e,r,null,t[r]);var o=e instanceof u["FormElement"];for(r in n)(t[r]!==n[r]||o&&"value"===r)&&v(e,r,n[r],t[r])}function h(e,t,n,r){var o=t.endsWith("Capture"),i=t.toLowerCase().slice(2);o&&(i=i.slice(0,-7));var a=Object(s["c"])(Object(s["t"])(e.tagName.toLowerCase()));"click"===i&&a in s["g"]&&(i="tap"),Object(s["j"])(n)?(r||e.addEventListener(i,n,o),"regionchange"===i?(e.__handlers.begin[0]=n,e.__handlers.end[0]=n):e.__handlers[i][0]=n):e.removeEventListener(i,r)}function p(e,t,n){"-"===t[0]&&e.setProperty(t,n.toString()),e[t]=Object(s["k"])(n)&&!1===d.test(t)?n+"px":null==n?"":n}function v(e,t,n,r){var o,i,a,c;if(t="className"===t?"class":t,"key"===t||"children"===t||"ref"===t);else if("style"===t){var u=e.style;if(Object(s["m"])(n))u.cssText=n;else{if(Object(s["m"])(r)&&(u.cssText="",r=null),Object(s["l"])(r))for(var d in r)n&&d in n||p(u,d,"");if(Object(s["l"])(n))for(var f in n)r&&n[f]===r[f]||p(u,f,n[f])}}else if(l(t))h(e,t,n,r);else if("dangerouslySetInnerHTML"===t){var v=null!==(i=null===(o=n)||void 0===o?void 0:o.__html)&&void 0!==i?i:"",m=null!==(c=null===(a=r)||void 0===a?void 0:a.__html)&&void 0!==c?c:"";(v||m)&&m!==v&&(e.innerHTML=v)}else Object(s["j"])(n)||(null==n?e.removeAttribute(t):e.setAttribute(t,n))}var m=c["unstable_now"];function g(){return!1}var b={createInstance:function(e){return u["document"].createElement(e)},createTextInstance:function(e){return u["document"].createTextNode(e)},getPublicInstance:function(e){return e},getRootHostContext:function(){return{}},getChildHostContext:function(){return{}},appendChild:function(e,t){e.appendChild(t)},appendInitialChild:function(e,t){e.appendChild(t)},appendChildToContainer:function(e,t){e.appendChild(t)},removeChild:function(e,t){e.removeChild(t)},removeChildFromContainer:function(e,t){e.removeChild(t)},insertBefore:function(e,t,n){e.insertBefore(t,n)},insertInContainerBefore:function(e,t,n){e.insertBefore(t,n)},commitTextUpdate:function(e,t,n){e.nodeValue=n},finalizeInitialChildren:function(e,t,n){return f(e,{},n),!1},prepareUpdate:function(){return s["a"]},commitUpdate:function(e,t,n,r,o){f(e,r,o)},hideInstance:function(e){var t=e.style;t.setProperty("display","none")},unhideInstance:function(e,t){var n=t.style,r=(null===n||void 0===n?void 0:n.hasOwnProperty("display"))?n.display:null;r=null==r||"boolean"===typeof r||""===r?"":(""+r).trim(),e.style["display"]=r},clearContainer:function(e){e.childNodes.length>0&&(e.textContent="")},queueMicrotask:"undefined"!==typeof Promise?function(e){return Promise.resolve(null).then(e).catch((function(e){setTimeout((function(){throw e}))}))}:setTimeout,shouldSetTextContent:g,prepareForCommit:function(){return null},resetAfterCommit:s["q"],commitMount:s["q"],now:m,cancelTimeout:clearTimeout,scheduleTimeout:setTimeout,preparePortalMount:s["q"],noTimeout:-1,supportsMutation:!0,supportsPersistence:!1,isPrimaryRenderer:!0,supportsHydration:!1},y=a()(b),w=new WeakMap,O=function(){function e(t,n){Object(r["a"])(this,e),this.renderer=t,this.internalRoot=t.createContainer(n,0,!1,null)}return Object(o["a"])(e,[{key:"render",value:function(e,t){return this.renderer.updateContainer(e,this.internalRoot,null,t),this.renderer.getPublicRootInstance(this.internalRoot)}},{key:"unmount",value:function(e){this.renderer.updateContainer(null,this.internalRoot,null,e)}}]),e}();function j(e,t,n){var r=w.get(t);if(null!=r)return r.render(e,n);var o=new O(y,t);return w.set(t,o),o.render(e,n)}var k=y.batchedUpdates;function S(e){Object(s["f"])(e&&[1,8,9,11].includes(e.nodeType),"unmountComponentAtNode(...): Target container is not a DOM element.");var t=w.get(e);return!!t&&(k((function(){t.unmount((function(){w.delete(e)}))}),null),!0)}function T(e){if(null==e)return null;var t=e.nodeType;return 1===t||3===t?e:y.findHostInstance(e)}var E="function"===typeof Symbol&&Symbol.for?Symbol.for("react.portal"):60106;function C(e,t,n){return{$$typeof:E,key:null==n?null:String(n),children:e,containerInfo:t,implementation:null}}var _={render:j,unstable_batchedUpdates:k,unmountComponentAtNode:S,findDOMNode:T,createPortal:C};t["a"]=_},9:function(e,t,n){"use strict";n.r(t),function(e,r,o,i,a,c){n.d(t,"Current",(function(){return jo})),n.d(t,"ElementNames",(function(){return we})),n.d(t,"Events",(function(){return So})),n.d(t,"FormElement",(function(){return Wn})),n.d(t,"SERVICE_IDENTIFIER",(function(){return C})),n.d(t,"SVGElement",(function(){return zn})),n.d(t,"Style",(function(){return ze})),n.d(t,"TaroElement",(function(){return Ye})),n.d(t,"TaroEvent",(function(){return to})),n.d(t,"TaroNode",(function(){return ke})),n.d(t,"TaroRootElement",(function(){return Hn})),n.d(t,"TaroText",(function(){return Se})),n.d(t,"cancelAnimationFrame",(function(){return bo})),n.d(t,"connectReactPage",(function(){return Go})),n.d(t,"connectVuePage",(function(){return ti})),n.d(t,"container",(function(){return eo})),n.d(t,"createComponentConfig",(function(){return qo})),n.d(t,"createDocument",(function(){return uo})),n.d(t,"createEvent",(function(){return no})),n.d(t,"createNativeComponentConfig",(function(){return ei})),n.d(t,"createPageConfig",(function(){return Mo})),n.d(t,"createReactApp",(function(){return Yo})),n.d(t,"createRecursiveComponentConfig",(function(){return Uo})),n.d(t,"createVue3App",(function(){return ai})),n.d(t,"createVueApp",(function(){return ri})),n.d(t,"document",(function(){return lo})),n.d(t,"eventCenter",(function(){return Eo})),n.d(t,"getComputedStyle",(function(){return yo})),n.d(t,"getCurrentInstance",(function(){return ko})),n.d(t,"hydrate",(function(){return ye})),n.d(t,"injectPageInstance",(function(){return Ao})),n.d(t,"navigator",(function(){return vo})),n.d(t,"nextTick",(function(){return Ti})),n.d(t,"now",(function(){return so})),n.d(t,"options",(function(){return Mn})),n.d(t,"processPluginHooks",(function(){return Yr})),n.d(t,"requestAnimationFrame",(function(){return go})),n.d(t,"stringify",(function(){return Bo})),n.d(t,"useAddToFavorites",(function(){return wi})),n.d(t,"useDidHide",(function(){return si})),n.d(t,"useDidShow",(function(){return ui})),n.d(t,"useOptionMenuClick",(function(){return gi})),n.d(t,"usePageScroll",(function(){return fi})),n.d(t,"usePullDownRefresh",(function(){return li})),n.d(t,"usePullIntercept",(function(){return bi})),n.d(t,"useReachBottom",(function(){return di})),n.d(t,"useReady",(function(){return Oi})),n.d(t,"useResize",(function(){return hi})),n.d(t,"useRouter",(function(){return ji})),n.d(t,"useScope",(function(){return ki})),n.d(t,"useShareAppMessage",(function(){return pi})),n.d(t,"useShareTimeline",(function(){return yi})),n.d(t,"useTabItemTap",(function(){return vi})),n.d(t,"useTitleClick",(function(){return mi})),n.d(t,"window",(function(){return wo}));n(5),n(8);var u=n(7),s=n(256),l=n(15),d=n(27),f=n(21),h=n(263),p=n(257),v=n(24),m=n(33),g=n(23),b=n(18),y=n(16),w=n(17),O=n(13),j=n(12),k=n(11);function S(e,t,n,r){var o,i=arguments.length,a=i<3?t:null===r?r=Object.getOwnPropertyDescriptor(t,n):r;if("object"===("undefined"===typeof Reflect?"undefined":Object(O["a"])(Reflect))&&"function"===typeof Reflect.decorate)a=Reflect.decorate(e,t,n,r);else for(var c=e.length-1;c>=0;c--)(o=e[c])&&(a=(i<3?o(a):i>3?o(t,n,a):o(t,n))||a);return i>3&&a&&Object.defineProperty(t,n,a),a}function T(e,t){return function(n,r){t(n,r,e)}}function E(e,t){if("object"===("undefined"===typeof Reflect?"undefined":Object(O["a"])(Reflect))&&"function"===typeof Reflect.metadata)return Reflect.metadata(e,t)}(function(t){(function(e){var n=r(t);function r(e,t){return function(n,r){"function"!==typeof e[n]&&Object.defineProperty(e,n,{configurable:!0,writable:!0,value:r}),t&&t(n,r)}}e(n)})((function(t){var n=Object.prototype.hasOwnProperty,r="function"===typeof Symbol,o=r&&"undefined"!==typeof Symbol.toPrimitive?Symbol.toPrimitive:"@@toPrimitive",i=r&&"undefined"!==typeof Symbol.iterator?Symbol.iterator:"@@iterator",a="function"===typeof Object.create,c={__proto__:[]}instanceof Array,u=!a&&!c,s={create:a?function(){return ae(Object.create(null))}:c?function(){return ae({__proto__:null})}:function(){return ae({})},has:u?function(e,t){return n.call(e,t)}:function(e,t){return t in e},get:u?function(e,t){return n.call(e,t)?e[t]:void 0}:function(e,t){return e[t]}},l=Object.getPrototypeOf(Function),d="object"===("undefined"===typeof e?"undefined":Object(O["a"])(e))&&e.env&&"true"===e.env["REFLECT_METADATA_USE_MAP_POLYFILL"],f=d||"function"!==typeof Map||"function"!==typeof Map.prototype.entries?re():Map,h=d||"function"!==typeof Set||"function"!==typeof Set.prototype.entries?oe():Set,p=d||"function"!==typeof WeakMap?ie():WeakMap,v=new p;function m(e,t,n,r){if(D(n)){if(!G(e))throw new TypeError;if(!Q(t))throw new TypeError;return C(e,t)}if(!G(e))throw new TypeError;if(!U(t))throw new TypeError;if(!U(r)&&!D(r)&&!M(r))throw new TypeError;return M(r)&&(r=void 0),n=V(n),_(e,t,n,r)}function g(e,t){function n(n,r){if(!U(n))throw new TypeError;if(!D(r)&&!Y(r))throw new TypeError;B(e,t,n,r)}return n}function b(e,t,n,r){if(!U(n))throw new TypeError;return D(r)||(r=V(r)),B(e,t,n,r)}function y(e,t,n){if(!U(t))throw new TypeError;return D(n)||(n=V(n)),A(e,t,n)}function w(e,t,n){if(!U(t))throw new TypeError;return D(n)||(n=V(n)),x(e,t,n)}function j(e,t,n){if(!U(t))throw new TypeError;return D(n)||(n=V(n)),I(e,t,n)}function k(e,t,n){if(!U(t))throw new TypeError;return D(n)||(n=V(n)),N(e,t,n)}function S(e,t){if(!U(e))throw new TypeError;return D(t)||(t=V(t)),F(e,t)}function T(e,t){if(!U(e))throw new TypeError;return D(t)||(t=V(t)),L(e,t)}function E(e,t,n){if(!U(t))throw new TypeError;D(n)||(n=V(n));var r=P(t,n,!1);if(D(r))return!1;if(!r.delete(e))return!1;if(r.size>0)return!0;var o=v.get(t);return o.delete(n),o.size>0||v.delete(t),!0}function C(e,t){for(var n=e.length-1;n>=0;--n){var r=e[n],o=r(t);if(!D(o)&&!M(o)){if(!Q(o))throw new TypeError;t=o}}return t}function _(e,t,n,r){for(var o=e.length-1;o>=0;--o){var i=e[o],a=i(t,n,r);if(!D(a)&&!M(a)){if(!U(a))throw new TypeError;r=a}}return r}function P(e,t,n){var r=v.get(e);if(D(r)){if(!n)return;r=new f,v.set(e,r)}var o=r.get(t);if(D(o)){if(!n)return;o=new f,r.set(t,o)}return o}function A(e,t,n){var r=x(e,t,n);if(r)return!0;var o=ne(t);return!M(o)&&A(e,o,n)}function x(e,t,n){var r=P(t,n,!1);return!D(r)&&W(r.has(e))}function I(e,t,n){var r=x(e,t,n);if(r)return N(e,t,n);var o=ne(t);return M(o)?void 0:I(e,o,n)}function N(e,t,n){var r=P(t,n,!1);if(!D(r))return r.get(e)}function B(e,t,n,r){var o=P(n,r,!0);o.set(e,t)}function F(e,t){var n=L(e,t),r=ne(e);if(null===r)return n;var o=F(r,t);if(o.length<=0)return n;if(n.length<=0)return o;for(var i=new h,a=[],c=0,u=n;c<u.length;c++){var s=u[c],l=i.has(s);l||(i.add(s),a.push(s))}for(var d=0,f=o;d<f.length;d++){s=f[d],l=i.has(s);l||(i.add(s),a.push(s))}return a}function L(e,t){var n=[],r=P(e,t,!1);if(D(r))return n;var o=r.keys(),i=X(o),a=0;while(1){var c=ee(i);if(!c)return n.length=a,n;var u=Z(c);try{n[a]=u}catch(e){try{te(i)}finally{throw e}}a++}}function R(e){if(null===e)return 1;switch(Object(O["a"])(e)){case"undefined":return 0;case"boolean":return 2;case"string":return 3;case"symbol":return 4;case"number":return 5;case"object":return null===e?1:6;default:return 6}}function D(e){return void 0===e}function M(e){return null===e}function q(e){return"symbol"===Object(O["a"])(e)}function U(e){return"object"===Object(O["a"])(e)?null!==e:"function"===typeof e}function $(e,t){switch(R(e)){case 0:return e;case 1:return e;case 2:return e;case 3:return e;case 4:return e;case 5:return e}var n=3===t?"string":5===t?"number":"default",r=J(e,o);if(void 0!==r){var i=r.call(e,n);if(U(i))throw new TypeError;return i}return H(e,"default"===n?"number":n)}function H(e,t){if("string"===t){var n=e.toString;if(K(n)){var r=n.call(e);if(!U(r))return r}var o=e.valueOf;if(K(o)){r=o.call(e);if(!U(r))return r}}else{o=e.valueOf;if(K(o)){r=o.call(e);if(!U(r))return r}var i=e.toString;if(K(i)){r=i.call(e);if(!U(r))return r}}throw new TypeError}function W(e){return!!e}function z(e){return""+e}function V(e){var t=$(e,3);return q(t)?t:z(t)}function G(e){return Array.isArray?Array.isArray(e):e instanceof Object?e instanceof Array:"[object Array]"===Object.prototype.toString.call(e)}function K(e){return"function"===typeof e}function Q(e){return"function"===typeof e}function Y(e){switch(R(e)){case 3:return!0;case 4:return!0;default:return!1}}function J(e,t){var n=e[t];if(void 0!==n&&null!==n){if(!K(n))throw new TypeError;return n}}function X(e){var t=J(e,i);if(!K(t))throw new TypeError;var n=t.call(e);if(!U(n))throw new TypeError;return n}function Z(e){return e.value}function ee(e){var t=e.next();return!t.done&&t}function te(e){var t=e["return"];t&&t.call(e)}function ne(e){var t=Object.getPrototypeOf(e);if("function"!==typeof e||e===l)return t;if(t!==l)return t;var n=e.prototype,r=n&&Object.getPrototypeOf(n);if(null==r||r===Object.prototype)return t;var o=r.constructor;return"function"!==typeof o||o===e?t:o}function re(){var e={},t=[],n=function(){function e(e,t,n){this._index=0,this._keys=e,this._values=t,this._selector=n}return e.prototype["@@iterator"]=function(){return this},e.prototype[i]=function(){return this},e.prototype.next=function(){var e=this._index;if(e>=0&&e<this._keys.length){var n=this._selector(this._keys[e],this._values[e]);return e+1>=this._keys.length?(this._index=-1,this._keys=t,this._values=t):this._index++,{value:n,done:!1}}return{value:void 0,done:!0}},e.prototype.throw=function(e){throw this._index>=0&&(this._index=-1,this._keys=t,this._values=t),e},e.prototype.return=function(e){return this._index>=0&&(this._index=-1,this._keys=t,this._values=t),{value:e,done:!0}},e}();return function(){function t(){this._keys=[],this._values=[],this._cacheKey=e,this._cacheIndex=-2}return Object.defineProperty(t.prototype,"size",{get:function(){return this._keys.length},enumerable:!0,configurable:!0}),t.prototype.has=function(e){return this._find(e,!1)>=0},t.prototype.get=function(e){var t=this._find(e,!1);return t>=0?this._values[t]:void 0},t.prototype.set=function(e,t){var n=this._find(e,!0);return this._values[n]=t,this},t.prototype.delete=function(t){var n=this._find(t,!1);if(n>=0){for(var r=this._keys.length,o=n+1;o<r;o++)this._keys[o-1]=this._keys[o],this._values[o-1]=this._values[o];return this._keys.length--,this._values.length--,t===this._cacheKey&&(this._cacheKey=e,this._cacheIndex=-2),!0}return!1},t.prototype.clear=function(){this._keys.length=0,this._values.length=0,this._cacheKey=e,this._cacheIndex=-2},t.prototype.keys=function(){return new n(this._keys,this._values,r)},t.prototype.values=function(){return new n(this._keys,this._values,o)},t.prototype.entries=function(){return new n(this._keys,this._values,a)},t.prototype["@@iterator"]=function(){return this.entries()},t.prototype[i]=function(){return this.entries()},t.prototype._find=function(e,t){return this._cacheKey!==e&&(this._cacheIndex=this._keys.indexOf(this._cacheKey=e)),this._cacheIndex<0&&t&&(this._cacheIndex=this._keys.length,this._keys.push(e),this._values.push(void 0)),this._cacheIndex},t}();function r(e,t){return e}function o(e,t){return t}function a(e,t){return[e,t]}}function oe(){return function(){function e(){this._map=new f}return Object.defineProperty(e.prototype,"size",{get:function(){return this._map.size},enumerable:!0,configurable:!0}),e.prototype.has=function(e){return this._map.has(e)},e.prototype.add=function(e){return this._map.set(e,e),this},e.prototype.delete=function(e){return this._map.delete(e)},e.prototype.clear=function(){this._map.clear()},e.prototype.keys=function(){return this._map.keys()},e.prototype.values=function(){return this._map.values()},e.prototype.entries=function(){return this._map.entries()},e.prototype["@@iterator"]=function(){return this.keys()},e.prototype[i]=function(){return this.keys()},e}()}function ie(){var e=16,t=s.create(),r=o();return function(){function e(){this._key=o()}return e.prototype.has=function(e){var t=i(e,!1);return void 0!==t&&s.has(t,this._key)},e.prototype.get=function(e){var t=i(e,!1);return void 0!==t?s.get(t,this._key):void 0},e.prototype.set=function(e,t){var n=i(e,!0);return n[this._key]=t,this},e.prototype.delete=function(e){var t=i(e,!1);return void 0!==t&&delete t[this._key]},e.prototype.clear=function(){this._key=o()},e}();function o(){var e;do{e="@@WeakMap@@"+u()}while(s.has(t,e));return t[e]=!0,e}function i(e,t){if(!n.call(e,r)){if(!t)return;Object.defineProperty(e,r,{value:s.create()})}return e[r]}function a(e,t){for(var n=0;n<t;++n)e[n]=255*Math.random()|0;return e}function c(e){return"function"===typeof Uint8Array?"undefined"!==typeof crypto?crypto.getRandomValues(new Uint8Array(e)):"undefined"!==typeof msCrypto?msCrypto.getRandomValues(new Uint8Array(e)):a(new Uint8Array(e),e):a(new Array(e),e)}function u(){var t=c(e);t[6]=79&t[6]|64,t[8]=191&t[8]|128;for(var n="",r=0;r<e;++r){var o=t[r];4!==r&&6!==r&&8!==r||(n+="-"),o<16&&(n+="0"),n+=o.toString(16).toLowerCase()}return n}}function ae(e){return e.__=void 0,delete e.__,e}t("decorate",m),t("metadata",g),t("defineMetadata",b),t("hasMetadata",y),t("hasOwnMetadata",w),t("getMetadata",j),t("getOwnMetadata",k),t("getMetadataKeys",S),t("getOwnMetadataKeys",T),t("deleteMetadata",E)}))})(Reflect||(Reflect={}));var C={TaroElement:"TaroElement",TaroElementFactory:"Factory<TaroElement>",TaroText:"TaroText",TaroTextFactory:"Factory<TaroText>",TaroNodeImpl:"TaroNodeImpl",TaroElementImpl:"TaroElementImpl",Hooks:"hooks",onRemoveAttribute:"onRemoveAttribute",getLifecycle:"getLifecycle",getPathIndex:"getPathIndex",getEventCenter:"getEventCenter",isBubbleEvents:"isBubbleEvents",getSpecialNodes:"getSpecialNodes",eventCenter:"eventCenter",modifyMpEvent:"modifyMpEvent",modifyTaroEvent:"modifyTaroEvent",batchedEventUpdates:"batchedEventUpdates",mergePageInstance:"mergePageInstance",createPullDownComponent:"createPullDownComponent",getDOMNode:"getDOMNode",initNativeApi:"initNativeApi",modifyHydrateData:"modifyHydrateData",modifySetAttrPayload:"modifySetAttrPayload",modifyRmAttrPayload:"modifyRmAttrPayload",onAddEvent:"onAddEvent",patchElement:"patchElement"},_="taro-app",P="\u5c0f\u7a0b\u5e8f setData",A="\u9875\u9762\u521d\u59cb\u5316",x="root",I="html",N="head",B="body",F="app",L="container",R="#document",D="document-fragment",M="id",q="uid",U="class",$="style",H="focus",W="view",z="static-view",V="pure-view",G="props",K="dataset",Q="object",Y="value",J="input",X="change",Z="custom-wrapper",ee="target",te="currentTarget",ne="type",re="confirm",oe="timeStamp",ie="keyCode",ae="touchmove",ce="Date",ue="catchMove",se="catch-view",le="comment",de=function(){var e=0;return function(){return(e++).toString()}};function fe(e){return 1===e.nodeType}function he(e){return 3===e.nodeType}function pe(e){return e.nodeName===le}function ve(e){var t=Object.keys(e.props).find((function(e){return!(/^(class|style|id)$/.test(e)||e.startsWith("data-"))}));return Boolean(t)}function me(e,t){var n,r=!1;while((null===e||void 0===e?void 0:e.parentElement)&&e.parentElement._path!==x){if(null===(n=e.parentElement.__handlers[t])||void 0===n?void 0:n.length){r=!0;break}e=e.parentElement}return r}function ge(e){switch(e){case $:return"st";case M:return q;case U:return"cl";default:return e}}var be=function(){function e(t){Object(y["a"])(this,e),this.__handlers={},this.hooks=t}return Object(w["a"])(e,[{key:"addEventListener",value:function(e,t,n){var r,o;if(null===(o=(r=this.hooks).onAddEvent)||void 0===o||o.call(r,e,t,n,this),"regionchange"===e)return this.addEventListener("begin",t,n),void this.addEventListener("end",t,n);e=e.toLowerCase();var i=this.__handlers[e],a=(Boolean(n),!1);if(Object(k["l"])(n)&&(Boolean(n.capture),a=Boolean(n.once)),a){var c=function n(){t.apply(this,arguments),this.removeEventListener(e,n)};this.addEventListener(e,c,Object.assign(Object.assign({},n),{once:!1}))}else Object(k["h"])(i)?i.push(t):this.__handlers[e]=[t]}},{key:"removeEventListener",value:function(e,t){if(e=e.toLowerCase(),null!=t){var n=this.__handlers[e];if(Object(k["h"])(n)){var r=n.indexOf(t);n.splice(r,1)}}}},{key:"isAnyEventBinded",value:function(){var e=this.__handlers,t=Object.keys(e).find((function(t){return e[t].length}));return Boolean(t)}}]),e}();function ye(e){var t,n,r=e.nodeName;if(he(e))return Object(b["a"])(Object(b["a"])({},"v",e.nodeValue),"nn",r);var o=Object(b["a"])(Object(b["a"])({},"nn",r),"uid",e.uid),i=e.props,a=e.hooks.getSpecialNodes();for(var c in!e.isAnyEventBinded()&&a.indexOf(r)>-1&&(o["nn"]="static-".concat(r),r!==W||ve(e)||(o["nn"]=V)),i){var u=Object(k["t"])(c);c.startsWith("data-")||c===U||c===$||c===M||u===ue||(o[u]=i[c]),r===W&&u===ue&&!1!==i[c]&&(o["nn"]=se)}var s=e.childNodes;return s=s.filter((function(e){return!pe(e)})),s.length>0?o["cn"]=s.map(ye):o["cn"]=[],""!==e.className&&(o["cl"]=e.className),""!==e.cssText&&"swiper-item"!==r&&(o["st"]=e.cssText),null===(n=(t=e.hooks).modifyHydrateData)||void 0===n||n.call(t,o),o}be=S([Object(j["d"])(),T(0,Object(j["c"])(C.Hooks)),E("design:paramtypes",[Object])],be);var we,Oe=new Map;(function(e){e["Element"]="Element",e["Document"]="Document",e["RootElement"]="RootElement",e["FormElement"]="FormElement"})(we||(we={}));var je=de(),ke=function(e){function t(e,n,r){var o;return Object(y["a"])(this,t),o=Object(v["a"])(this,t,[r]),o.parentNode=null,o.childNodes=[],o.hydrate=function(e){return function(){return ye(e)}},e.bind(Object(m["a"])(o)),o._getElement=n,o.uid="_n_".concat(je()),Oe.set(o.uid,Object(m["a"])(o)),o}return Object(g["a"])(t,e),Object(w["a"])(t,[{key:"_empty",value:function(){while(this.childNodes.length>0){var e=this.childNodes[0];e.parentNode=null,Oe.delete(e.uid),this.childNodes.shift()}}},{key:"_root",get:function(){var e;return(null===(e=this.parentNode)||void 0===e?void 0:e._root)||null}},{key:"findIndex",value:function(e){var t=this.childNodes.indexOf(e);return Object(k["f"])(-1!==t,"The node to be replaced is not a child of this node."),t}},{key:"_path",get:function(){var e=this.parentNode;if(e){var t=e.childNodes.filter((function(e){return!pe(e)})),n=t.indexOf(this),r=this.hooks.getPathIndex(n);return"".concat(e._path,".","cn",".").concat(r)}return""}},{key:"nextSibling",get:function(){var e=this.parentNode;return(null===e||void 0===e?void 0:e.childNodes[e.findIndex(this)+1])||null}},{key:"previousSibling",get:function(){var e=this.parentNode;return(null===e||void 0===e?void 0:e.childNodes[e.findIndex(this)-1])||null}},{key:"parentElement",get:function(){var e=this.parentNode;return 1===(null===e||void 0===e?void 0:e.nodeType)?e:null}},{key:"firstChild",get:function(){return this.childNodes[0]||null}},{key:"lastChild",get:function(){var e=this.childNodes;return e[e.length-1]||null}},{key:"textContent",set:function(e){if(this._empty(),""===e)this.enqueueUpdate({path:"".concat(this._path,".","cn"),value:function(){return[]}});else{var t=this._getElement(we.Document)();this.appendChild(t.createTextNode(e))}}},{key:"insertBefore",value:function(e,t,n){var r,o=this;if(e.nodeName===D)return e.childNodes.reduceRight((function(e,t){return o.insertBefore(t,e),t}),t),e;if(e.remove(),e.parentNode=this,t){var i=this.findIndex(t);this.childNodes.splice(i,0,e),r=n?{path:e._path,value:this.hydrate(e)}:{path:"".concat(this._path,".","cn"),value:function(){var e=o.childNodes.filter((function(e){return!pe(e)}));return e.map(ye)}}}else this.childNodes.push(e),r={path:e._path,value:this.hydrate(e)};return this.enqueueUpdate(r),Oe.has(e.uid)||Oe.set(e.uid,e),e}},{key:"appendChild",value:function(e){this.insertBefore(e)}},{key:"replaceChild",value:function(e,t){if(t.parentNode===this)return this.insertBefore(e,t,!0),t.remove(!0),t}},{key:"removeChild",value:function(e,t){var n=this,r=this.findIndex(e);return this.childNodes.splice(r,1),t||this.enqueueUpdate({path:"".concat(this._path,".","cn"),value:function(){var e=n.childNodes.filter((function(e){return!pe(e)}));return e.map(ye)}}),e.parentNode=null,Oe.delete(e.uid),e}},{key:"remove",value:function(e){var t;null===(t=this.parentNode)||void 0===t||t.removeChild(this,e)}},{key:"hasChildNodes",value:function(){return this.childNodes.length>0}},{key:"enqueueUpdate",value:function(e){var t;null===(t=this._root)||void 0===t||t.enqueueUpdate(e)}},{key:"contains",value:function(e){var t=!1;return this.childNodes.some((function(n){var r=n.uid;if(r===e.uid||r===e.id||n.contains(e))return t=!0,!0})),t}},{key:"ownerDocument",get:function(){var e=this._getElement(we.Document)();return e}}]),t}(be);ke=S([Object(j["d"])(),T(0,Object(j["c"])(C.TaroNodeImpl)),T(1,Object(j["c"])(C.TaroElementFactory)),T(2,Object(j["c"])(C.Hooks)),E("design:paramtypes",[Function,Function,Function])],ke);var Se=function(e){function t(e,n,r){var o;return Object(y["a"])(this,t),o=Object(v["a"])(this,t,[e,n,r]),o.nodeType=3,o.nodeName="#text",o}return Object(g["a"])(t,e),Object(w["a"])(t,[{key:"textContent",get:function(){return this._value},set:function(e){this._value=e,this.enqueueUpdate({path:"".concat(this._path,".","v"),value:e})}},{key:"nodeValue",get:function(){return this._value},set:function(e){this.textContent=e}}]),t}(ke);Se=S([Object(j["d"])(),T(0,Object(j["c"])(C.TaroNodeImpl)),T(1,Object(j["c"])(C.TaroElementFactory)),T(2,Object(j["c"])(C.Hooks)),E("design:paramtypes",[Function,Function,Function])],Se);var Te=["all","appearance","blockOverflow","blockSize","bottom","clear","contain","content","continue","cursor","direction","display","filter","float","gap","height","inset","isolation","left","letterSpacing","lightingColor","markerSide","mixBlendMode","opacity","order","position","quotes","resize","right","rowGap","tabSize","tableLayout","top","userSelect","verticalAlign","visibility","voiceFamily","volume","whiteSpace","widows","width","zIndex","pointerEvents"];function Ee(e,t,n){!n&&Te.push(e),t.forEach((function(t){Te.push(e+t)}))}var Ce="Color",_e="Style",Pe="Width",Ae="Image",xe="Size",Ie=[Ce,_e,Pe],Ne=["FitLength","FitWidth",Ae],Be=[].concat(Ne,["Radius"]),Fe=[].concat(Ie,Ne),Le=["EndRadius","StartRadius"],Re=["Bottom","Left","Right","Top"],De=["End","Start"],Me=["Content","Items","Self"],qe=["BlockSize","Height","InlineSize",Pe],Ue=["After","Before"];function $e(e,t){var n=this[t];e&&this._usedStyleProp.add(t),n!==e&&(this._value[t]=e,this._element.enqueueUpdate({path:"".concat(this._element._path,".","st"),value:this.cssText}))}function He(e){for(var t={},n=function(){var e=Te[r];t[e]={get:function(){return this._value[e]||""},set:function(t){$e.call(this,t,e)}}},r=0;r<Te.length;r++)n();Object.defineProperties(e.prototype,t)}function We(e){return/^--/.test(e)}Ee("borderBlock",Ie),Ee("borderBlockEnd",Ie),Ee("borderBlockStart",Ie),Ee("outline",[].concat(Ie,["Offset"])),Ee("border",[].concat(Ie,["Boundary","Break","Collapse","Radius","Spacing"])),Ee("borderFit",["Length",Pe]),Ee("borderInline",Ie),Ee("borderInlineEnd",Ie),Ee("borderInlineStart",Ie),Ee("borderLeft",Fe),Ee("borderRight",Fe),Ee("borderTop",Fe),Ee("borderBottom",Fe),Ee("textDecoration",[Ce,_e,"Line"]),Ee("textEmphasis",[Ce,_e,"Position"]),Ee("scrollMargin",Re),Ee("scrollPadding",Re),Ee("padding",Re),Ee("margin",[].concat(Re,["Trim"])),Ee("scrollMarginBlock",De),Ee("scrollMarginInline",De),Ee("scrollPaddingBlock",De),Ee("scrollPaddingInline",De),Ee("gridColumn",De),Ee("gridRow",De),Ee("insetBlock",De),Ee("insetInline",De),Ee("marginBlock",De),Ee("marginInline",De),Ee("paddingBlock",De),Ee("paddingInline",De),Ee("pause",Ue),Ee("cue",Ue),Ee("mask",["Clip","Composite",Ae,"Mode","Origin","Position","Repeat",xe,"Type"]),Ee("borderImage",["Outset","Repeat","Slice","Source","Transform",Pe]),Ee("maskBorder",["Mode","Outset","Repeat","Slice","Source",Pe]),Ee("font",["Family","FeatureSettings","Kerning","LanguageOverride","MaxSize","MinSize","OpticalSizing","Palette",xe,"SizeAdjust","Stretch",_e,"Weight","VariationSettings"]),Ee("fontSynthesis",["SmallCaps",_e,"Weight"]),Ee("transform",["Box","Origin",_e]),Ee("background",[Ce,Ae,"Attachment","BlendMode","Clip","Origin","Position","Repeat",xe]),Ee("listStyle",[Ae,"Position","Type"]),Ee("scrollSnap",["Align","Stop","Type"]),Ee("grid",["Area","AutoColumns","AutoFlow","AutoRows"]),Ee("gridTemplate",["Areas","Columns","Rows"]),Ee("overflow",["Block","Inline","Wrap","X","Y"]),Ee("transition",["Delay","Duration","Property","TimingFunction"]),Ee("lineStacking",["Ruby","Shift","Strategy"]),Ee("color",["Adjust","InterpolationFilters","Scheme"]),Ee("textAlign",["All","Last"]),Ee("page",["BreakAfter","BreakBefore","BreakInside"]),Ee("speak",["Header","Numeral","Punctuation"]),Ee("animation",["Delay","Direction","Duration","FillMode","IterationCount","Name","PlayState","TimingFunction"]),Ee("flex",["Basis","Direction","Flow","Grow","Shrink","Wrap"]),Ee("offset",[].concat(Ue,De,["Anchor","Distance","Path","Position","Rotate"])),Ee("fontVariant",["Alternates","Caps","EastAsian","Emoji","Ligatures","Numeric","Position"]),Ee("perspective",["Origin"]),Ee("pitch",["Range"]),Ee("clip",["Path","Rule"]),Ee("flow",["From","Into"]),Ee("align",["Content","Items","Self"],!0),Ee("alignment",["Adjust","Baseline"],!0),Ee("bookmark",["Label","Level","State"],!0),Ee("borderStart",Le,!0),Ee("borderEnd",Le,!0),Ee("borderCorner",["Fit",Ae,"ImageTransform"],!0),Ee("borderTopLeft",Be,!0),Ee("borderTopRight",Be,!0),Ee("borderBottomLeft",Be,!0),Ee("borderBottomRight",Be,!0),Ee("column",["s","Count","Fill","Gap","Rule","RuleColor","RuleStyle","RuleWidth","Span",Pe],!0),Ee("break",[].concat(Ue,["Inside"]),!0),Ee("wrap",[].concat(Ue,["Flow","Inside","Through"]),!0),Ee("justify",Me,!0),Ee("place",Me,!0),Ee("max",[].concat(qe,["Lines"]),!0),Ee("min",qe,!0),Ee("line",["Break","Clamp","Grid","Height","Padding","Snap"],!0),Ee("inline",["BoxAlign",xe,"Sizing"],!0),Ee("text",["CombineUpright","GroupAlign","Height","Indent","Justify","Orientation","Overflow","Shadow","SpaceCollapse","SpaceTrim","Spacing","Transform","UnderlinePosition","Wrap"],!0),Ee("shape",["ImageThreshold","Inside","Margin","Outside"],!0),Ee("word",["Break","Spacing","Wrap"],!0),Ee("nav",["Down","Left","Right","Up"],!0),Ee("object",["Fit","Position"],!0),Ee("box",["DecorationBreak","Shadow","Sizing","Snap"],!0);var ze=function(){function e(t){Object(y["a"])(this,e),this._element=t,this._usedStyleProp=new Set,this._value={}}return Object(w["a"])(e,[{key:"setCssVariables",value:function(e){var t=this;this.hasOwnProperty(e)||Object.defineProperty(this,e,{enumerable:!0,configurable:!0,get:function(){return t._value[e]||""},set:function(n){$e.call(t,n,e)}})}},{key:"cssText",get:function(){var e=this,t="";return this._usedStyleProp.forEach((function(n){var r=e[n];if(r){var o=We(n)?n:Object(k["u"])(n);t+="".concat(o,": ").concat(r,";")}})),t},set:function(e){var t=this;if(null==e&&(e=""),this._usedStyleProp.forEach((function(e){t.removeProperty(e)})),""!==e)for(var n=e.split(";"),r=0;r<n.length;r++){var o=n[r].trim();if(""!==o){var i=o.split(":"),a=Object(p["a"])(i),c=a[0],u=a.slice(1),s=u.join(":");Object(k["n"])(s)||this.setProperty(c.trim(),s.trim())}}}},{key:"setProperty",value:function(e,t){"-"===e[0]?this.setCssVariables(e):e=Object(k["t"])(e),Object(k["n"])(t)||(null===t||""===t?this.removeProperty(e):this[e]=t)}},{key:"removeProperty",value:function(e){if(e=Object(k["t"])(e),!this._usedStyleProp.has(e))return"";var t=this[e];return this[e]="",this._usedStyleProp.delete(e),t}},{key:"getPropertyValue",value:function(e){e=Object(k["t"])(e);var t=this[e];return t||""}}]),e}();function Ve(){return!0}function Ge(e,t){var n=[],r=null!==t&&void 0!==t?t:Ve,o=e;while(o)1===o.nodeType&&r(o)&&n.push(o),o=Ke(o,e);return n}function Ke(e,t){var n=e.firstChild;if(n)return n;var r=e;do{if(r===t)return null;var o=r.nextSibling;if(o)return o;r=r.parentElement}while(r);return null}He(ze);var Qe=function(e){function t(e,n){var r,o;return Object(y["a"])(this,t),o=Object(v["a"])(this,t),e.trim().split(/\s+/).forEach(Object(d["a"])((r=Object(m["a"])(o),Object(f["a"])(t.prototype)),"add",r).bind(Object(m["a"])(o))),o.el=n,o}return Object(g["a"])(t,e),Object(w["a"])(t,[{key:"value",get:function(){return Object(l["a"])(this).join(" ")}},{key:"add",value:function(e){return Object(d["a"])(Object(f["a"])(t.prototype),"add",this).call(this,e),this._update(),this}},{key:"length",get:function(){return this.size}},{key:"remove",value:function(e){Object(d["a"])(Object(f["a"])(t.prototype),"delete",this).call(this,e),this._update()}},{key:"toggle",value:function(e){Object(d["a"])(Object(f["a"])(t.prototype),"has",this).call(this,e)?Object(d["a"])(Object(f["a"])(t.prototype),"delete",this).call(this,e):Object(d["a"])(Object(f["a"])(t.prototype),"add",this).call(this,e),this._update()}},{key:"replace",value:function(e,n){Object(d["a"])(Object(f["a"])(t.prototype),"delete",this).call(this,e),Object(d["a"])(Object(f["a"])(t.prototype),"add",this).call(this,n),this._update()}},{key:"contains",value:function(e){return Object(d["a"])(Object(f["a"])(t.prototype),"has",this).call(this,e)}},{key:"toString",value:function(){return this.value}},{key:"_update",value:function(){this.el.className=this.value}}]),t}(Object(h["a"])(Set)),Ye=function(e){function t(e,n,r,o){var i;return Object(y["a"])(this,t),i=Object(v["a"])(this,t,[e,n,r]),i.props={},i.dataset=k["b"],o.bind(Object(m["a"])(i)),i.nodeType=1,i.style=new ze(Object(m["a"])(i)),r.patchElement(Object(m["a"])(i)),i}return Object(g["a"])(t,e),Object(w["a"])(t,[{key:"_stopPropagation",value:function(e){var t=this;while(t=t.parentNode){var n=t.__handlers[e.type];if(Object(k["h"])(n))for(var r=n.length;r--;){var o=n[r];o._stop=!0}}}},{key:"id",get:function(){return this.getAttribute(M)},set:function(e){this.setAttribute(M,e)}},{key:"className",get:function(){return this.getAttribute(U)||""},set:function(e){this.setAttribute(U,e)}},{key:"cssText",get:function(){return this.getAttribute($)||""}},{key:"classList",get:function(){return new Qe(this.className,this)}},{key:"children",get:function(){return this.childNodes.filter(fe)}},{key:"attributes",get:function(){var e=this.props,t=Object.keys(e),n=this.style.cssText,r=t.map((function(t){return{name:t,value:e[t]}}));return r.concat(n?{name:$,value:n}:[])}},{key:"textContent",get:function(){for(var e="",t=this.childNodes,n=0;n<t.length;n++)e+=t[n].textContent;return e},set:function(e){Object(s["a"])(Object(f["a"])(t.prototype),"textContent",e,this,!0)}},{key:"hasAttribute",value:function(e){return!Object(k["n"])(this.props[e])}},{key:"hasAttributes",value:function(){return this.attributes.length>0}},{key:"focus",get:function(){return function(){this.setAttribute(H,!0)}},set:function(e){this.setAttribute(H,e)}},{key:"blur",value:function(){this.setAttribute(H,!1)}},{key:"setAttribute",value:function(e,t){var n,r,o=this.nodeName===W&&!ve(this)&&!this.isAnyEventBinded();switch(e){case $:this.style.cssText=t;break;case M:Oe.delete(this.uid),t=String(t),this.props[e]=this.uid=t,Oe.set(t,this);break;default:this.props[e]=t,e.startsWith("data-")&&(this.dataset===k["b"]&&(this.dataset=Object.create(null)),this.dataset[Object(k["t"])(e.replace(/^data-/,""))]=t);break}e=ge(e);var i={path:"".concat(this._path,".").concat(Object(k["t"])(e)),value:Object(k["j"])(t)?function(){return t}:t};null===(r=(n=this.hooks).modifySetAttrPayload)||void 0===r||r.call(n,this,e,i),this.enqueueUpdate(i),this.nodeName===W&&(Object(k["t"])(e)===ue?this.enqueueUpdate({path:"".concat(this._path,".","nn"),value:t?se:this.isAnyEventBinded()?W:z}):o&&ve(this)&&this.enqueueUpdate({path:"".concat(this._path,".","nn"),value:z}))}},{key:"removeAttribute",value:function(e){var t,n,r,o,i=this.nodeName===W&&ve(this)&&!this.isAnyEventBinded();if(e===$)this.style.cssText="";else{var a=null===(n=(t=this.hooks).onRemoveAttribute)||void 0===n?void 0:n.call(t,this,e);if(a)return;if(!this.props.hasOwnProperty(e))return;delete this.props[e]}e=ge(e);var c={path:"".concat(this._path,".").concat(Object(k["t"])(e)),value:""};null===(o=(r=this.hooks).modifyRmAttrPayload)||void 0===o||o.call(r,this,e,c),this.enqueueUpdate(c),this.nodeName===W&&(Object(k["t"])(e)===ue?this.enqueueUpdate({path:"".concat(this._path,".","nn"),value:this.isAnyEventBinded()?W:ve(this)?z:V}):i&&!ve(this)&&this.enqueueUpdate({path:"".concat(this._path,".","nn"),value:V}))}},{key:"getAttribute",value:function(e){var t=e===$?this.style.cssText:this.props[e];return null!==t&&void 0!==t?t:""}},{key:"getElementsByTagName",value:function(e){var t=this;return Ge(this,(function(n){return n.nodeName===e||"*"===e&&t!==n}))}},{key:"getElementsByClassName",value:function(e){return Ge(this,(function(t){var n=t.classList,r=e.trim().split(/\s+/);return r.every((function(e){return n.has(e)}))}))}},{key:"dispatchEvent",value:function(e){var t=e.cancelable,n=this.__handlers[e.type];if(!Object(k["h"])(n))return!1;for(var r=n.length;r--;){var o=n[r],i=void 0;if(o._stop?o._stop=!1:i=o.call(this,e),(!1===i||e._end)&&t&&(e.defaultPrevented=!0),e._end&&e._stop)break}return e._stop?this._stopPropagation(e):e._stop=!0,null!=n}},{key:"addEventListener",value:function(e,n,r){var o=this.nodeName,i=this.hooks.getSpecialNodes();!this.isAnyEventBinded()&&i.indexOf(o)>-1&&this.enqueueUpdate({path:"".concat(this._path,".","nn"),value:o}),Object(d["a"])(Object(f["a"])(t.prototype),"addEventListener",this).call(this,e,n,r)}},{key:"removeEventListener",value:function(e,n){Object(d["a"])(Object(f["a"])(t.prototype),"removeEventListener",this).call(this,e,n);var r=this.nodeName,o=this.hooks.getSpecialNodes();!this.isAnyEventBinded()&&o.indexOf(r)>-1&&this.enqueueUpdate({path:"".concat(this._path,".","nn"),value:ve(this)?"static-".concat(r):"pure-".concat(r)})}}]),t}(ke);Ye=S([Object(j["d"])(),T(0,Object(j["c"])(C.TaroNodeImpl)),T(1,Object(j["c"])(C.TaroElementFactory)),T(2,Object(j["c"])(C.Hooks)),T(3,Object(j["c"])(C.TaroElementImpl)),E("design:paramtypes",[Function,Function,Function,Function])],Ye);var Je=Array.isArray,Xe="object"==("undefined"===typeof r?"undefined":Object(O["a"])(r))&&r&&r.Object===Object&&r,Ze="object"==("undefined"===typeof self?"undefined":Object(O["a"])(self))&&self&&self.Object===Object&&self,et=Xe||Ze||Function("return this")(),tt=et.Symbol,nt=Object.prototype,rt=nt.hasOwnProperty,ot=nt.toString,it=tt?tt.toStringTag:void 0;function at(e){var t=rt.call(e,it),n=e[it];try{e[it]=void 0;var r=!0}catch(e){}var o=ot.call(e);return r&&(t?e[it]=n:delete e[it]),o}var ct=Object.prototype,ut=ct.toString;function st(e){return ut.call(e)}var lt="[object Null]",dt="[object Undefined]",ft=tt?tt.toStringTag:void 0;function ht(e){return null==e?void 0===e?dt:lt:ft&&ft in Object(e)?at(e):st(e)}function pt(e){return null!=e&&"object"==Object(O["a"])(e)}var vt="[object Symbol]";function mt(e){return"symbol"==Object(O["a"])(e)||pt(e)&&ht(e)==vt}var gt=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,bt=/^\w*$/;function yt(e,t){if(Je(e))return!1;var n=Object(O["a"])(e);return!("number"!=n&&"symbol"!=n&&"boolean"!=n&&null!=e&&!mt(e))||(bt.test(e)||!gt.test(e)||null!=t&&e in Object(t))}function wt(e){var t=Object(O["a"])(e);return null!=e&&("object"==t||"function"==t)}var Ot="[object AsyncFunction]",jt="[object Function]",kt="[object GeneratorFunction]",St="[object Proxy]";function Tt(e){if(!wt(e))return!1;var t=ht(e);return t==jt||t==kt||t==Ot||t==St}var Et=et["__core-js_shared__"],Ct=function(){var e=/[^.]+$/.exec(Et&&Et.keys&&Et.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}();function _t(e){return!!Ct&&Ct in e}var Pt=Function.prototype,At=Pt.toString;function xt(e){if(null!=e){try{return At.call(e)}catch(e){}try{return e+""}catch(e){}}return""}var It=/[\\^$.*+?()[\]{}|]/g,Nt=/^\[object .+?Constructor\]$/,Bt=Function.prototype,Ft=Object.prototype,Lt=Bt.toString,Rt=Ft.hasOwnProperty,Dt=RegExp("^"+Lt.call(Rt).replace(It,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");function Mt(e){if(!wt(e)||_t(e))return!1;var t=Tt(e)?Dt:Nt;return t.test(xt(e))}function qt(e,t){return null==e?void 0:e[t]}function Ut(e,t){var n=qt(e,t);return Mt(n)?n:void 0}var $t=Ut(Object,"create");function Ht(){this.__data__=$t?$t(null):{},this.size=0}function Wt(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}var zt="__lodash_hash_undefined__",Vt=Object.prototype,Gt=Vt.hasOwnProperty;function Kt(e){var t=this.__data__;if($t){var n=t[e];return n===zt?void 0:n}return Gt.call(t,e)?t[e]:void 0}var Qt=Object.prototype,Yt=Qt.hasOwnProperty;function Jt(e){var t=this.__data__;return $t?void 0!==t[e]:Yt.call(t,e)}var Xt="__lodash_hash_undefined__";function Zt(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=$t&&void 0===t?Xt:t,this}function en(e){var t=-1,n=null==e?0:e.length;this.clear();while(++t<n){var r=e[t];this.set(r[0],r[1])}}function tn(){this.__data__=[],this.size=0}function nn(e,t){return e===t||e!==e&&t!==t}function rn(e,t){var n=e.length;while(n--)if(nn(e[n][0],t))return n;return-1}en.prototype.clear=Ht,en.prototype["delete"]=Wt,en.prototype.get=Kt,en.prototype.has=Jt,en.prototype.set=Zt;var on=Array.prototype,an=on.splice;function cn(e){var t=this.__data__,n=rn(t,e);if(n<0)return!1;var r=t.length-1;return n==r?t.pop():an.call(t,n,1),--this.size,!0}function un(e){var t=this.__data__,n=rn(t,e);return n<0?void 0:t[n][1]}function sn(e){return rn(this.__data__,e)>-1}function ln(e,t){var n=this.__data__,r=rn(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this}function dn(e){var t=-1,n=null==e?0:e.length;this.clear();while(++t<n){var r=e[t];this.set(r[0],r[1])}}dn.prototype.clear=tn,dn.prototype["delete"]=cn,dn.prototype.get=un,dn.prototype.has=sn,dn.prototype.set=ln;var fn=Ut(et,"Map");function hn(){this.size=0,this.__data__={hash:new en,map:new(fn||dn),string:new en}}function pn(e){var t=Object(O["a"])(e);return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e}function vn(e,t){var n=e.__data__;return pn(t)?n["string"==typeof t?"string":"hash"]:n.map}function mn(e){var t=vn(this,e)["delete"](e);return this.size-=t?1:0,t}function gn(e){return vn(this,e).get(e)}function bn(e){return vn(this,e).has(e)}function yn(e,t){var n=vn(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this}function wn(e){var t=-1,n=null==e?0:e.length;this.clear();while(++t<n){var r=e[t];this.set(r[0],r[1])}}wn.prototype.clear=hn,wn.prototype["delete"]=mn,wn.prototype.get=gn,wn.prototype.has=bn,wn.prototype.set=yn;var On="Expected a function";function jn(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw new TypeError(On);var n=function n(){var r=arguments,o=t?t.apply(this,r):r[0],i=n.cache;if(i.has(o))return i.get(o);var a=e.apply(this,r);return n.cache=i.set(o,a)||i,a};return n.cache=new(jn.Cache||wn),n}jn.Cache=wn;var kn=500;function Sn(e){var t=jn(e,(function(e){return n.size===kn&&n.clear(),e})),n=t.cache;return t}var Tn=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,En=/\\(\\)?/g,Cn=Sn((function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(Tn,(function(e,n,r,o){t.push(r?o.replace(En,"$1"):n||e)})),t}));function _n(e,t){var n=-1,r=null==e?0:e.length,o=Array(r);while(++n<r)o[n]=t(e[n],n,e);return o}var Pn=1/0,An=tt?tt.prototype:void 0,xn=An?An.toString:void 0;function In(e){if("string"==typeof e)return e;if(Je(e))return _n(e,In)+"";if(mt(e))return xn?xn.call(e):"";var t=e+"";return"0"==t&&1/e==-Pn?"-0":t}function Nn(e){return null==e?"":In(e)}function Bn(e,t){return Je(e)?e:yt(e,t)?[e]:Cn(Nn(e))}var Fn=1/0;function Ln(e){if("string"==typeof e||mt(e))return e;var t=e+"";return"0"==t&&1/e==-Fn?"-0":t}function Rn(e,t){t=Bn(t,e);var n=0,r=t.length;while(null!=e&&n<r)e=e[Ln(t[n++])];return n&&n==r?e:void 0}function Dn(e,t,n){var r=null==e?void 0:Rn(e,t);return void 0===r?n:r}var Mn={prerender:!0,debug:!1},qn=function(){function e(){Object(y["a"])(this,e),this.recorder=new Map}return Object(w["a"])(e,[{key:"start",value:function(e){Mn.debug&&this.recorder.set(e,Date.now())}},{key:"stop",value:function(e){if(Mn.debug){var t=Date.now(),n=this.recorder.get(e),r=t-n;console.log("".concat(e," \u65f6\u957f\uff1a ").concat(r,"ms"))}}}]),e}(),Un=new qn,$n=de(),Hn=function(e){function t(e,n,r,o,i){var a;return Object(y["a"])(this,t),a=Object(v["a"])(this,t,[e,n,r,o]),a.pendingFlush=!1,a.updatePayloads=[],a.updateCallbacks=[],a.pendingUpdate=!1,a.ctx=null,a.nodeName=x,a.eventCenter=i,a}return Object(g["a"])(t,e),Object(w["a"])(t,[{key:"_path",get:function(){return x}},{key:"_root",get:function(){return this}},{key:"enqueueUpdate",value:function(e){this.updatePayloads.push(e),this.pendingUpdate||null===this.ctx||this.performUpdate()}},{key:"performUpdate",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],n=arguments.length>1?arguments[1]:void 0;this.pendingUpdate=!0;var r=this.ctx;setTimeout((function(){Un.start(P);var o=Object.create(null),i=new Set(t?["root.cn.[0]","root.cn[0]"]:[]);while(e.updatePayloads.length>0){var a=e.updatePayloads.shift(),c=a.path,u=a.value;c.endsWith("cn")&&i.add(c),o[c]=u}var s=function(e){i.forEach((function(t){e.includes(t)&&e!==t&&delete o[e]}));var t=o[e];Object(k["j"])(t)&&(o[e]=t())};for(var l in o)s(l);if(Object(k["j"])(n))n(o);else{e.pendingUpdate=!1;var d=[],f=new Map,h={};if(!t){for(var p in o){for(var v=p.split("."),m=!1,g=v.length;g>0;g--){var y=v.slice(0,g).join("."),w=Dn(r.__data__||r.data,y);if(w&&w.nn&&w.nn===Z){var O=w.uid,j=r.selectComponent("#".concat(O)),S=v.slice(g).join(".");j&&(m=!0,f.set(j,Object.assign(Object.assign({},f.get(j)||{}),Object(b["a"])({},"i.".concat(S),o[p]))));break}}m||(h[p]=o[p])}f.size>0&&f.forEach((function(e,t){d.push({ctx:t,data:e})}))}var T=d.length;if(T){var E="".concat(e._path,"_update_").concat($n()),C=e.eventCenter,_=0;C.once(E,(function(){_++,_===T+1&&(Un.stop(P),e.pendingFlush||e.flushUpdateCallback(),t&&Un.stop(A))}),C),d.forEach((function(e){e.ctx.setData(e.data,(function(){C.trigger(E)}))})),Object.keys(h).length&&r.setData(h,(function(){C.trigger(E)}))}else r.setData(o,(function(){Un.stop(P),e.pendingFlush||e.flushUpdateCallback(),t&&Un.stop(A)}))}}),0)}},{key:"enqueueUpdateCallback",value:function(e,t){this.updateCallbacks.push((function(){t?e.call(t):e()}))}},{key:"flushUpdateCallback",value:function(){this.pendingFlush=!1;var e=this.updateCallbacks.slice(0);this.updateCallbacks.length=0;for(var t=0;t<e.length;t++)e[t]()}}]),t}(Ye);Hn=S([Object(j["d"])(),T(0,Object(j["c"])(C.TaroNodeImpl)),T(1,Object(j["c"])(C.TaroElementFactory)),T(2,Object(j["c"])(C.Hooks)),T(3,Object(j["c"])(C.TaroElementImpl)),T(4,Object(j["c"])(C.eventCenter)),E("design:paramtypes",[Function,Function,Function,Function,Function])],Hn);var Wn=function(e){function t(){return Object(y["a"])(this,t),Object(v["a"])(this,t,arguments)}return Object(g["a"])(t,e),Object(w["a"])(t,[{key:"value",get:function(){var e=this.props[Y];return null==e?"":e},set:function(e){this.setAttribute(Y,e)}},{key:"dispatchEvent",value:function(e){if(e.mpEvent){var n=e.mpEvent.detail.value;e.type===X?this.props.value=n:e.type===J&&(this.value=n)}return Object(d["a"])(Object(f["a"])(t.prototype),"dispatchEvent",this).call(this,e)}}]),t}(Ye),zn=function(e){function t(){return Object(y["a"])(this,t),Object(v["a"])(this,t,arguments)}return Object(g["a"])(t,e),Object(w["a"])(t)}(Ye);function Vn(){return{index:0,column:0,line:0}}function Gn(e,t,n){for(var r=e.index,o=e.index=r+n,i=r;i<o;i++){var a=t.charAt(i);"\n"===a?(e.line++,e.column=0):e.column++}}function Kn(e,t,n){var r=n-e.index;return Gn(e,t,r)}function Qn(e){return{index:e.index,line:e.line,column:e.column}}var Yn=/\s/;function Jn(e){return Yn.test(e)}var Xn=/=/;function Zn(e){return Xn.test(e)}function er(e){var t=e.toLowerCase();return!!Mn.html.skipElements.has(t)}var tr=/[A-Za-z0-9]/;function nr(e,t){while(1){var n=e.indexOf("<",t);if(-1===n)return n;var r=e.charAt(n+1);if("/"===r||"!"===r||tr.test(r))return n;t=n+1}}function rr(e,t,n){if(!Jn(n.charAt(e)))return!1;for(var r=n.length,o=e-1;o>t;o--){var i=n.charAt(o);if(!Jn(i)){if(Zn(i))return!1;break}}for(var a=e+1;a<r;a++){var c=n.charAt(a);if(!Jn(c))return!Zn(c)}}var or=function(){function e(t){Object(y["a"])(this,e),this.tokens=[],this.position=Vn(),this.html=t}return Object(w["a"])(e,[{key:"scan",value:function(){var e=this.html,t=this.position,n=e.length;while(t.index<n){var r=t.index;if(this.scanText(),t.index===r){var o=e.startsWith("!--",r+1);if(o)this.scanComment();else{var i=this.scanTag();er(i)&&this.scanSkipTag(i)}}}return this.tokens}},{key:"scanText",value:function(){var e="text",t=this.html,n=this.position,r=nr(t,n.index);if(r!==n.index){-1===r&&(r=t.length);var o=Qn(n),i=t.slice(n.index,r);Kn(n,t,r);var a=Qn(n);this.tokens.push({type:e,content:i,position:{start:o,end:a}})}}},{key:"scanComment",value:function(){var e="comment",t=this.html,n=this.position,r=Qn(n);Gn(n,t,4);var o=t.indexOf("--\x3e",n.index),i=o+3;-1===o&&(o=i=t.length);var a=t.slice(n.index,o);Kn(n,t,i),this.tokens.push({type:e,content:a,position:{start:r,end:Qn(n)}})}},{key:"scanTag",value:function(){this.scanTagStart();var e=this.scanTagName();return this.scanAttrs(),this.scanTagEnd(),e}},{key:"scanTagStart",value:function(){var e="tag-start",t=this.html,n=this.position,r=t.charAt(n.index+1),o="/"===r,i=Qn(n);Gn(n,t,o?2:1),this.tokens.push({type:e,close:o,position:{start:i}})}},{key:"scanTagEnd",value:function(){var e="tag-end",t=this.html,n=this.position,r=t.charAt(n.index),o="/"===r;Gn(n,t,o?2:1);var i=Qn(n);this.tokens.push({type:e,close:o,position:{end:i}})}},{key:"scanTagName",value:function(){var e="tag",t=this.html,n=this.position,r=t.length,o=n.index;while(o<r){var i=t.charAt(o),a=!(Jn(i)||"/"===i||">"===i);if(a)break;o++}var c=o+1;while(c<r){var u=t.charAt(c),s=!(Jn(u)||"/"===u||">"===u);if(!s)break;c++}Kn(n,t,c);var l=t.slice(o,c);return this.tokens.push({type:e,content:l}),l}},{key:"scanAttrs",value:function(){var e=this.html,t=this.position,n=this.tokens,r=t.index,o=null,i=r,a=[],c=e.length;while(r<c){var u=e.charAt(r);if(o){var s=u===o;s&&(o=null),r++}else{var l="/"===u||">"===u;if(l){r!==i&&a.push(e.slice(i,r));break}if(rr(r,i,e))r!==i&&a.push(e.slice(i,r)),i=r+1,r++;else{var d="'"===u||'"'===u;d?(o=u,r++):r++}}}Kn(t,e,r);for(var f=a.length,h="attribute",p=0;p<f;p++){var v=a[p],m=v.includes("=");if(m){var g=a[p+1];if(g&&g.startsWith("=")){if(g.length>1){var b=v+g;n.push({type:h,content:b}),p+=1;continue}var y=a[p+2];if(p+=1,y){var w=v+"="+y;n.push({type:h,content:w}),p+=1;continue}}}if(v.endsWith("=")){var O=a[p+1];if(O&&!O.includes("=")){var j=v+O;n.push({type:h,content:j}),p+=1;continue}var k=v.slice(0,-1);n.push({type:h,content:k})}else n.push({type:h,content:v})}}},{key:"scanSkipTag",value:function(e){var t=this.html,n=this.position,r=e.toLowerCase(),o=t.length;while(n.index<o){var i=t.indexOf("</",n.index);if(-1===i){this.scanText();break}Kn(n,t,i);var a=this.scanTag();if(r===a.toLowerCase())break}}}]),e}();function ir(e,t){for(var n=Object.create(null),r=e.split(","),o=0;o<r.length;o++)n[r[o]]=!0;return t?function(e){return!!n[e.toLowerCase()]}:function(e){return!!n[e]}}var ar={img:"image",iframe:"web-view"},cr=Object.keys(k["g"]).map((function(e){return e.toLowerCase()})).join(","),ur=ir(cr,!0),sr=ir("a,i,abbr,iframe,select,acronym,slot,small,span,bdi,kbd,strong,big,map,sub,sup,br,mark,mark,meter,template,canvas,textarea,cite,object,time,code,output,u,data,picture,tt,datalist,var,dfn,del,q,em,s,embed,samp,b",!0),lr=ir("address,fieldset,li,article,figcaption,main,aside,figure,nav,blockquote,footer,ol,details,form,p,dialog,h1,h2,h3,h4,h5,h6,pre,dd,header,section,div,hgroup,table,dl,hr,ul,dt",!0);function dr(e){var t=e.charAt(0),n=e.length-1,r='"'===t||"'"===t;return r&&t===e.charAt(n)?e.slice(1,n):e}var fr="{",hr="}",pr=".",vr="#",mr=">",gr="~",br="+",yr=function(){function e(){Object(y["a"])(this,e),this.styles=[]}return Object(w["a"])(e,[{key:"extractStyle",value:function(e){var t=this,n=/<style\s?[^>]*>((.|\n|\s)+?)<\/style>/g,r=e;return r=r.replace(n,(function(e,n){var r=n.trim();return t.stringToSelector(r),""})),r.trim()}},{key:"stringToSelector",value:function(e){var t=this,n=e.indexOf(fr),r=function(){var r=e.indexOf(hr),o=e.slice(0,n).trim(),i=e.slice(n+1,r);i=i.replace(/:(.*);/g,(function(e,t){var n=t.trim().replace(/ +/g,"+++");return":".concat(n,";")})),i=i.replace(/ /g,""),i=i.replace(/\+\+\+/g," "),/;$/.test(i)||(i+=";"),o.split(",").forEach((function(e){var n=t.parseSelector(e);t.styles.push({content:i,selectorList:n})})),e=e.slice(r+1),n=e.indexOf(fr)};while(n>-1)r()}},{key:"parseSelector",value:function(e){var t=e.trim().replace(/ *([>~+]) */g," $1").replace(/ +/g," ").replace(/\[\s*([^[\]=\s]+)\s*=\s*([^[\]=\s]+)\s*\]/g,"[$1=$2]").split(" "),n=t.map((function(e){var t=e.charAt(0),n={isChild:t===mr,isGeneralSibling:t===gr,isAdjacentSibling:t===br,tag:null,id:null,class:[],attrs:[]};return e=e.replace(/^[>~+]/,""),e=e.replace(/\[(.+?)\]/g,(function(e,t){var r=t.split("="),o=Object(u["a"])(r,2),i=o[0],a=o[1],c=-1===t.indexOf("="),s={all:c,key:i,value:c?null:a};return n.attrs.push(s),""})),e=e.replace(/([.#][A-Za-z0-9-_]+)/g,(function(e,t){return t[0]===vr?n.id=t.substr(1):t[0]===pr&&n.class.push(t.substr(1)),""})),""!==e&&(n.tag=e),n}));return n}},{key:"matchStyle",value:function(e,t,n){var r=this,o=Or(this.styles).reduce((function(o,i,a){var c=i.content,u=i.selectorList,s=n[a],l=u[s],d=u[s+1];((null===d||void 0===d?void 0:d.isGeneralSibling)||(null===d||void 0===d?void 0:d.isAdjacentSibling))&&(l=d,s+=1,n[a]+=1);var f=r.matchCurrent(e,t,l);if(f&&l.isGeneralSibling){var h=wr(t);while(h){if(h.h5tagName&&r.matchCurrent(h.h5tagName,h,u[s-1])){f=!0;break}h=wr(h),f=!1}}if(f&&l.isAdjacentSibling){var p=wr(t);if(p&&p.h5tagName){var v=r.matchCurrent(p.h5tagName,p,u[s-1]);v||(f=!1)}else f=!1}if(f){if(s===u.length-1)return o+c;s<u.length-1&&(n[a]+=1)}else l.isChild&&s>0&&(n[a]-=1,r.matchCurrent(e,t,u[n[a]])&&(n[a]+=1));return o}),"");return o}},{key:"matchCurrent",value:function(e,t,n){if(n.tag&&n.tag!==e)return!1;if(n.id&&n.id!==t.id)return!1;if(n.class.length)for(var r=t.className.split(" "),o=0;o<n.class.length;o++){var i=n.class[o];if(-1===r.indexOf(i))return!1}if(n.attrs.length)for(var a=0;a<n.attrs.length;a++){var c=n.attrs[a],u=c.all,s=c.key,l=c.value;if(u&&!t.hasAttribute(s))return!1;var d=t.getAttribute(s);if(d!==dr(l||""))return!1}return!0}}]),e}();function wr(e){var t=e.parentElement;if(!t)return null;var n=e.previousSibling;return n?1===n.nodeType?n:wr(n):null}function Or(e){return e.sort((function(e,t){var n=jr(e.selectorList),r=jr(t.selectorList);if(n!==r)return n-r;var o=kr(e.selectorList),i=kr(t.selectorList);if(o!==i)return o-i;var a=Sr(e.selectorList),c=Sr(t.selectorList);return a-c}))}function jr(e){return e.reduce((function(e,t){return e+(t.id?1:0)}),0)}function kr(e){return e.reduce((function(e,t){return e+t.class.length+t.attrs.length}),0)}function Sr(e){return e.reduce((function(e,t){return e+(t.tag?1:0)}),0)}var Tr={li:["ul","ol","menu"],dt:["dl"],dd:["dl"],tbody:["table"],thead:["table"],tfoot:["table"],tr:["table"],td:["table"]};function Er(e,t){var n=Tr[e];if(n){var r=t.length-1;while(r>=0){var o=t[r].tagName;if(o===e)break;if(n&&n.includes(o))return!0;r--}}return!1}function Cr(e){return Mn.html.renderHTMLTag?e:ar[e]?ar[e]:ur(e)?e:lr(e)?"view":sr(e)?"text":"view"}function _r(e){var t="=",n=e.indexOf(t);if(-1===n)return[e];var r=e.slice(0,n).trim(),o=e.slice(n+t.length).trim();return[r,o]}function Pr(e,t,n,r){return e.filter((function(e){return"comment"!==e.type&&("text"!==e.type||""!==e.content)})).map((function(e){if("text"===e.type){var o=t.createTextNode(e.content);return Object(k["j"])(Mn.html.transformText)&&(o=Mn.html.transformText(o,e)),null===r||void 0===r||r.appendChild(o),o}var i=t.createElement(Cr(e.tagName));i.h5tagName=e.tagName,null===r||void 0===r||r.appendChild(i),Mn.html.renderHTMLTag||(i.className="h5-".concat(e.tagName));for(var a=0;a<e.attributes.length;a++){var c=e.attributes[a],s=_r(c),l=Object(u["a"])(s,2),d=l[0],f=l[1];if("class"===d)i.className+=" "+dr(f);else{if("o"===d[0]&&"n"===d[1])continue;i.setAttribute(d,null==f||dr(f))}}var h=n.styleTagParser,p=n.descendantList,v=p.slice(),m=h.matchStyle(e.tagName,i,v);return i.setAttribute("style",m+i.style.cssText),Pr(e.children,t,{styleTagParser:h,descendantList:v},i),Object(k["j"])(Mn.html.transformElement)?Mn.html.transformElement(i,e):i}))}function Ar(e,t){var n=new yr;e=n.extractStyle(e);var r=new or(e).scan(),o={tagName:"",children:[],type:"element",attributes:[]},i={tokens:r,options:Mn,cursor:0,stack:[o]};return xr(i),Pr(o.children,t,{styleTagParser:n,descendantList:Array(n.styles.length).fill(0)})}function xr(e){var t=e.tokens,n=e.stack,r=e.cursor,o=t.length,i=n[n.length-1].children;while(r<o){var a=t[r];if("tag-start"===a.type){var c=t[++r];r++;var u=c.content.toLowerCase();if(a.close){var s=n.length,l=!1;while(--s>-1)if(n[s].tagName===u){l=!0;break}while(r<o){var d=t[r];if("tag-end"!==d.type)break;r++}if(l){n.splice(s);break}}else{var f=Mn.html.closingElements.has(u),h=f;if(h&&(h=!Er(u,n)),h){var p=n.length-1;while(p>0){if(u===n[p].tagName){n.splice(p);var v=p-1;i=n[v].children;break}p-=1}}var m=[],g=void 0;while(r<o){if(g=t[r],"tag-end"===g.type)break;m.push(g.content),r++}r++;var b=[],y={type:"element",tagName:c.content,attributes:m,children:b};i.push(y);var w=!(g.close||Mn.html.voidElements.has(u));if(w){n.push({tagName:u,children:b});var O={tokens:t,cursor:r,stack:n};xr(O),r=O.cursor}}}else i.push(a),r++}e.cursor=r}function Ir(e,t,n){while(e.firstChild)e.removeChild(e.firstChild);for(var r=Ar(t,n()),o=0;o<r.length;o++)e.appendChild(r[o])}function Nr(e,t,n){for(var r,o,i=Ar(t,n()),a=0;a<i.length;a++){var c=i[a];switch(e){case"beforebegin":null===(r=this.parentNode)||void 0===r||r.insertBefore(c,this);break;case"afterbegin":this.hasChildNodes()?this.insertBefore(c,this.childNodes[0]):this.appendChild(c);break;case"beforeend":this.appendChild(c);break;case"afterend":null===(o=this.parentNode)||void 0===o||o.appendChild(c);break}}}function Br(e,t){var n,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=t();for(var i in 1===e.nodeType?n=o.createElement(e.nodeName):3===e.nodeType&&(n=o.createTextNode("")),this){var a=this[i];[G,K].includes(i)&&Object(O["a"])(a)===Q?n[i]=Object.assign({},a):"_value"===i?n[i]=a:i===$&&(n.style._value=Object.assign({},a._value),n.style._usedStyleProp=new Set(Array.from(a._usedStyleProp)))}return r&&(n.childNodes=e.childNodes.map((function(e){return e.cloneNode(!0)}))),n}Mn.html={skipElements:new Set(["style","script"]),voidElements:new Set(["!doctype","area","base","br","col","command","embed","hr","img","input","keygen","link","meta","param","source","track","wbr"]),closingElements:new Set(["html","head","body","p","dt","dd","li","option","thead","th","tbody","tr","td","tfoot","colgroup"]),renderHTMLTag:!1};var Fr=function(){function e(t){Object(y["a"])(this,e),this.getDoc=function(){return t(we.Document)()}}return Object(w["a"])(e,[{key:"bind",value:function(e){var t=this.getDoc;Lr(e,t),Rr(e,t),e.cloneNode=Br.bind(e,e,t)}}]),e}();function Lr(e,t){Object.defineProperty(e,"innerHTML",{configurable:!0,enumerable:!0,set:function(n){Ir.call(e,e,n,t)},get:function(){return""}})}function Rr(e,t){e.insertAdjacentHTML=function(n,r){Nr.call(e,n,r,t)}}function Dr(e){if("template"===e.nodeName){var t=e._getElement(we.Element)(D);return t.childNodes=e.childNodes,e.childNodes=[t],t.parentNode=e,t.childNodes.forEach((function(e){e.parentNode=t})),t}}Fr=S([Object(j["d"])(),T(0,Object(j["c"])(C.TaroElementFactory)),E("design:paramtypes",[Function])],Fr);var Mr=function(){function e(){Object(y["a"])(this,e)}return Object(w["a"])(e,[{key:"bind",value:function(e){qr(e)}}]),e}();function qr(e){Object.defineProperty(e,"content",{configurable:!0,enumerable:!0,get:function(){return Dr(e)}})}Mr=S([Object(j["d"])()],Mr);var Ur=function(e){function t(e,n,r,o,i){var a;return Object(y["a"])(this,t),a=Object(v["a"])(this,t,[e,n,r,o]),a._getText=i,a.nodeType=9,a.nodeName=R,a}return Object(g["a"])(t,e),Object(w["a"])(t,[{key:"createElement",value:function(e){return e===x?this._getElement(we.RootElement)():k["d"].has(e)?this._getElement(we.FormElement)(e):this._getElement(we.Element)(e)}},{key:"createElementNS",value:function(e,t){return this.createElement(t)}},{key:"createTextNode",value:function(e){return this._getText(e)}},{key:"getElementById",value:function(e){var t=Oe.get(e);return Object(k["n"])(t)?null:t}},{key:"querySelector",value:function(e){return/^#/.test(e)?this.getElementById(e.slice(1)):null}},{key:"querySelectorAll",value:function(){return[]}},{key:"createComment",value:function(){var e=this._getText("");return e.nodeName=le,e}}]),t}(Ye);Ur=S([Object(j["d"])(),T(0,Object(j["c"])(C.TaroNodeImpl)),T(1,Object(j["c"])(C.TaroElementFactory)),T(2,Object(j["c"])(C.Hooks)),T(3,Object(j["c"])(C.TaroElementImpl)),T(4,Object(j["c"])(C.TaroTextFactory)),E("design:paramtypes",[Function,Function,Function,Function,Function])],Ur);var $r=function(){function e(){Object(y["a"])(this,e)}return Object(w["a"])(e,[{key:"modifyMpEvent",value:function(e){var t;null===(t=this.modifyMpEventImpls)||void 0===t||t.forEach((function(t){return t(e)}))}},{key:"modifyTaroEvent",value:function(e,t){var n;null===(n=this.modifyTaroEventImpls)||void 0===n||n.forEach((function(n){return n(e,t)}))}},{key:"initNativeApi",value:function(e){var t;null===(t=this.initNativeApiImpls)||void 0===t||t.forEach((function(t){return t(e)}))}},{key:"patchElement",value:function(e){var t;null===(t=this.patchElementImpls)||void 0===t||t.forEach((function(t){return t(e)}))}}]),e}();S([Object(j["c"])(C.getLifecycle),E("design:type",Function)],$r.prototype,"getLifecycle",void 0),S([Object(j["c"])(C.getPathIndex),E("design:type",Function)],$r.prototype,"getPathIndex",void 0),S([Object(j["c"])(C.getEventCenter),E("design:type",Function)],$r.prototype,"getEventCenter",void 0),S([Object(j["c"])(C.isBubbleEvents),E("design:type",Function)],$r.prototype,"isBubbleEvents",void 0),S([Object(j["c"])(C.getSpecialNodes),E("design:type",Function)],$r.prototype,"getSpecialNodes",void 0),S([Object(j["c"])(C.onRemoveAttribute),Object(j["f"])(),E("design:type",Function)],$r.prototype,"onRemoveAttribute",void 0),S([Object(j["c"])(C.batchedEventUpdates),Object(j["f"])(),E("design:type",Function)],$r.prototype,"batchedEventUpdates",void 0),S([Object(j["c"])(C.mergePageInstance),Object(j["f"])(),E("design:type",Function)],$r.prototype,"mergePageInstance",void 0),S([Object(j["c"])(C.createPullDownComponent),Object(j["f"])(),E("design:type",Function)],$r.prototype,"createPullDownComponent",void 0),S([Object(j["c"])(C.getDOMNode),Object(j["f"])(),E("design:type",Function)],$r.prototype,"getDOMNode",void 0),S([Object(j["c"])(C.modifyHydrateData),Object(j["f"])(),E("design:type",Function)],$r.prototype,"modifyHydrateData",void 0),S([Object(j["c"])(C.modifySetAttrPayload),Object(j["f"])(),E("design:type",Function)],$r.prototype,"modifySetAttrPayload",void 0),S([Object(j["c"])(C.modifyRmAttrPayload),Object(j["f"])(),E("design:type",Function)],$r.prototype,"modifyRmAttrPayload",void 0),S([Object(j["c"])(C.onAddEvent),Object(j["f"])(),E("design:type",Function)],$r.prototype,"onAddEvent",void 0),S([Object(j["e"])(C.modifyMpEvent),Object(j["f"])(),E("design:type",Array)],$r.prototype,"modifyMpEventImpls",void 0),S([Object(j["e"])(C.modifyTaroEvent),Object(j["f"])(),E("design:type",Array)],$r.prototype,"modifyTaroEventImpls",void 0),S([Object(j["e"])(C.initNativeApi),Object(j["f"])(),E("design:type",Array)],$r.prototype,"initNativeApiImpls",void 0),S([Object(j["e"])(C.patchElement),Object(j["f"])(),E("design:type",Array)],$r.prototype,"patchElementImpls",void 0),$r=S([Object(j["d"])()],$r);var Hr=new Set(["touchstart","touchmove","touchcancel","touchend","touchforcechange","tap","longpress","longtap","transitionend","animationstart","animationiteration","animationend"]),Wr=function(e,t){return e[t]},zr=function(e){return"[".concat(e,"]")},Vr=function(e){return new e},Gr=function(e){return Hr.has(e)},Kr=function(){return["view","text","image"]},Qr=new j["b"]((function(e){e(C.getLifecycle).toFunction(Wr),e(C.getPathIndex).toFunction(zr),e(C.getEventCenter).toFunction(Vr),e(C.isBubbleEvents).toFunction(Gr),e(C.getSpecialNodes).toFunction(Kr)}));function Yr(e){var t=Object.keys(k["e"]);t.forEach((function(t){if(t in C){var n=C[t],r=k["e"][t];Object(k["h"])(r)?r.forEach((function(t){return e.bind(n).toFunction(t)})):e.isBound(n)?e.rebind(n).toFunction(r):e.bind(n).toFunction(r)}}))}var Jr,Xr,Zr,eo=new j["a"];eo.bind(C.TaroElement).to(Ye).whenTargetNamed(we.Element),eo.bind(C.TaroElement).to(Ur).inSingletonScope().whenTargetNamed(we.Document),eo.bind(C.TaroElement).to(Hn).whenTargetNamed(we.RootElement),eo.bind(C.TaroElement).to(Wn).whenTargetNamed(we.FormElement),eo.bind(C.TaroElementFactory).toFactory((function(e){return function(t){return function(n){var r=e.container.getNamed(C.TaroElement,t);return n&&(r.nodeName=n),r.tagName=r.nodeName.toUpperCase(),r}}})),eo.bind(C.TaroText).to(Se),eo.bind(C.TaroTextFactory).toFactory((function(e){return function(t){var n=e.container.get(C.TaroText);return n._value=t,n}})),eo.bind(C.TaroNodeImpl).to(Fr).inSingletonScope(),eo.bind(C.TaroElementImpl).to(Mr).inSingletonScope(),eo.bind(C.Hooks).to($r).inSingletonScope(),eo.load(Qr),Yr(eo),Jr=eo.get(C.Hooks),Xr=eo.get(C.TaroElementFactory),Zr=Xr(we.Document)();var to=function(){function e(t,n,r){Object(y["a"])(this,e),this._stop=!1,this._end=!1,this.defaultPrevented=!1,this.timeStamp=Date.now(),this.type=t.toLowerCase(),this.mpEvent=r,this.bubbles=Boolean(n&&n.bubbles),this.cancelable=Boolean(n&&n.cancelable)}return Object(w["a"])(e,[{key:"stopPropagation",value:function(){this._stop=!0}},{key:"stopImmediatePropagation",value:function(){this._end=this._stop=!0}},{key:"preventDefault",value:function(){this.defaultPrevented=!0}},{key:"target",get:function(){var e,t,n,r=Zr.getElementById(null===(e=this.mpEvent)||void 0===e?void 0:e.target.id);return Object.assign(Object.assign(Object.assign({},null===(t=this.mpEvent)||void 0===t?void 0:t.target),null===(n=this.mpEvent)||void 0===n?void 0:n.detail),{dataset:null!==r?r.dataset:k["b"]})}},{key:"currentTarget",get:function(){var e,t,n,r=Zr.getElementById(null===(e=this.mpEvent)||void 0===e?void 0:e.currentTarget.id);return null===r?this.target:Object.assign(Object.assign(Object.assign({},null===(t=this.mpEvent)||void 0===t?void 0:t.currentTarget),null===(n=this.mpEvent)||void 0===n?void 0:n.detail),{dataset:r.dataset})}}]),e}();function no(e,t){if("string"===typeof e)return new to(e,{bubbles:!0,cancelable:!0});var n=new to(e.type,{bubbles:!0,cancelable:!0},e);for(var r in e)r!==te&&r!==ee&&r!==ne&&r!==oe&&(n[r]=e[r]);return n.type===re&&(null===t||void 0===t?void 0:t.nodeName)===J&&(n[ie]=13),n}var ro={};function oo(e){var t;null===(t=Jr.modifyMpEvent)||void 0===t||t.call(Jr,e),null==e.currentTarget&&(e.currentTarget=e.target);var n=Zr.getElementById(e.currentTarget.id);if(n){var r=function(){var t,r=no(e,n);null===(t=Jr.modifyTaroEvent)||void 0===t||t.call(Jr,r,n),n.dispatchEvent(r)};if("function"===typeof Jr.batchedEventUpdates){var o=e.type;!Jr.isBubbleEvents(o)||!me(n,o)||o===ae&&n.props.catchMove?Jr.batchedEventUpdates((function(){ro[o]&&(ro[o].forEach((function(e){return e()})),delete ro[o]),r()})):(ro[o]||(ro[o]=[])).push(r)}else r()}}var io="undefined"!==typeof o&&!!o.scripts,ao=io?o:k["b"],co=io?i:k["b"];function uo(){var e=eo.get(C.TaroElementFactory),t=e(we.Document)(),n=t.createElement.bind(t),r=n(I),o=n(N),i=n(B),a=n(F);a.id=F;var c=n(L);return t.appendChild(r),r.appendChild(o),r.appendChild(i),i.appendChild(c),c.appendChild(a),t.documentElement=r,t.head=o,t.body=i,t.createEvent=no,t}var so,lo=io?ao:uo(),fo="Macintosh",ho="Intel Mac OS X 10_14_5",po="AppleWebKit/534.36 (KHTML, like Gecko) NodeJS/v4.1.0 Chrome/76.0.3809.132 Safari/534.36",vo=io?co.navigator:{appCodeName:"Mozilla",appName:"Netscape",appVersion:"5.0 ("+fo+"; "+ho+") "+po,cookieEnabled:!0,mimeTypes:[],onLine:!0,platform:"MacIntel",plugins:[],product:"Taro",productSub:"20030107",userAgent:"Mozilla/5.0 ("+fo+"; "+ho+") "+po,vendor:"Joyent",vendorSub:""};(function(){var e;"undefined"!==typeof performance&&null!==performance&&performance.now?so=function(){return performance.now()}:Date.now?(so=function(){return Date.now()-e},e=Date.now()):(so=function(){return(new Date).getTime()-e},e=(new Date).getTime())})();var mo=0,go="undefined"!==typeof a&&null!==a?a:function(e){var t=so(),n=Math.max(mo+16,t);return setTimeout((function(){e(mo=n)}),n-t)},bo="undefined"!==typeof c&&null!==c?c:function(e){clearTimeout(e)};function yo(e){return e.style}var wo=io?co:{navigator:vo,document:lo};if(!io){var Oo=[].concat(Object(l["a"])(Object.getOwnPropertyNames(r||co)),Object(l["a"])(Object.getOwnPropertySymbols(r||co)));Oo.forEach((function(e){"atob"!==e&&(Object.prototype.hasOwnProperty.call(wo,e)||(wo[e]=r[e]))})),lo.defaultView=wo}wo.requestAnimationFrame=go,wo.cancelAnimationFrame=bo,wo.getComputedStyle=yo,wo.addEventListener=function(){},wo.removeEventListener=function(){},ce in wo||(wo.Date=Date),wo.setTimeout=function(e,t){setTimeout(e,t)},wo.clearTimeout=function(e){clearTimeout(e)};var jo={app:null,router:null,page:null},ko=function(){return jo},So=function(){function e(t){Object(y["a"])(this,e),"undefined"!==typeof t&&t.callbacks?this.callbacks=t.callbacks:this.callbacks={}}return Object(w["a"])(e,[{key:"on",value:function(t,n,r){var o,i,a,c;if(!n)return this;t=t.split(e.eventSplitter),this.callbacks||(this.callbacks={});var u=this.callbacks;while(o=t.shift())c=u[o],i=c?c.tail:{},i.next=a={},i.context=r,i.callback=n,u[o]={tail:a,next:c?c.next:i};return this}},{key:"once",value:function(e,t,n){var r=this,o=function o(){for(var i=arguments.length,a=new Array(i),c=0;c<i;c++)a[c]=arguments[c];t.apply(r,a),r.off(e,o,n)};return this.on(e,o,n),this}},{key:"off",value:function(t,n,r){var o,i,a,c,u,s;if(!(i=this.callbacks))return this;if(!(t||n||r))return delete this.callbacks,this;t=t?t.split(e.eventSplitter):Object.keys(i);while(o=t.shift())if(a=i[o],delete i[o],a&&(n||r)){c=a.tail;while((a=a.next)!==c)u=a.callback,s=a.context,(n&&u!==n||r&&s!==r)&&this.on(o,u,s)}return this}},{key:"trigger",value:function(t){var n,r,o,i;if(!(o=this.callbacks))return this;t=t.split(e.eventSplitter);var a=[].slice.call(arguments,1);while(n=t.shift())if(r=o[n]){i=r.tail;while((r=r.next)!==i)r.callback.apply(r.context||this,a)}return this}}]),e}();So.eventSplitter=/\s+/;var To=eo.get(C.Hooks),Eo=To.getEventCenter(So);eo.bind(C.eventCenter).toConstantValue(Eo);var Co=new Map,_o=de(),Po=eo.get(C.Hooks);function Ao(e,t){var n;null===(n=Po.mergePageInstance)||void 0===n||n.call(Po,Co.get(t),e),Co.set(t,e)}function xo(e){return Co.get(e)}function Io(e){return null==e?"":"/"===e.charAt(0)?e:"/"+e}function No(e,t){for(var n=arguments.length,r=new Array(n>2?n-2:0),o=2;o<n;o++)r[o-2]=arguments[o];var i=Co.get(e);if(null!=i){var a=Po.getLifecycle(i,t);if(Object(k["h"])(a)){var c=a.map((function(e){return e.apply(i,r)}));return c[0]}if(Object(k["j"])(a))return a.apply(i,r)}}function Bo(e){if(null==e)return"";var t=Object.keys(e).map((function(t){return t+"="+e[t]})).join("&");return""===t?t:"?"+t}function Fo(e,t){var n=e;return io||(n=e+Bo(t)),n}function Lo(e){return e+".onReady"}function Ro(e){return e+".onShow"}function Do(e){return e+".onHide"}function Mo(e,t,n,r){var o,i,a,c,u=null!==t&&void 0!==t?t:"taro_page_".concat(_o()),s=null,l=!1,d=[],f={onLoad:function(){var t=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=arguments.length>1?arguments[1]:void 0;c=new Promise((function(e){a=e})),Un.start(A),jo.page=this,this.config=r||{},n.$taroTimestamp=Date.now(),this.$taroPath=Fo(u,n),null==this.$taroParams&&(this.$taroParams=Object.assign({},n));var i=io?this.$taroPath:this.route||this.__route__;jo.router={params:this.$taroParams,path:Io(i),onReady:Lo(u),onShow:Ro(u),onHide:Do(u)};var f=function(){jo.app.mount(e,t.$taroPath,(function(){s=lo.getElementById(t.$taroPath),Object(k["f"])(null!==s,"\u6ca1\u6709\u627e\u5230\u9875\u9762\u5b9e\u4f8b\u3002"),No(t.$taroPath,"onLoad",t.$taroParams),a(),io?Object(k["j"])(o)&&o():(s.ctx=t,s.performUpdate(!0,o))}))};l?d.push(f):f()},onReady:function(){go((function(){Eo.trigger(Lo(u))})),No(this.$taroPath,"onReady"),this.onReady.called=!0},onUnload:function(){var e=this;l=!0,jo.app.unmount(this.$taroPath,(function(){l=!1,Co.delete(e.$taroPath),s&&(s.ctx=null),d.length&&(d.forEach((function(e){return e()})),d=[])}))},onShow:function(){var e=this;c.then((function(){jo.page=e,e.config=r||{};var t=io?e.$taroPath:e.route||e.__route__;jo.router={params:e.$taroParams,path:Io(t),onReady:Lo(u),onShow:Ro(u),onHide:Do(u)},go((function(){Eo.trigger(Ro(u))})),No(e.$taroPath,"onShow")}))},onHide:function(){jo.page=null,jo.router=null,No(this.$taroPath,"onHide"),Eo.trigger(Do(u))},onPullDownRefresh:function(){return No(this.$taroPath,"onPullDownRefresh")},onReachBottom:function(){return No(this.$taroPath,"onReachBottom")},onPageScroll:function(e){return No(this.$taroPath,"onPageScroll",e)},onResize:function(e){return No(this.$taroPath,"onResize",e)},onTabItemTap:function(e){return No(this.$taroPath,"onTabItemTap",e)},onTitleClick:function(){return No(this.$taroPath,"onTitleClick")},onOptionMenuClick:function(){return No(this.$taroPath,"onOptionMenuClick")},onPopMenuClick:function(){return No(this.$taroPath,"onPopMenuClick")},onPullIntercept:function(){return No(this.$taroPath,"onPullIntercept")},onAddToFavorites:function(){return No(this.$taroPath,"onAddToFavorites")}};return(e.onShareAppMessage||(null===(o=e.prototype)||void 0===o?void 0:o.onShareAppMessage)||e.enableShareAppMessage)&&(f.onShareAppMessage=function(e){var t=null===e||void 0===e?void 0:e.target;if(null!=t){var n=t.id,r=lo.getElementById(n);null!=r&&(e.target.dataset=r.dataset)}return No(this.$taroPath,"onShareAppMessage",e)}),(e.onShareTimeline||(null===(i=e.prototype)||void 0===i?void 0:i.onShareTimeline)||e.enableShareTimeline)&&(f.onShareTimeline=function(){return No(this.$taroPath,"onShareTimeline")}),f.eh=oo,Object(k["n"])(n)||(f.data=n),io&&(f.path=u),f}function qo(e,t,n){var r,o,i,a=null!==t&&void 0!==t?t:"taro_component_".concat(_o()),c=null,u={attached:function(){var t,n=this;Un.start(A);var r=Fo(a,{id:(null===(t=this.getPageId)||void 0===t?void 0:t.call(this))||_o()});jo.app.mount(e,r,(function(){c=lo.getElementById(r),Object(k["f"])(null!==c,"\u6ca1\u6709\u627e\u5230\u7ec4\u4ef6\u5b9e\u4f8b\u3002"),No(r,"onLoad"),io||(c.ctx=n,c.performUpdate(!0))}))},detached:function(){var e=Fo(a,{id:this.getPageId()});jo.app.unmount(e,(function(){Co.delete(e),c&&(c.ctx=null)}))},methods:{eh:oo}};return Object(k["n"])(n)||(u.data=n),u["options"]=null!==(r=null===e||void 0===e?void 0:e["options"])&&void 0!==r?r:k["b"],u["externalClasses"]=null!==(o=null===e||void 0===e?void 0:e["externalClasses"])&&void 0!==o?o:k["b"],u["behaviors"]=null!==(i=null===e||void 0===e?void 0:e["behaviors"])&&void 0!==i?i:k["b"],u}function Uo(e){return{properties:{i:{type:Object,value:Object(b["a"])({},"nn","view")},l:{type:String,value:""}},options:{addGlobalClass:!0,virtualHost:"custom-wrapper"!==e},methods:{eh:oo}}}var $o=eo.get(C.Hooks);function Ho(e,t){var n;return Object(k["j"])(t.render)||!!(null===(n=t.prototype)||void 0===n?void 0:n.isReactComponent)||t.prototype instanceof e.Component}var Wo,zo=k["b"],Vo=k["b"];function Go(e,t){var n=e.createElement;return function(r){var o=Ho(e,r),i=function(e){return e&&Ao(e,t)},a=o?{ref:i}:{forwardedRef:i,reactReduxForwardedRef:i};return Vo===k["b"]&&(Vo=e.createContext("")),function(e){function o(){var e;return Object(y["a"])(this,o),e=Object(v["a"])(this,o,arguments),e.state={hasError:!1},e}return Object(g["a"])(o,e),Object(w["a"])(o,[{key:"componentDidCatch",value:function(e,t){}},{key:"render",value:function(){var e=this.state.hasError?[]:n(Vo.Provider,{value:t},n(r,Object.assign(Object.assign({},this.props),a)));return io?n("div",{id:t,className:"taro_page"},e):n("root",{id:t},e)}}],[{key:"getDerivedStateFromError",value:function(e){return{hasError:!0}}}]),o}(e.Component)}}function Ko(){var e=function(e,t){return t=t.replace(/^on(Show|Hide)$/,"componentDid$1"),e[t]},t=function(e){e.type=e.type.replace(/-/g,"")},n=function(e){Wo.unstable_batchedUpdates(e)},r=function(e,t){e&&t&&("constructor"in e||Object.keys(e).forEach((function(n){Object(k["j"])(t[n])?t[n]=[t[n]].concat(Object(l["a"])(e[n])):t[n]=[].concat(Object(l["a"])(t[n]||[]),Object(l["a"])(e[n]))})))};$o.getLifecycle=e,$o.modifyMpEvent=t,$o.batchedEventUpdates=n,$o.mergePageInstance=r}var Qo=de();function Yo(e,t,n,r){var o;zo=t,Wo=n,Object(k["f"])(!!Wo,"\u6784\u5efa React/Nerv \u9879\u76ee\u8bf7\u628a process.env.FRAMEWORK \u8bbe\u7f6e\u4e3a 'react'/'nerv' ");var i=zo.createRef(),a=Ho(zo,e);Ko();var c,u=function(t){function n(){var e;return Object(y["a"])(this,n),e=Object(v["a"])(this,n,arguments),e.pages=[],e.elements=[],e}return Object(g["a"])(n,t),Object(w["a"])(n,[{key:"mount",value:function(e,t,n){var r=t+Qo(),o=function(){return zo.createElement(e,{key:r,tid:t})};this.pages.push(o),this.forceUpdate(n)}},{key:"unmount",value:function(e,t){for(var n=0;n<this.elements.length;n++){var r=this.elements[n];if(r.props.tid===e){this.elements.splice(n,1);break}}this.forceUpdate(t)}},{key:"render",value:function(){while(this.pages.length>0){var t=this.pages.pop();this.elements.push(t())}var n=null;return a&&(n={ref:i}),zo.createElement(e,n,io?zo.createElement("div",null,this.elements.slice()):this.elements.slice())}}]),n}(zo.Component);io||(c=null===(o=Wo.render)||void 0===o?void 0:o.call(Wo,zo.createElement(u),lo.getElementById("app")));var s=Object.create({render:function(e){c.forceUpdate(e)},mount:function(e,t,n){var r=Go(zo,t)(e);c.mount(r,t,n)},unmount:function(e,t){c.unmount(e,t)}},{config:{writable:!0,enumerable:!0,configurable:!0,value:r},onLaunch:{enumerable:!0,writable:!0,value:function(e){var t,n=this;jo.router=Object.assign({params:null===e||void 0===e?void 0:e.query},e),io&&(c=null===(t=Wo.render)||void 0===t?void 0:t.call(Wo,zo.createElement(u),lo.getElementById((null===r||void 0===r?void 0:r.appId)||"app")));var o=i.current;if(null===o||void 0===o?void 0:o.taroGlobalData){var a=o.taroGlobalData,s=Object.keys(a),l=Object.getOwnPropertyDescriptors(a);s.forEach((function(e){Object.defineProperty(n,e,{configurable:!0,enumerable:!0,get:function(){return a[e]},set:function(t){a[e]=t}})})),Object.defineProperties(this,l)}this.$app=o,null!=o&&Object(k["j"])(o.onLaunch)&&o.onLaunch(e)}},onShow:{enumerable:!0,writable:!0,value:function(e){var t=i.current;jo.router=Object.assign({params:null===e||void 0===e?void 0:e.query},e),null!=t&&Object(k["j"])(t.componentDidShow)&&t.componentDidShow(e),l("onShow")}},onHide:{enumerable:!0,writable:!0,value:function(e){var t=i.current;null!=t&&Object(k["j"])(t.componentDidHide)&&t.componentDidHide(e),l("onHide")}},onPageNotFound:{enumerable:!0,writable:!0,value:function(e){var t=i.current;null!=t&&Object(k["j"])(t.onPageNotFound)&&t.onPageNotFound(e)}}});function l(e){var t=xo(_);if(t){var n=i.current,r=$o.getLifecycle(t,e);Array.isArray(r)&&r.forEach((function(e){return e.apply(n)}))}}return jo.app=s,jo.app}var Jo,Xo=de();function Zo(e,t){var n=function(t){function n(){var t;return Object(y["a"])(this,n),t=Object(v["a"])(this,n,arguments),t.root=e.createRef(),t.ctx=t.props.getCtx(),t}return Object(g["a"])(n,t),Object(w["a"])(n,[{key:"componentDidMount",value:function(){this.ctx.component=this;var e=this.root.current;e.ctx=this.ctx,e.performUpdate(!0)}},{key:"render",value:function(){return e.createElement("root",{ref:this.root},this.props.renderComponent(this.ctx))}}]),n}(e.Component),r=function(t){function r(){var e;return Object(y["a"])(this,r),e=Object(v["a"])(this,r,arguments),e.state={components:[]},e}return Object(g["a"])(r,t),Object(w["a"])(r,[{key:"componentDidMount",value:function(){jo.app=this}},{key:"mount",value:function(t,r,o){var i=Ho(e,t),a=function(e){return e&&Ao(e,r)},c=i?{ref:a}:{forwardedRef:a,reactReduxForwardedRef:a},u={compId:r,element:e.createElement(n,{key:r,getCtx:o,renderComponent:function(n){return e.createElement(t,Object.assign(Object.assign({},(n.data||(n.data={})).props),c))}})};this.setState({components:[].concat(Object(l["a"])(this.state.components),[u])})}},{key:"unmount",value:function(e){var t=this.state.components,n=t.findIndex((function(t){return t.compId===e})),r=[].concat(Object(l["a"])(t.slice(0,n)),Object(l["a"])(t.slice(n+1)));this.setState({components:r})}},{key:"render",value:function(){var e=this.state.components;return e.map((function(e){var t=e.element;return t}))}}]),r}(e.Component);Ko();var o=lo.getElementById("app");t.render(e.createElement(r,{}),o)}function ei(e,t,n,r){zo=t,Wo=n,Ko();var o={properties:{props:{type:null,value:null,observer:function(e,t){t&&this.component.forceUpdate()}}},created:function(){jo.app||Zo(zo,Wo)},attached:function(){var t=this;i(),this.compId=Xo(),this.config=r,jo.app.mount(e,this.compId,(function(){return t}))},ready:function(){No(this.compId,"onReady")},detached:function(){jo.app.unmount(this.compId)},pageLifetimes:{show:function(){No(this.compId,"onShow")},hide:function(){No(this.compId,"onHide")}},methods:{eh:oo}};function i(){var e=getCurrentPages(),t=e[e.length-1];if(jo.page!==t){jo.page=t;var n=t.route||t.__route__,r={params:t.options||{},path:Io(n),onReady:"",onHide:"",onShow:""};jo.router=r,t.options||Object.defineProperty(t,"options",{enumerable:!0,configurable:!0,get:function(){return this._optionsValue},set:function(e){r.params=e,this._optionsValue=e}})}}return o}function ti(e,t){return function(n){var r=e.extend({props:{tid:String},mixins:[n,{created:function(){Ao(this,t)}}]}),o={render:function(e){return e(io?"div":"root",{attrs:{id:t,class:io?"taro_page":""}},[e(r,{props:{tid:t}})])}};return o}}function ni(){var e=eo.get(C.Hooks),t=function(e,t){var n=e.props;if(!n.hasOwnProperty(t)||Object(k["i"])(n[t]))return e.setAttribute(t,!1),!0},n=function(e,t){return e.$options[t]};e.onRemoveAttribute=t,e.getLifecycle=n}function ri(e,t,n){Jo=t,Object(k["f"])(!!Jo,"\u6784\u5efa Vue \u9879\u76ee\u8bf7\u628a process.env.FRAMEWORK \u8bbe\u7f6e\u4e3a 'vue'"),ni(),Jo.config.getTagNamespace=k["q"];var r,o=[],i=[],a=new Jo({render:function(t){while(i.length>0){var n=i.pop();o.push(n(t))}return t(e,{ref:"app"},o.slice())},methods:{mount:function(e,t,n){i.push((function(n){return n(e,{key:t})})),this.updateSync(n)},updateSync:function(e){this._update(this._render(),!1),this.$children.forEach((function(e){return e._update(e._render(),!1)})),e()},unmount:function(e,t){for(var n=0;n<o.length;n++){var r=o[n];if(r.key===e){o.splice(n,1);break}}this.updateSync(t)}}});io||a.$mount(lo.getElementById("app"));var c=Object.create({mount:function(e,t,n){var r=ti(Jo,t)(e);a.mount(r,t,n)},unmount:function(e,t){a.unmount(e,t)}},{config:{writable:!0,enumerable:!0,configurable:!0,value:n},onLaunch:{writable:!0,enumerable:!0,value:function(e){jo.router=Object.assign({params:null===e||void 0===e?void 0:e.query},e),io&&a.$mount(lo.getElementById((null===n||void 0===n?void 0:n.appId)||"app")),r=a.$refs.app,null!=r&&Object(k["j"])(r.$options.onLaunch)&&r.$options.onLaunch.call(r,e)}},onShow:{writable:!0,enumerable:!0,value:function(e){jo.router=Object.assign({params:null===e||void 0===e?void 0:e.query},e),null!=r&&Object(k["j"])(r.$options.onShow)&&r.$options.onShow.call(r,e)}},onHide:{writable:!0,enumerable:!0,value:function(e){null!=r&&Object(k["j"])(r.$options.onHide)&&r.$options.onHide.call(r,e)}}});return jo.app=c,jo.app}function oi(e,t){return function(n){var r,o={props:{tid:String},created:function(){Ao(this,t)}};if(Object(k["h"])(n.mixins)){var i=n.mixins,a=i.length-1;(null===(r=i[a].props)||void 0===r?void 0:r.tid)?n.mixins[a]=o:n.mixins.push(o)}else n.mixins=[o];return e(io?"div":"root",{key:t,id:t,class:io?"taro_page":""},[e(Object.assign({},n),{tid:t})])}}function ii(){var e=eo.get(C.Hooks),t=function(e,t){return e.$options[t]},n=function(e){e.type=e.type.replace(/-/g,"")};e.getLifecycle=t,e.modifyMpEvent=n}function ai(e,t,n){var r,o=[];Object(k["f"])(!Object(k["j"])(e._component),"\u5165\u53e3\u7ec4\u4ef6\u4e0d\u652f\u6301\u4f7f\u7528\u51fd\u6570\u5f0f\u7ec4\u4ef6"),ii(),e._component.render=function(){return o.slice()},io||(r=e.mount("#app"));var i=Object.create({mount:function(e,n,r){var i=oi(t,n)(e);o.push(i),this.updateAppInstance(r)},unmount:function(e,t){o=o.filter((function(t){return t.key!==e})),this.updateAppInstance(t)},updateAppInstance:function(e){r.$forceUpdate(),r.$nextTick(e)}},{config:{writable:!0,enumerable:!0,configurable:!0,value:n},onLaunch:{writable:!0,enumerable:!0,value:function(t){var o,i=this;if(jo.router=Object.assign({params:null===t||void 0===t?void 0:t.query},t),io&&(r=e.mount("#"+n.appId||!1)),e["taroGlobalData"]){var a=e["taroGlobalData"],c=Object.keys(a),u=Object.getOwnPropertyDescriptors(a);c.forEach((function(e){Object.defineProperty(i,e,{configurable:!0,enumerable:!0,get:function(){return a[e]},set:function(t){a[e]=t}})})),Object.defineProperties(this,u)}var s=null===(o=null===r||void 0===r?void 0:r.$options)||void 0===o?void 0:o.onLaunch;Object(k["j"])(s)&&s.call(r,t)}},onShow:{writable:!0,enumerable:!0,value:function(e){var t;jo.router=Object.assign({params:null===e||void 0===e?void 0:e.query},e);var n=null===(t=null===r||void 0===r?void 0:r.$options)||void 0===t?void 0:t.onShow;Object(k["j"])(n)&&n.call(r,e)}},onHide:{writable:!0,enumerable:!0,value:function(e){var t,n=null===(t=null===r||void 0===r?void 0:r.$options)||void 0===t?void 0:t.onHide;Object(k["j"])(n)&&n.call(r,e)}}});return jo.app=i,jo.app}var ci=function(e){return function(t){var n=zo.useContext(Vo)||_,r=zo.useRef(t);r.current!==t&&(r.current=t),zo.useLayoutEffect((function(){var t=xo(n),o=!1;null==t&&(o=!0,t=Object.create(null)),t=t;var i=function(){return r.current.apply(r,arguments)};return Object(k["j"])(t[e])?t[e]=[t[e],i]:t[e]=[].concat(Object(l["a"])(t[e]||[]),[i]),o&&Ao(t,n),function(){var t=xo(n),r=t[e];r===i?t[e]=void 0:Object(k["h"])(r)&&(t[e]=r.filter((function(e){return e!==i})))}}),[])}},ui=ci("componentDidShow"),si=ci("componentDidHide"),li=ci("onPullDownRefresh"),di=ci("onReachBottom"),fi=ci("onPageScroll"),hi=ci("onResize"),pi=ci("onShareAppMessage"),vi=ci("onTabItemTap"),mi=ci("onTitleClick"),gi=ci("onOptionMenuClick"),bi=ci("onPullIntercept"),yi=ci("onShareTimeline"),wi=ci("onAddToFavorites"),Oi=ci("onReady"),ji=function(){var e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return e?jo.router:zo.useMemo((function(){return jo.router}),[])},ki=function(){};function Si(e){return null==e?"":"/"===e.charAt(0)?e.slice(1):e}var Ti=function(e,t){var n,r,o,i=jo.router,a=function(){setTimeout((function(){t?e.call(t):e()}),1)};if(null!==i){var c=null,u=Fo(Si(i.path),i.params);c=lo.getElementById(u),(null===c||void 0===c?void 0:c.pendingUpdate)?io?null!==(o=null===(r=null===(n=c.firstChild)||void 0===n?void 0:n["componentOnReady"])||void 0===r?void 0:r.call(n).then((function(){a()})))&&void 0!==o||a():c.enqueueUpdateCallback(e,t):a()}else a()}}.call(this,n(26),n(29),n(9)["document"],n(9)["window"],n(9)["requestAnimationFrame"],n(9)["cancelAnimationFrame"])}}]);